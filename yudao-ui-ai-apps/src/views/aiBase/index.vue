<template>
  <div
    class="bg-[#f3f8fa] p-2 overflow-auto"
    style="height: calc(var(--vh) * 100 - 160px)"
  >
    <div class="pc_container" style="display: flex">
      <template v-if="isNormal">
        <div class="bg-[#fff] m_bg" style="border-radius: 10px; width: 40%">
          <div class="flex" v-for="(item, index) in userInputForm" :key="index">
            <template v-for="(value, key) in item" :key="value['variable']">
              <InputField
                :type="key"
                @payShowStatus= "payShowChange"
                :label="$t(value?.label)"
                :value="inputs[value?.variable]"
                :required="value?.required"
                :placeholder="`${value?.label}`"
                :max_length="value?.max_length"
                :options="value?.options"
                :fileVerify="value?.allowed_file_types"
                :currentItem="props.currentItem"
                @update:value="
                  (newValue) => (inputs[value?.variable] = newValue)
                "
                ref="childRef"
              />
            </template>
          </div>
          <div
            class="p-3"
            style="display: flex; justify-content: space-between"
          >
            <el-button @click="reset">{{ $t("tool.clear") }}</el-button>
            <el-button @click="onExec" :loading="loadingBtn" type="primary">{{
              $t("tool.execute")
            }}</el-button>
          </div>
        </div>
        <div class="pc_right bg-[#fff]">
          <div id="typing-area">
            <div
              class="decContaniner nop bg-[#fff]"
              v-if="decListNew.length > 0 || displayedText || isShow"
            >
              <el-collapse v-model="activeNames">
                <el-collapse-item name="1">
                  <template #title>
                    <div class="img_box" v-if="loadingDecImg">
                      <img :src="loadingImg" alt="loading" />
                    </div>
                    <div class="icon" v-else>
                      <el-icon><SuccessFilled /></el-icon>
                    </div>
                    {{ $t("tool.execution_progress") }}
                  </template>
                  <div
                    v-for="(item, index) in decListNew"
                    :key="index"
                    class="process"
                  >
                    <div class="process_text label_width">{{ item.title }}</div>
                    &numsp;&numsp;&numsp;<span
                      style="color: #36b15e"
                      :class="!item.status ? 'loading-text' : ''"
                      >{{
                        item.status ? $t("tool.completed") : $t("tool.loading")
                      }}</span
                    >
                  </div>
                </el-collapse-item>
                <div><div v-if="displayedText" class="process"></div></div>
                <el-collapse-item name="2" v-if="displayedText">
                  <template #title>
                    <div class="img_box" v-if="processLoadingPng">
                      <img :src="loadingImg" alt="loading" />
                    </div>
                    <div class="icon" v-else>
                      <el-icon><SuccessFilled /></el-icon>
                    </div>
                    {{ $t("tool.reasoning_process") }}
                  </template>
                  <div class="process">
                    <div class="process_text">{{ displayedText }}</div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
            <v-md-preview v-if="answer&&!isSubscribe" :text="answer" id="previewMd" />
            <div  v-if="isSubscribe">{{answer}} <el-button type="text" @click="handleOrder">{{ $t("market.subscribe")}}</el-button> </div>
            <div>
              <img
                v-if="loadingPng"
                :src="loadingImg"
                alt="loading"
                class="spinner"
              /><span
                v-if="loadingPng"
                text
                type="primary"
                class="stop_btn"
                @click="stop"
                >{{ $t("tool.stopGeneration") }}</span
              >
              <img
                v-if="isCopy"
                @click="handlePaste"
                :src="copy"
                alt=""
                style="width: 20px"
                class="copy"
              />
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="mobile_container">
      <van-tabs :active="activeName" shrink line-width="20"  @click-tab="onClickTab">
        <van-tab title="输入" name="a">
          <div class="flex" v-for="(item, index) in userInputForm" :key="index">
            <template v-for="(value, key) in item" :key="value['variable']">
              <InputField
                :type="key"
                :label="$t(value?.label)"
                :value="inputs[value?.variable]"
                :required="value?.required"
                :placeholder="`${value?.label}`"
                :max_length="value?.max_length"
                :options="value?.options"
                :fileVerify="value?.allowed_file_types"
                @update:value="
                  (newValue) => (inputs[value?.variable] = newValue)
                "
                ref="childRefs"
              />
            </template>
          </div>
          <div
            class="p-3"
            style="display: flex; justify-content: space-between"
          >
            <el-button @click="reset">Clear</el-button>
            <el-button
              @click="onExec('mobile')"
              :loading="loadingBtn"
              type="primary"
              >Execute</el-button
            >
          </div>
        </van-tab>
        <van-tab title="结果" name="b" :disabled="isdisabled">
          <div class="mobile_right">
            <div id="typing-area">
              <div
                class="decContaniner nop bg-[#fff]"
                v-if="decList.length > 0 || displayedText"
              >
                <el-collapse v-model="activeNames">
                  <el-collapse-item name="1">
                    <template #title>
                      <div class="img_box" v-if="loadingDecImg">
                        <img :src="loadingImg" alt="loading" />
                      </div>
                      <div class="icon" v-else>
                        <el-icon><SuccessFilled /></el-icon>
                      </div>
                      {{ $t("tool.execution_progress") }}
                    </template>
                    <div
                      v-for="(item, index) in decList"
                      :key="index"
                      class="process"
                    >
                      <div class="process_text label_width">
                        {{ item.title }}
                      </div>
                      &numsp;&numsp;&numsp;<span
                        style="color: #36b15e"
                        :class="!item.status ? 'loading-text' : ''"
                        >{{
                          item.status
                            ? $t("tool.completed")
                            : $t("tool.loading")
                        }}</span
                      >
                    </div>
                  </el-collapse-item>
                  <div><div class="process"></div></div>
                  <el-collapse-item
                    title="推导过程"
                    name="2"
                    v-if="displayedText"
                  >
                    <template #title>
                      <div class="img_box" v-if="processLoadingPng">
                        <img :src="loadingImg" alt="loading" />
                      </div>
                      <div class="icon" v-else>
                        <el-icon><SuccessFilled /></el-icon>
                      </div>
                      {{ $t("tool.reasoning_process") }}
                    </template>
                    <div class="process">
                      <div class="process_text">{{ displayedText }}</div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <v-md-preview v-if="answer&&!isSubscribe" :text="answer" id="previewMd" />
              <div  v-if="isSubscribe">{{answer}} <el-button type="text" @click="handleOrder">{{ $t("market.subscribe")}}</el-button> </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    <el-dialog
      v-model="payShow"
      v-if="payShow"
      class="payPC"
      :show-close="false"
    >
      <pay
        :userInfo="userInfo"
        :appTypes="appTypes"
        :currentItem="props.currentItem"
        @toAgreement="toAgreement"
        @close="close"
        @subscribe="subscribe"
      />
    </el-dialog>
    <van-popup
      v-model:show="payShow"
      v-if="payShow"
      round
      closeable
      class="payMobile"
      position="bottom"
      :style="{ height: '90%' }"
    >
      <payMobile
        :userInfo="userInfo"
        :appTypes="appTypes"
        :currentItem="props.currentItem"
        @toAgreement="toAgreement"
        @close="close"
      />
    </van-popup>
  </div>
</template>

<script setup>
import cookie from "js-cookie";
import { useI18n } from "vue-i18n";
import { SuccessFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getParameters, mainLogin, getAppPrompt, stopChat, getAppByUuid, createSubscription } from "@/api/base";
import { getAssetsFile } from "@/utils/index";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import InputField from "./components/InputField.vue";
import pay from "@/components/pay/index.vue";
import payMobile from "@/components/payMobile/index.vue";
import { Popup as VanPopup } from "vant";
import { injectCustomCode } from "@/utils/index";
import { Tab as VanTab, Tabs as VanTabs } from "vant";
import { useHead } from '@vueuse/head'
import titles from "@/langs/lang.js";
import axios from "axios";
import {
  defaultLanguageName,
  defaultLanguage,
  getDefaultLanguageCode,
} from "@/common/commonJs";
import { watch } from "vue";
const props = defineProps({
  currentItem: {
    type: Object,
    default: () => {},
  },
});
//数据响应式
const loadingImg = getAssetsFile("loading.png");
const copy = getAssetsFile("copy.png");
const inputs = reactive({}); // 用户输入的表单数据
const route = useRoute();
const keyInfo = {}; // 保存dify配置的各字段的label
const userInputForm = ref([]); // dify配置的字段信息
const userInfo = ref(
  cookie.get("userInfo") ? JSON.parse(cookie.get("userInfo")) : {}
); // 用户信息
const nodeInfo = ref(null); // 实例信息
// const dec = ref(null);
// 获取 URL 中的参数
const { t, locale } = useI18n();
const router = useRouter();
const longText = ref(false);
let activeName = ref("a");
let isdisabled = ref(true);
let answer = ref("");
const task_id = ref("");
const childRef = ref(null);
const childRefs = ref(null);
const activeNames = ref(["1", "2"]);
const loadingDecImg = ref(false);
const isShow = ref(false);
const payShow = ref(false);
let interval;
const dAppUuid = ref('');
const title = ref('');
// 终止消息
const stop = async () => {
  await stopChat({
    appId: dAppUuid.value,
    user: userInfo.value.userName,
    mode: nodeInfo.value?.mode,
    task_id: task_id.value,
  });
  setTimeout(() => {
    ctrl.abort();
    isWorkflowFinished = true;
    typingQueue.value = [];
    loadingDecImg.value = false;
    if (decList.value.length) {
      decList.value.forEach((element) => {
        element.status = true;
      });
    }
    processTypingQueue();
  }, 0);
};
const close = () => {
  payShow.value = false;
};
// 支付
const subscribe = async (item, appUuid) => {
  let language = getDefaultLanguageCode();
  if (!userInfo.value?.userId) {
    // country='xxxx' // 模拟国外
    // 判断是否是国内用户访问
    if (!language || language == "zh-CN") {
      window.addLoginDom();
    } else {
      // 跳转到获取授权页
      router.push(isUp ? "/login" : "/login");
    }
  } else {
    const subscriptionParams = {
      appUuid: appUuid,
      priceId: item.priceId,
      monthNum: item.monthNum,
    };
    let res = await createSubscription(subscriptionParams);
    if (res) {
      ElMessage({
        type: "success",
        message: t("tool.sS"),
      });
      setTimeout(() => {
        location.href = res;
      }, 1000);
    }
  }
};
// 页面初始化
onMounted(async () => {
  const windowVH = window.innerHeight / 100;
  document.documentElement.style.setProperty("--vh", `${windowVH}px`);
  let arr = [
    "zh-CN",
    "en",
    "es",
    "ja",
    "zh-TW",
    "vi",
    "ko",
    "pt",
    "ar",
    "id",
    "ms",
  ];
  if (arr.includes(route.params?.lang)) {
    window.localStorage.setItem("ai_apps_lang", route.params?.lang);
  } else {
    window.localStorage.setItem(
      "ai_apps_lang",
      localStorage.getItem("ai_apps_lang")
        ? localStorage.getItem("ai_apps_lang")
        : navigator.browserLanguage || navigator.language
    );
  }
  // 设置 i18n 的语言
  locale.value = defaultLanguage();
  insert();
  const appLang= await getAppByUuid(route.params.appUuid);
  dAppUuid.value = appLang.dAppUuid;
  title.value =  appLang.appName;
  if(cookie.get("userInfo")){
    await loginMain();
  }
  getInputForm();
  injectCustomCode(appLang.customCss, appLang.customJs)
 
});

const fullTitle = computed(() => {
  return `<${title.value}>${
          titles[localStorage.getItem("ai_apps_lang")]
        }`;
});
useHead({
  title: fullTitle
});

// 切换
const onClickTab = (e)=>{
  activeName.value = e.name
}
// 处理订阅事件
const handleOrder = async () => {
  // router.push('/payType?appUuid='+item.appUuid)
  payShow.value = true;
};
// 获取实例参数
const getInputForm = () => {
  if (!dAppUuid.value) return;
  getParameters({
    appId: dAppUuid.value,
    user: userInfo.value.userName,
  }).then((res) => {
    if (res?.user_input_form) {
      userInputForm.value = res.user_input_form;
      res.user_input_form.forEach((item) => {
        const key = Object.keys(item)[0];
        const variable = item[key].variable;
        keyInfo[variable] = {
          label: item[key].label,
        };
        inputs[variable] = "";
      });
    }
  });
};

// 是否获取到dify实例字段信息
const isNormal = computed(() => {
  return !!userInputForm.value.length;
});

const requiredKeyArr = computed(() => {
  return userInputForm.value
    .filter((item) => {
      const key = Object.keys(item)[0];
      return item[key].required === true;
    })
    .map((item) => {
      const key = Object.keys(item)[0];
      return item[key].variable;
    });
});

// 获取dify实例信息
const getTemplateInfo = () => {
  if (!dAppUuid.value) return;
  getAppPrompt({
    appId: dAppUuid.value,
    user: userInfo.value.userName,
  }).then((res) => {
    nodeInfo.value = { ...res };
  });
};

const loading = ref(false); // 答案区域记载中
const loadingBtn = ref(false); // 左边区域执行按钮loading
const loadingPng = ref(false); // 答案区域加载打印中
const isCopy = ref(false); // copy按钮
// 打点
const insert = async () => {
  let data = [
    {
      userRandomId:
        Math.random().toString(36).substr(2, 9) + Date.now().toString(36),
      title: document.title,
      refer: "",
      userAgen: navigator.userAgent,
      time: new Date().getTime(),
      url: location.href,
      actionValue: "",
      userAction: "Exposure",
      actionCode: null,
      userId: userInfo.value?.userId,
      userToken: "",
      channel: "MedSci_xAI",
      appId: dAppUuid.value,
      userUuid: userInfo.value?.openid,
    },
  ];
  // 使用 axios 发送 POST 请求
  await axios.post(
    "https://app-trace.medsci.cn/api/points/v1/user-action-batch",
    data
  );
};
const payShowChange = (value) => {
  payShow.value = value; 
}
// 判断是否填写问题
const hasUndefinedValue = (inputs) => {
  return Object.values(inputs).some((value) => value);
};
// 发起AI会话
const onExec =async () => {
  if (!cookie.get("userInfo")) {
    localStorage.removeItem("yudaoToken");
    localStorage.removeItem("hasuraToken");
    const language = getDefaultLanguageCode();
    if (!language || language == "zh-CN") {
      window.addLoginDom();
    } else {
      // 跳转到获取授权页
      router.push("/login");
    }
    return;
  }
  await loginMain();
  if(!props.currentItem?.appUser?.status){
    payShow.value = true;
    return false;
  }
  // 校验必填项
  if (requiredKeyArr.value.length == 0 && !hasUndefinedValue(inputs)) {
    ElMessage({
      message: `${t("tool.enterquestion")}`,
      type: "error",
    });
    return;
  }
  for (let key in inputs) {
    if (requiredKeyArr.value.includes(key) && !inputs[key]) {
      ElMessage({
        message: `${keyInfo[key].label}${t("tool.requiredfield")}`,
        type: "error",
      });
      return;
    }
  }
  if (nodeInfo.value?.mode) {
    // 需要判断是chat还是workflow
    if (["advanced-chat", "chat"].includes(nodeInfo.value?.mode)) {
      ElMessage({
        type: "success",
        message: t("tool.planning"),
      });
    } else if (nodeInfo.value.mode == "completion") {
      isdisabled.value = false;
      setTimeout(() => {
        activeName.value = "b";
      }, 1000);
      submitCompletionflow();
    } else {
      isdisabled.value = false;
      setTimeout(() => {
        activeName.value = "b";
      }, 1000);
      submitWorkflow();
    }
  }
};

// const submitChat = () => {
//   execChat({
//     appId: nodeInfo.value?.meta?.dify_app_uuid,
//     user: userInfo.value.userName,
//     inputs: inputs,
//     files: [],
//     response_mode: "streaming"
//   }).then((res) => {
//     answer.value = res.data;
//   });
// }

const typingQueue = ref([]); // 打字机效果的队列
var decList = ref([]); //每个节点的title和状态
var decListNew = ref([]); //每个节点的title和状态
const process = ref("");
const index = ref(0);
const displayedText = ref("");
const processLoadingPng = ref(false);
const isSubscribe = ref(false);
// 当前正在处理的元素的索引
let currentIndex = ref(0);
const isResult = ref(false);
const isTyping = ref(false); // 是否正在打字中
let isWorkflowFinished = false; // 工作流是否执行完毕
let isTypingFinished = false; // 打字机效果是否显示完毕
let ctrl;
watch(
  decList,
  () => {
    // 如果原始数组长度增加了，开始处理下一个元素
    processNextElement();
  },
  {
    deep: true,
  }
);
// 处理下一个元素的函数
const processNextElement = () => {
  if (currentIndex.value < decList.value.length) {
    // 将当前元素添加到新数组
    decListNew.value.push(decList.value[currentIndex.value]);
    // 索引自增
    currentIndex.value++;
    // 等待一秒钟后处理下一个元素
    setTimeout(processNextElement, 1000);
  }
};
const typeWriter = () => {
  //推理过程定时器
  if (index.value < process.value.length) {
    processLoadingPng.value = true;
    displayedText.value += process.value.charAt(index.value);
    index.value++;
    setTimeout(typeWriter, 20);
  } else {
    isTyping.value = false;
    processLoadingPng.value = false;
    loadingPng.value = true;
    processTypingQueue();
  }
};

// 工作流
const submitWorkflow = async () => {
  isShow.value = true;
  loadingDecImg.value = true;
  answer.value = "";
  decList.value = [];
  decListNew.value = [];
  currentIndex.value = 0;
  displayedText.value = "";
  process.value = "";
  typingQueue.value = [];
  isResult.value = false;
  isWorkflowFinished = false;
  index.value = 0;
  ctrl = new AbortController();
  try {
    let url = `${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;
    if (import.meta.env.MODE === "dev") {
      url = `/dev-api/ai-base/chat/workflows/run?locale=zh`;
    }
    loading.value = true;
    loadingBtn.value = true;
    isCopy.value = false;
    await fetchEventSource(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${localStorage.getItem("yudaoToken") || null}`,
      },
      body: JSON.stringify({
        appId: dAppUuid.value,
        user: userInfo.value.userName,
        inputs: {
          ...inputs,
          outputLanguage: inputs.outputLanguage
            ? inputs.outputLanguage
            : defaultLanguageName() == "中文"
            ? "简体中文"
            : defaultLanguageName(),
        },
        requestId: crypto.randomUUID(),
        files: [],
        response_mode: "streaming",
        appUuid: route.params.appUuid,
      }),
      // AbortSignal
      onmessage(event) {
        if (event.data.trim()) {
          try {
            const data = JSON.parse(event.data);
            task_id.value = data.task_id;
            if (data.error) {
              throw new Error(data.error);
            }
            if (
              data?.data?.title == "智能体推理思维链" &&
              data.event === "node_finished"
            ) {
              isTyping.value = true;
              process.value = JSON.parse(data?.data?.outputs?.text)?.text;
              typeWriter();
            }
            if (
              data.event === "node_started" &&
              !isResult.value &&
              data?.data?.title != "开始"
            ) {
              decList.value.push({
                node_id: data?.data?.node_id,
                title: data?.data?.title,
                status: false,
              });
              // setTimeout(() => {
              //   dec.value.scrollTop = dec.value.scrollHeight;
              // }, 0);
            }
            if (data.event === "error") {
              if(data.code == 5047){
                isSubscribe.value = true;
              }
              isResult.value = true;
              loadingDecImg.value = false;
              isWorkflowFinished = true; // workflow_finished 事件执行完毕
              loadingBtn.value = false;
              // ElMessage.error(t("tool.accessbusy"));
              answer.value = data?.message
            }
            if (data.event === "node_finished") {
              decList.value.forEach((element) => {
                if (element.node_id == data?.data?.node_id) {
                  element.status = true;
                }
              });
            }
            if (data.event === "text_chunk") {
              longText.value = true;
              typingQueue.value.push(data?.data?.text);
              if (!isTyping.value) {
                processTypingQueue();
              }
            }
            if (data.event === "workflow_started") {
              loading.value = false;
            }
            if (data.event === "workflow_finished") {
              isWorkflowFinished = true; // workflow_finished 事件执行完毕
              isResult.value = true;
              loadingDecImg.value = false;
              loadingBtn.value = false;
              loadingPng.value = false;
              if (!longText.value) {
                typingQueue.value.push(data?.data?.outputs?.text);
                if (!isTyping.value) {
                  processTypingQueue();
                }
              }
            }
          } catch (error) {
            handleError(error);
          }
        }
      },
      onerror(error) {
        handleError(error);
      },
      signal: ctrl.signal,
      openWhenHidden: true,
    });
  } catch (error) {
    handleError(error);
  }
};
const submitCompletionflow = async () => {
  answer.value = "";
  typingQueue.value = [];
  isTyping.value = false;
  isWorkflowFinished = false;
  ctrl = new AbortController();
  try {
    let url = `${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;
    if (import.meta.env.MODE === "dev") {
      url = `/dev-api/ai-base/chat/completion-messages?locale=zh`;
    }
    loading.value = true;
    loadingBtn.value = true;
    isCopy.value = false;
    await fetchEventSource(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${localStorage.getItem("yudaoToken") || null}`,
      },
      body: JSON.stringify({
        appId: dAppUuid.value,
        user: userInfo.value.userName,
        inputs: {
          ...inputs,
          outputLanguage: defaultLanguageName(),
        },
        files: [],
        response_mode: "streaming",
        appUuid: route.params.appUuid,
        requestId: crypto.randomUUID(),
      }),
      onmessage(event) {
        loading.value = false;
        loadingPng.value = true;
        if (event.data.trim()) {
          try {
            const data = JSON.parse(event.data);
            task_id.value = data.task_id;
            if (data.error) {
              throw new Error(data.error);
            }
            if (data.event === "error") {
              if(data.code == 5047){
                isSubscribe.value = true;
              }
              isWorkflowFinished = true; // workflow_finished 事件执行完毕
              answer.value = data?.message
              loadingPng.value = false;
              loadingBtn.value = false;
            }
            if (data.event === "message") {
              typingQueue.value.push(data?.answer);
              if (!isTyping.value) {
                processTypingQueue();
              }
            }
            if (data.event === "message_end") {
              isWorkflowFinished = true; // workflow_finished 事件执行完毕
              loadingBtn.value = false;
              loadingPng.value = false;
            }
          } catch (error) {
            handleError(error);
          }
        }
      },
      onerror(error) {
        handleError(error);
      },
      signal: ctrl.signal,
      openWhenHidden: true,
    });
  } catch (error) {
    handleError(error);
  }
};
//

const processTypingQueue = () => {
  if(typingQueue.value.length === 0){
        isTyping.value = false;
        isTypingFinished = true; // 所有字打字机效果显示完毕
        checkCopyButton();
        return;
  }
  isTyping.value = true;
  const text = typingQueue.value.shift();
    simulateTyping(text).then(() => {
      processTypingQueue();
    });
};
const checkCopyButton = () => {
  if (isTypingFinished && isWorkflowFinished) {
    loadingBtn.value = false;
    loadingPng.value = false;
    isCopy.value = true; // 满足条件，设置 isCopy 为 true
  }
};

const simulateTyping = (text) => {
  return new Promise((resolve) => {
    let index = 0;
    interval = setInterval(() => {
      if (index < text?.length) {
        answer.value += text[index++];
        const pc_right = document.getElementsByClassName("pc_right");
        pc_right[0].scrollTop = pc_right[0].scrollHeight;
        const mobile_right = document.getElementsByClassName("mobile_right");
        mobile_right[0].scrollTop = mobile_right[0].scrollHeight;
      } else {
        clearInterval(interval);
        resolve();
      }
    }, 0);
  });
};
const handleError = () => {
  setTimeout(() => {
    ctrl.abort();
  }, 0);
  loadingDecImg.value = false;
  loading.value = false;
  loadingPng.value = false;
  loadingBtn.value = false;
  isTyping.value = false;
  ElMessage.error(t("tool.accessbusy"));
  answer.value = t("tool.accessbusy");
};

const handlePaste = async () => {
  try {
    await navigator.clipboard.writeText(answer.value);
    ElMessage({
      type: "success",
      message: t("tool.copysuccess"),
    });
  } catch (err) {
    ElMessage(err);
  }
};
const reset = () => {
  for (let key in inputs) {
    inputs[key] = "";
  }
  childRef.value.forEach((element) => {
    element.updateMessage();
  });
  childRefs.value.forEach((element) => {
    element.updateMessage();
  });
};

// 主登录逻辑
const loginMain =async () => {
  const token = localStorage.getItem("yudaoToken");
  if (token) {
    getTemplateInfo();
    return;
  }
    try {
     await mainLogin({
        userId: userInfo.value.userId,
        userName: userInfo.value.userName,
        realName: userInfo.value.realName,
        avatar: userInfo.value.avatar,
        plaintextUserId: userInfo.value.plaintextUserId,
        mobile: userInfo.value.mobile,
        email: userInfo.value.email,
      }).then(async (res) => {
        if (res?.token && res?.htoken) {
          localStorage.setItem("yudaoToken", res.token);
          localStorage.setItem("hasuraToken", res.htoken);
          localStorage.setItem("openid", res.openid);
          localStorage.setItem("socialUserId", res.socialUserId);
          localStorage.setItem("socialType", res.socialType);
          getTemplateInfo();
        } else {
          console.error("登录失败: 未返回 token");
        }
      });
    } catch (error) {
      console.error(error, "登录失败pc");
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  padding: 0px !important;
  width: 679px;
  border-radius: 20px;

  .el-dialog__header {
    padding: 0px !important;
  }
}
:deep() {
  .el-button--text{
    padding: 0;
    line-height: 1;
    height: auto;
  }
  .van-popup__close-icon {
    top: 7px !important;
  }
  .payMobile {
    display: none;
  }
  .payPC {
    display: block;
  }
}
.tool-bar-btn:hover {
  background: #409eff;
}
.mobile_container {
  display: none;
}
.pc_container {
  display: flex !important;
  padding-bottom: 0.5rem;
  min-height: 100%;
  .pc_right {
    width: 60%;
    margin-left: 15px;
    padding: 15px;
    border-radius: 10px;
    height: calc(var(--vh) * 100 - 160px);
    overflow-y: scroll;
  }
}
:deep() {
  .payMobile {
    display: none;
  }
  .payPC {
    display: block;
  }
  button:active,
  input[type="submit"]:active {
    box-shadow: unset !important;
  }
  .el-collapse-item__header {
    border-bottom: none;
    height: 28px;
    line-height: 28px;
  }
  .el-collapse-item__wrap {
    border-bottom: none;
  }
  .el-collapse-item__content {
    padding-bottom: 0;
  }
  .el-collapse {
    border-top: unset;
    border-bottom: unset;
  }
}
:deep(.el-textarea__inner),
:deep(.el-input__wrapper) {
  background-color: rgb(249 250 251/1);
  border-radius: 10px;
}

:deep(.el-select__wrapper) {
  background-color: rgb(249 250 251/1);
}

:deep(.el-button) {
  border-radius: 10px;
}

:deep(.github-markdown-body) {
  background: #fff;
  border-radius: 10px;
  padding: 16px 10px;
}

.copy {
  cursor: pointer;
}

.spinner {
  width: 20px;
  height: 20px;
  margin: 0 auto;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

:deep(.github-markdown-body) {
  background: transparent;
  border-radius: 10px;
  padding-right: 10px;
}

:deep(.github-markdown-body) h1,
:deep(.github-markdown-body) h2,
:deep(.github-markdown-body) h3,
:deep(.github-markdown-body) h4 {
  font-size: 16px;
  border-bottom: 0;
}
:deep(.el-upload__input) {
  display: none !important;
}
:deep(.el-upload) {
  width: 100%;
  button {
    width: 100%;
  }
}
::-webkit-scrollbar {
  width: 7px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #b1b3b8;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b1b3b8;
}
.stop_btn {
  color: #409eff;
  margin-left: 10px;
}
.stop_btn:hover {
  cursor: pointer;
}
.nop {
  height: auto !important;
  .img_box {
    width: 20px;
    margin-left: -10px;
    margin-right: 5px;
    position: relative;
  }
  .icon {
    margin-left: -9px;
    display: flex;
    margin-right: 5px;
    color: #409eff;
    font-size: 18px;
  }
  img {
    width: 20px;
    animation: spin 1s linear infinite;
  }
}
.decContaniner {
  height: 100px; /* 固定高度 */
  width: 100%;
  overflow-y: auto; /* 当内容超出div高度时，显示滚动条 */
  border: 2px solid #e2f0f6;
  padding: 10px 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fff;
}
.process {
  display: flex;
  .process_text {
    padding: 0 10px;
    color: #999999;
  }
  .label_width {
    min-width: 200px;
  }
}
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.loading-text {
  font-size: 1.2em;
  color: #2f92ee !important;
}

.loading-text::after {
  content: "...";
  animation: blink 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.process:before {
  content: "";
  width: 1px;
  background: #cccfd3;
  border-radius: 13px;
  transition: 0.3s backgroundcubic-bezier(0.4, 0, 0.2, 1);
  padding-bottom: 14px;
}
:deep(.v-md-editor-preview li){
    list-style: decimal;
  }
@media screen and (max-width: 768px) {
  .mobile_container {
    display: block;
  }
  .bg-\[\#f3f8fa\]{
    overflow: hidden;
  }
  .mobile_right {
    height: calc(var(--vh) * 100 - 173px - 3rem);
    overflow-y: auto;
  }
  :deep() {
    .el-overlay {
      display: none;
    }
    .payPC {
      display: none !important;
    }
    .payMobile {
      display: block;
    }
    .van-tabs__content {
      overflow: scroll;
      height: calc(var(--vh)* 100 - 210px);
    }
    .el-input__wrapper {
      border-radius: 4px;
    }
    .el-upload button {
      border-radius: 4px;
    }
    .p-6 {
      padding: 1.5rem 0;
      .van-tabs__nav {
        padding-left: unset;
      }
    }
    .github-markdown-body {
      font-size: 14px;
      padding: 10px 0;
    }
    .van-tab {
      padding-left: 0 !important ;
    }
    .van-tabs__nav--line {
      padding-left: 0 !important;
    }
    .van-tabs__line {
      left: -6px !important;
      bottom: 20px;
    }
    .v-md-editor-preview li {
      list-style: decimal;
    }
  }

  .bg-\[\#f3f8fa\] {
    background: #fff;
  }
  .overflow-auto {
    .pc_container {
      display: none !important;
      height: calc(var(--vh) * 100 - 190px);
      min-height: unset;
    }
    .m-bg {
      height: calc(var(--vh) * 100 - 190px);
    }
    .p-2 {
      padding: 0;
    }
    :deep() {
    }
  }
}
</style>
