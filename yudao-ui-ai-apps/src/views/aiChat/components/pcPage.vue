<template>
  <div :class="currentTheme">
    <customerService v-if="isZH" />
    <el-dialog
      v-model="payShow"
      v-if="payShow"
      class="payPC"
      :show-close="false"
    >
      <pay
        :userInfo="userInfo"
        :appTypes="appTypes"
        :currentItem="currentItem"
        @toAgreement="toAgreement"
        @close="close"
        @subscribe="subscribe"
      />
    </el-dialog>
    <div
      :style="[
        { width: !isLeftNav || $route.query?.hideHistory ? '0px' : '264px' },
      ]"
      class="left"
      v-if="!$route.query?.hideHistory"
    >
      <div class="chatBar" v-if="isLeftNav">
        <div style="padding: 0 50px; margin-top: 30px">
          <a :href="hrefUrl" class="hover:text-[#5298FF]"
            ><img
              :src="
                currentTheme == 'dark-theme'
                  ? 'https://img.medsci.cn/202502/006b10f5a9bf4de6ad9a7c82d83c8607-lQLFMQuVe7dR.png'
                  : 'https://img.medsci.cn/202502/249ed78c2d9246619edb188e0f086c95-BW5tMjX5qolD.png'
              "
              alt=""
          /></a>
        </div>
        <div class="startBtn" @click="newTalk">
          <span
            v-if="isLeftNav"
            class="beginTalk"
            style="
              opacity: 1;
              color: #ffffffff;
              font-size: 16px;
              font-weight: 500;
              font-family: 'PingFang SC';
              text-align: center;
              margin-left: -34px;
            "
            :style="$i18n.locale == 'fr' ? { fontSize: '10.5px' } : {}"
            >{{ $t("faq.newChat") }}</span
          >
        </div>

        <!-- 左侧历史记录列表 -->
        <div class="logList" @click="hidePage()">
          <div v-if="logList.length !== 0">
            <div
              v-for="(item, index) in logList"
              :key="index"
              :class="item.isActive ? 'isActive logBox' : 'logBox'"
              @click="getLog(item.id, item.name, index)"
            >
              <div class="chatTime" v-if="item.createdTime != ''">
                {{ item.createdTime }}
              </div>
              <div
                class="chatItem"
                @mouseover="onMouseover(index)"
                @mouseout="onMouseout(index)"
              >
                <span class="middle-dot">·</span>
                <div class="chatTopic">{{ item.name }}</div>
              </div>
            </div>
          </div>

          <div class="empty-box" v-else>
            <img
              :src="currentTheme == 'dark-theme' ? emptyDark : emptytlight"
              alt="Your Image"
            />
            <p style="margin-top: 0; text-align: center">
              {{ $t("faq.noChat") }}
            </p>
          </div>
        </div>

        <div class="userList">
          <div class="userBox">
            <img
              class="avatar"
              :src="currentTheme == 'dark-theme' ? user : user2"
              alt="avatar"
            />
            <span v-if="isLeftNav">{{ userName || $t("faq.guest") }}</span>
          </div>
          <div class="userBox" v-if="userName">
            <div class="userBox-text">
              <span>{{ $t("faq.beta") }}</span>
              <div class="feedback">
                <img
                  :src="currentTheme == 'dark-theme' ? feedback : feedback2"
                  style=""
                />
                <a
                  :href="feedbackHref"
                  target="_blank"
                  :title="$t('faq.feedback')"
                  >{{ $t("faq.feedback").slice(0, 6) }}
                  <i v-if="$i18n.locale == 'fr' || $i18n.locale == 'en'">...</i>
                </a>
              </div>
              <span class="vertical-line">|</span>
              <span class="logout" v-if="isLeftNav" @click="logout">{{
                $t("faq.logout")
              }}</span>
            </div>
          </div>
          <div class="userBox" @click="login" v-if="!userName">
            <img src="./images/logout_h5.png" alt="logout_h5" />
            <span style="cursor: pointer" v-if="isLeftNav">{{
              $t("market.login")
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- </Popup> -->
    <div
      :style="[
        {
          width:
            !isLeftNav || $route.query?.hideHistory
              ? ' calc(100%)'
              : ' calc(100% - 264px)',
        },
      ]"
      class="right"
    >
      <header>
        <!-- 增加右展开图片 -->
        <div
          class="openBtnLeft"
          v-if="!isLeftNav && !$route.query?.hideHistory"
        >
          <img
            @click="openMenuPc"
            :src="currentTheme === 'dark-theme' ? open_pc : open_pc2"
          />
          <span
            ><a :href="hrefUrl" class="hover:text-[#5298FF]">{{
              $t("tool.home")
            }}</a
            ><span class="line">/</span>{{ $t(`${types["问答"]}`)
            }}<span class="line">/</span> {{ this.currentItem?.appName }}</span
          >
        </div>
        <div class="openBtn" v-if="isLeftNav && !$route.query?.hideHistory">
          <img
            @click="closeMenuPc"
            :src="currentTheme === 'dark-theme' ? close_pc : close_pc2"
          />
          <span
            ><a :href="hrefUrl" class="hover:text-[#5298FF]">{{
              $t("tool.home")
            }}</a
            ><span class="line">/</span>{{ $t(`${types["问答"]}`)
            }}<span class="line">/</span>{{this.currentItem?.appName }}</span
          >
        </div>

        <div class="topTitle">
          <a :href="hrefUrl"
            ><img
              alt="MedsciAI"
              :src="currentTheme === 'dark-theme' ? logo1 : logo"
          /></a>
          <span>
            <a :href="hrefUrl">{{ this.currentItem?.appName }}</a></span
          >
        </div>
      </header>
      <main style="position: relative;">
        <section id="scrollbar">
          <!-- 上半部分内容 -->
          <div v-if="isNewDialogue" class="box">
            <div
              v-for="(item, index) in messageList"
              :key="index"
              class="contextStyle"
            >
              <div v-if="item.chatType == 1" class="contextStyle1">
                <div
                  style="
                    display: flex;
                    align-items: center;
                    width: 34px;
                    margin-right: 10px;
                  "
                >
                  <img
                    :src="avatar || avat"
                    alt="avatar"
                    @error="changeImg"
                    class="avatar"
                  />
                </div>
                <div>
                  <div style="display:flex">
                    <div
                    class="documents"
                    v-for="(items, index) in item.message_files"
                    :key="index"
                  >
                    <div>
                      <div class="name" >
                        <img
                          class="logo"
                          v-if="items.type == 'image'"
                          src="https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png"
                          alt=""
                        />
                        <img
                          class="logo"
                          v-if="items.type != 'image'"
                          src="https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png"
                          alt=""
                        />{{ items.filename.slice(items.filename.indexOf("_") + 1) }}
                      </div>
                      <div class="size" >
                        <img />{{
                          items.filename.split(".")[1].toUpperCase()
                        }}
                        · {{ (items.size / 1024).toFixed(2) }}KB
                      </div>
                    </div>
                    <!-- <img style="width:64px;" v-if="items.type=='image'" :src="items.url"  :key="index" alt="" /> -->
                  </div>
                  </div>

                  <div
                    style="
                      flex: 1;
                      line-height: 22px;
                      font-size: 14px;
                      overflow-wrap: anywhere;
                      text-align: 左侧历史记录列表ustify;
                      letter-spacing: 0.5px;
                    "
                    v-html="item.answer"
                  ></div>
                </div>
              </div>
              <div v-else class="contextStyle2">
                <div class="contextStyle2Top" style="text-align: justify">
                  <div
                    style="width: 2.5rem; height: 2.5rem; margin-right: 0.8rem"
                  >
                    <img
                      src="./images/robot.png"
                      alt="resAvatar"
                      class="avatar"
                    />
                  </div>
                  <div style="width: calc(100% - 3.3rem)">
                    <v-md-preview
                      :text="
                        (item.answer || '')
                          .replace(/\\\[/g, '$[')
                          .replace(/\\\]/g, ']$')
                          .replace(/\\\(/g, '$(')
                          .replace(/\\\)/g, ')$')
                      "
                      v-if="!isSubscribe"
                      style="
                        width: calc(100% - 44px);
                        letter-spacing: 0.5px;
                        line-height: 22px;
                        margin-top: 5px;
                        font-size: 14px;
                        word-break: break-word;
                      "
                    />
                    <div v-if="isSubscribe">
                      {{ item.answer
                      }}<el-button
                        type="text"
                        @click="handleOrder"
                        v-if="index == messageList.length - 1"
                        >{{ $t("market.subscribe") }}</el-button
                      >
                    </div>
                    <div class="tags">
                      <div
                        class="tag"
                        v-for="items in item.suggested_questions"
                        :key="items"
                        @click="defoultAnswer(items)"
                      >
                        {{ items }}
                      </div>
                    </div>
                  </div>
                  <Loading
                    v-if="item.loading"
                    color="#1989fa"
                    style="width: 25px"
                  />
                  <!-- <div v-else v-html="item.answer"
                    style="letter-spacing: 0.5px;line-height: 22px; margin-top: 10px; font-size: 16px; word-break: break-word">
                  </div> -->
                </div>
                <!-- <div class="contextStyle2Bottom">
                  <div style="
                      display: flex;
                      justify-content: space-around;
                      width: 83px;
                      height: 16px;
                    ">
                    <img :src="currentTheme === 'dark-theme'
                        ? import('./images/copy_pc.png')
                        : import('./images/copy_pc2.png')
                      " />
                    <span style="margin-left: 2px; margin-top: -2px">|</span>
                    <img :src="currentTheme === 'dark-theme'
                        ? import('./images/like_pc.png')
                        : import('./images/like_pc2.png')
                      " />
                    <img :src="currentTheme === 'dark-theme'
                        ? import('./images/critic_pc.png')
                        : import('./images/critic_pc2.png')
                      " />
                  </div>
                </div> -->
              </div>
            </div>
          </div>
          <div v-if="!isNewDialogue" class="box">
            <div
              v-for="(item, index) in messageList"
              :key="index"
              class="contextStyle"
            >
              <div class="contextStyle1" v-if="item.id">
                <div
                  style="
                    display: flex;
                    align-items: center;
                    width: 34px;
                    margin-right: 10px;
                  "
                >
                  <img
                    :src="avatar || avat"
                    alt="avatar"
                    @error="changeImg"
                    class="avatar"
                  />
                </div>
                <div
                  style="
                    width: calc(100% - 44px);
                    line-height: 22px;
                    font-size: 14px;
                    overflow-wrap: anywhere;
                    text-align: 左侧历史记录列表ustify;
                    letter-spacing: 0.5px;
                  "
                  v-html="item.query"
                ></div>
              </div>
              <div class="contextStyle2" v-if="item.isTrue || item.id">
                <div class="contextStyle2Top" style="text-align: justify">
                  <div
                    style="width: 2.5rem; height: 2.5rem; margin-right: 0.8rem"
                  >
                    <img
                      src="./images/robot.png"
                      alt="resAvatar"
                      class="avatar"
                    />
                  </div>
                  <div style="width: calc(100% - 3.3rem)">
                    <v-md-preview
                      :text="
                        (item.answer || '')
                          .replace(/\\\[/g, '$[')
                          .replace(/\\\]/g, ']$')
                          .replace(/\\\(/g, '$(')
                          .replace(/\\\)/g, ')$')
                      "
                      v-if="!isSubscribe"
                      style="
                        width: calc(100% - 44px);
                        letter-spacing: 0.5px;
                        line-height: 22px;
                        margin-top: 5px;
                        font-size: 14px;
                        word-break: break-word;
                      "
                    />
                    <div v-if="isSubscribe">
                      {{ item.answer }}
                      <el-button
                        type="text"
                        @click="handleOrder"
                        v-if="index == messageList.length - 1"
                        >{{ $t("market.subscribe") }}</el-button
                      >
                    </div>
                    <div class="tags">
                      <div
                        class="tag"
                        v-for="items in item.suggested_questions"
                        :key="items"
                        @click="defoultAnswer(items)"
                      >
                        {{ items }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 新对话页面 -->
          <!-- & messageList.length < 0  -->
          <!-- <div v-if="isShow && messageList.length <= 0" class="newDialogue">
            <div class="newBox">
              <div class="titleBox" :style="isLightTheme ? {color: '#2f5883' } : {color: '#fff'}">
                <span style="font-size:26px;font-weight: 600;" :style="isLightTheme ? {color: '#2f5883' }:{}">
                  {{ $t('faq.hello') }}，{{ $t('faq.iAm') }}&nbsp; 
                </span>
                <span :style="isLightTheme ? {color: '#4892E7',fontSize:'26px',fontWeight: 600 } : {fontSize:'26px',color: '#79B7FF',fontWeight: 600}">
                  {{ $t('faq.medsciXAI') }}
                </span>
              </div>
              <div class="h2">
                {{ $t('faq.humanLang') }}，{{ $t('faq.medicalQues') }}
              </div>
            </div>
            <div class="qTemplate">
              <el-row :gutter="20" class="qContent">
                <el-col v-for="item in filterList" :key="item.id" :xs="24" :sm="24" :md="12" :lg="8" :xl="6">
                  <div class="grid-content qItemBox">
                    <div class="qTop">
                      <span class="keyWord">{{ item.tagName }}</span>
                      <div class="editBox" @click="editBtn(item.chatTemplate)">
                        <img :src="
                            currentTheme === 'dark-theme'
                              ? import('./images/editIcon_Pc.png')
                              : import('./images/editIcon_Pc2.png')
                          " />
                        <span class="editBtn">编辑执行</span>
                      </div>
                    </div>
                    <span class="qBottom" v-html="item.chatFormatTemplate"></span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div> -->
        </section>
        <div class="footer">
          <div
            class="backImg"
            :class="
              currentTheme == 'dark-theme' ? 'backImg_dark' : 'backImg_light'
            "
            v-if="!$route.query?.hideHistory"
          >
            <a :href="hrefUrl"
              ><img
                src="https://img.medsci.cn/202502/aa9a00f8e05a4c5b86e00f493fbdac51-KU2AOKigINI4.png"
                alt="更多智能体"
              />{{ $t("tool.moreAgents") }}</a
            >
          </div>

          <div class="footer-wrap-inner">
            <div
              style="
                display: flex;
                flex-direction: column;
                position: relative;
                border-radius: 8px;
              "
            >
              <!-- 下半部分内容 -->
              <div class="footer-wrap">
                <div class="files" id="scrollFilesPc">
                  <div
                    class="documents"
                    v-for="(item, index) in fileList"
                    :key="index"
                  >
                    <div class="icon">
                      <el-icon @click.stop="handleDelete(item.upload_file_id)"
                        ><CloseBold
                      /></el-icon>
                    </div>

                    <div class="name">
                      <img
                        v-if="item.type == 'image'"
                        class="logo"
                        src="https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png"
                        alt=""
                      />
                      <img
                        v-if="item.type != 'image'"
                        class="logo"
                        src="https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png"
                        alt=""
                      />{{ item.name.slice(item.name.indexOf("_") + 1) }}
                    </div>
                    <div class="size" >
                      {{ item.extension }} ·
                      {{ (item.size / 1024).toFixed(2) }}KB
                    </div>
                    <!-- <img v-if="item.type=='image'" :src="item.url" alt=""/> -->
                  </div>
                </div>
              </div>
              <div class="btomArea">
                <textarea
                  ref="myTextarea"
                  :class="{ textareaStyle: contextDisable }"
                  class="chatInput"
                  @input="chatMessage()"
                  @focus="focusSubscribe"
                  v-model="message"
                  :placeholder="
                    messageList.length ? $t('faq.iMSL') : $t('faq.questionTemp')
                  "
                  maxlength="1024"
                  :readonly="contextDisable"
                  @keydown.enter.exact.prevent="sendMessage"
                ></textarea>
                <div class="upload" v-if="fileUploadType?.file_upload?.enabled">
                  <el-upload
                   :file-list="fileList"
                    class="upload-demo"
                    :show-file-list="false"
                    multiple
                    :limit="fileUploadType?.file_upload?.number_limits"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                    :before-remove="beforeRemove"
                    :http-request="customRequest"
                    :on-Success="handleSuccess"
                    :on-exceed="handleExceed"
                    :accept="checkFileType()"
                  >
                    <img
                      style="width: 25px; height: 25px; margin-right: 4px"
                      :src="currentTheme == 'dark-theme'?'https://img.medsci.cn/202504/ebbbbbd8d4ee4beb9d2578930ac69fb5-NxjOfuavyQ26.png':'https://img.medsci.cn/202504/ed70d9316c644dfbb586bb23f55a7431-Jz8JLYrqoqid.png'"
                    />
                    <!-- <template #tip>
                  <div class="el-upload__tip">
                    jpg/png files with a size less than 500KB.
                  </div>
                </template> -->
                  </el-upload>
                </div>
                <el-button
                  :class="{ textareaStyle: contextDisable }"
                  :disabled="contextDisable||!message"
                  class="chatButton"
                  @click="sendMessage()"
                  v-if="(messageList && messageList.length == 0) || message"
                >
                  <span>
                    {{ $t("faq.send") }}
                  </span>
                </el-button>
                <el-button
                  :class="{ textareaStyle: contextDisable }"
                  :disabled="contextDisable||!message"
                  class="chatRefreshButton"
                  size="small"
                  @click="reloadMessage()"
                  v-else
                >
                  <img :src="refreshtn_pc" alt="icon" class="refreshBtn" />
                </el-button>
              </div>
            </div>
          </div>
          <footer
            style="margin-top: 16px; text-align: center; position: relative"
          >
            <div class="change_background" style="position: absolute">
              <el-switch
                v-model="isLightTheme"
                inactive-color="#858585"
                active-color="#D1E7FF"
                size="small"
                style="top: -4px"
              />
              <span style="margin-left: 4px">{{ $t("faq.gentleMode") }}</span>
            </div>
            <div class="response">
              <span class="linkStyle" :title="$t('faq.beforeUse')">
                {{
                  $i18n.locale == "fr"
                    ? $t("faq.beforeUse").slice(0, 37) + "..."
                    : $t("faq.beforeUse")
                }}
                <a
                  href="https://www.medsci.cn/about/index.do?id=14"
                  target="_blank"
                  >{{ $t("faq.disclaimer") }}</a
                >
              </span>
            </div>
          </footer>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import common from "./common";
import { useHead } from "@vueuse/head";
import titles from "@/langs/lang.js";
import emptyDark from "./images/empty.png";
import emptytlight from "./images/empty2.png";
import open_pc from "./images/open_pc.png";
import open_pc2 from "./images/open_pc2.png";
import close_pc from "./images/close_pc.png";
import close_pc2 from "./images/close_pc2.png";
import feedback from "./images/feedback.png";
import feedback2 from "./images/feedback2.png";
import user from "./images/user.png";
import user2 from "./images/user2.png";
import top_logo from "./images/top_logo.png";
import top_logo2 from "./images/top_logo2.png";
import refreshtn_pc from "./images/refreshtn_pc.png";
import avat from "./images/avat.png";
import logo from "./images/logo_pc.png";
import logo1 from "./images/logo1.png";
import { appTypes } from "@/common/commonJs";

export default {
  mixins: [common],
  setup() {
    const instance = getCurrentInstance().proxy; // 获取 Vue2 实例
    onMounted(() => {
      setTimeout(() => {
        if (instance.$data.currentItem) {
          useHead({
            title: () => `<${instance.$data.currentItem.appName}>${
          titles[localStorage.getItem("ai_apps_lang")]
        }`,
          });
        }
      }, 1000);
    });
  },
  data() {
    return {
      emptyDark: emptyDark,
      emptytlight: emptytlight,
      open_pc: open_pc,
      open_pc2: open_pc2,
      close_pc: close_pc,
      close_pc2: close_pc2,
      feedback: feedback,
      feedback2: feedback2,
      user: user,
      user2: user2,
      top_logo: top_logo,
      top_logo2: top_logo2,
      logo: logo,
      logo1: logo1,
      refreshtn_pc: refreshtn_pc,
      avat: avat,
      hrefUrl: "",
      types: appTypes,
      payShow: false,
    };
  },
  mounted() {
    this.hrefUrl =
      import.meta.env.MODE == "dev"
        ? "http://localhost:3000"
        : import.meta.env.MODE == "test"
        ? "https://ai.medon.com.cn"
        : import.meta.env.MODE == "prd"
        ? "https://ai.medsci.cn"
        : "";
  },
};
</script>
<style scoped lang="scss">
@import "@/assets/css/dark-theme.scss";
@import "@/assets/css/light-theme.scss";
:deep(.upload) {
  .el-button {
    width: 75px;
    height: 24px;
    border: 1px solid #ffffff;
    border-radius: 12px;
    background: unset;
    span {
      font-size: 12px;
    }
    img {
      width: 25px;
      height: 25px;
    }
  }
  .el-upload__input {
    display: none;
  }
  .el-upload-list {
    margin: 0;
  }
}
:deep(.el-dialog) {
  padding: 0px !important;
  width: 679px;
  border-radius: 20px;

  .el-dialog__header {
    padding: 0px !important;
  }
}
:deep() {
  .el-button--text {
    padding: 0;
    line-height: 1;
    height: auto;
  }
  .van-popup__close-icon {
    top: 7px !important;
  }
  .payMobile {
    display: none;
  }
  .payPC {
    display: block;
  }
}
body {
  box-sizing: content-box !important;
}
.line {
  line-height: 1;
  margin: 0 3px;
}
body::-webkit-scrollbar {
  display: none;
  width: 0px;
  background: transparent;
}

:deep(.github-markdown-body) {
  background: transparent;
  border-radius: 10px;
  padding: 0px;
  padding-right: 10px;
}
:deep(.github-markdown-body) table tr {
  background-color: unset;
}
:deep(.github-markdown-body) h1,
:deep(.github-markdown-body) h2,
:deep(.github-markdown-body) h3,
:deep(.github-markdown-body) h4 {
  color: white;
  font-size: 16px;
  border-bottom: 0;
}
.response {
  text-align: right;
}
.el-tag--plain.el-tag--primary:hover {
  cursor: pointer;
}
.box {
  width: 100%;
  ::v-deep .github-markdown-body {
    font-size: 14px;
    blockquote,
    details,
    dl,
    ol,
    p,
    pre,
    table,
    ul {
      margin-bottom: 4px;
    }
    blockquote {
      font-size: 14px;
    }
  }
}
.tags {
  width: 100%;
  display: flex;
  .tag {
    padding: 4px 10px;
    font-size: 12px;
    margin-right: 8px;
    border-radius: 2px;
  }
  .tag:hover {
    cursor: pointer;
    color: #2982e8;
  }
}
.backImg_dark {
  color: #fff;
}
.backImg_light {
  color: #65b0ef;
  background: #65b0ef !important;
}
.backImg:hover {
  cursor: pointer;
}
.backImg {
  border-radius: 8px;
  background: #ffffff1a;
  padding: 6px 10px;
  margin-bottom: 8px;
  line-height: 1;
  font-size: 12px;
  width: max-content;
  display: flex;
  align-items: center;
  a {
    color: #fff;
    display: flex;
    align-items: center;
  }
  img {
    width: 18px;
    height: 18px;
    margin-right: 6px;
  }
}
</style>
