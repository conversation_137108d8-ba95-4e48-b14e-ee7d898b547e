<template>
  <div
    class="container_box"
    :class="{ light: isLightTheme, dark: !isLightTheme }"
  >
    <Popup
      v-model:show="payShow"
      v-if="payShow"
      round
      closeable
      class="payMobile"
      position="bottom"
      :style="{ height: '90%' }"
    >
      <payMobile
        :userInfo="userInfo"
        :appTypes="appTypes"
        :currentItem="currentItem"
        @toAgreement="toAgreement"
        @close="close"
      />
    </Popup>
    <customerService v-if="isZH" />
    <Popup position="right" v-model:show="showMenu" :style="{ width: '284px' }">
      <!-- <left> -->
      <div
        class="chatBar"
        :class="{ light: isLightTheme, dark: !isLightTheme }"
      >
        <div class="startBtn" @click="newTalk">
          <span>{{ $t("faq.newChat") }}</span>
        </div>
        <!-- 左侧历史记录列表 -->
        <div class="logList">
          <div v-if="logList.length !== 0">
            <div
              v-for="(item, index) in logList"
              :key="index"
              :class="item.isActive ? 'isActive logBox' : 'logBox'"
            >
              <div class="chatTime" v-if="item.createdTime != ''">
                {{ item.createdTime }}
              </div>
              <div class="chatItem">
                <span class="middle-dot">·</span>
                <div class="chatTopic" @click="getLog(item.id, item.name)">
                  {{ stripHtmlTags(item.name) }}
                </div>
                <!-- <img
                    :src="imgpath.delete"
                    alt=""
                    @click="handleDelete(item.conversationId)"
                  /> -->
              </div>
            </div>
          </div>
          <div class="empty-box" v-else>
            <img
              :src="isLightTheme ? lightpath.empty2 : darkpath.empty"
              alt="Your Image"
            />
            <p>无对话</p>
          </div>
        </div>
        <div class="userList">
          <div class="userBox">
            <img :src="imgpath.user" alt="avatar" />
            <span>{{ userName || "游客" }}</span>
          </div>
          <div class="userBox" v-if="userName">
            <div class="userBox-text">
              <div class="feedback">
                <img :src="imgpath.feedback" alt="" style="" />
                <a :href="feedbackHref" target="_blank"> 点此意见反馈 </a>
              </div>
              <span class="vertical-line">|</span>
              <span class="logout" v-if="isLeftNav" @click="logout"
                >退出登录</span
              >
            </div>
        </div>
        <div class="userBox" @click="login" v-if="!userName">{{userName}}
            <img src="./images/logout_h5.png" alt="logout_h5" />
            <span style="cursor: pointer" v-if="isLeftNav">{{
              $t("market.login")
            }}</span>
          
          </div>
        </div>
      </div>
      <!-- </left> -->
    </Popup>
    <!-- <right> -->
    <header>
      <div class="betaBox">
        <div class="logoBox">
          <a :href="hrefUrl"><img alt="MedsciAI" :src="imgpath.logo" /></a>
          <span
            ><a :href="hrefUrl">{{ $route.query?.appName }}</a></span
          >
        </div>

        <!-- <img class="betaImg" :src="imgpath.beta" alt="" /> -->
      </div>
      <img @click="openMenu" class="openBtn" :src="imgpath.open" alt="" />
    </header>
    <main style="padding: 0 15px">
      <section id="scrollbar">
        <!-- <div v-if="isShow && messageList.length <= 0">
                    <div class="newBox">
                        <div class="titleBox" v-html="highlightText"></div>
                        <div class="h2" style="font-size: 14px;">
                            能学习和理解人类的语言，能回答你医疗相关的问题
                        </div>
                    </div>
                    <div class="qTemplate" v-if="filterList.length !== 0">
                        <div class="qContent">
                            <div class="qItemBox" v-for="item in filterList" :key="item.id">
                                <div class="qTop">
                                    <span class="keyWord">{{ item.tagName }}</span>
                                    <div class="editBox" @click="editBtn(item.chatTemplate)">
                                        <img :src="imgpath.edit" />
                                        <span class="editBtn">编辑执行</span>
                                    </div>
                                </div>
                                <span class="qBottom" v-html="item.chatFormatTemplate"></span>
                            </div>
                        </div>
                    </div>
                </div> -->
        <!-- 上半部分内容 -->
        <div v-if="isNewDialogue" class="box">
          <div
            v-for="(item, index) in messageList"
            :key="index"
            class="contextStyle"
          >
            <div v-if="item.chatType == 1" class="contextStyle1">
              <div
                style="
                  display: flex;
                  align-items: center;
                  width: 34px;
                  margin-right: 10px;
                "
              >
                <img
                  :src="avatar || avat"
                  alt="avatar"
                  @error="changeImg"
                  class="avatar"
                />
              </div>
              <div style="width:calc(100% - 25px)">
                <div style="display:flex;flex-wrap: wrap;">
                    <div
                    class="documents"
                    v-for="(items, index) in item.message_files"
                    :key="index"
                  >
                    <div>
                      <div class="name" >
                        <img
                          class="logo"
                          v-if="items.type == 'image'"
                          src="https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png"
                          alt=""
                        />
                        <img
                          class="logo"
                          v-if="items.type != 'image'"
                          src="https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png"
                          alt=""
                        />{{ items.filename.slice(items.filename.indexOf("_") + 1) }}
                      </div>
                      <div class="size" >
                        <img />{{
                          items.filename.split(".")[1].toUpperCase()
                        }}
                        · {{ (items.size / 1024).toFixed(2) }}KB
                      </div>
                    </div>
                    <!-- <img style="width:64px;" v-if="items.type=='image'" :src="items.url"  :key="index" alt="" /> -->
                  </div>
                  </div>
                <div
                  style="
                    flex: 1;
                    line-height: 22px;
                    font-size: 14px;
                    overflow-wrap: anywhere;
                    text-align: 左侧历史记录列表ustify;
                    letter-spacing: 0.5px;
                  "
                  v-html="item.answer"
                ></div>
              </div>
            </div>
            <div v-else class="contextStyle2">
              <div class="contextStyle2Top" style="text-align: justify">
                <div
                  style="width: 2.5rem; height: 2.5rem; margin-right: 0.8rem"
                >
                  <img
                    src="./images/robot.png"
                    alt="resAvatar"
                    class="avatar"
                  />
                </div>
                <div style="width: calc(100% - 3.3rem)">
                  <v-md-preview
                    :text="
                      (item.answer || '')
                        .replace(/\\\[/g, '$[')
                        .replace(/\\\]/g, ']$')
                        .replace(/\\\(/g, '$(')
                        .replace(/\\\)/g, ')$')
                    "
                    v-if="!isSubscribe"
                    style="
                      width: calc(100%);
                      letter-spacing: 0.5px;
                      line-height: 22px;
                      margin-top: 5px;
                      font-size: 14px;
                      word-break: break-word;
                    "
                  />
                  <div v-if="isSubscribe">
                    {{ item.answer
                    }}<el-button
                      type="text"
                      v-if="index == messageList.length - 1"
                      @click="handleOrder"
                      >{{ $t("market.subscribe") }}</el-button
                    >
                  </div>
                  <div class="tags">
                    <div
                      class="tag"
                      v-for="items in item.suggested_questions"
                      :key="items"
                      @click="defoultAnswer(items)"
                    >
                      {{ items }}
                    </div>
                  </div>
                </div>
                <Loading
                  v-if="item.loading"
                  color="#1989fa"
                  style="width: 25px"
                />
                <!-- <div v-else v-html="item.answer"
                    style="letter-spacing: 0.5px;line-height: 22px; margin-top: 10px; font-size: 16px; word-break: break-word">
                  </div> -->
              </div>
              <!-- <div class="contextStyle2Bottom">
                  <div style="
                      display: flex;
                      justify-content: space-around;
                      width: 83px;
                      height: 16px;
                    ">
                    <img :src="currentTheme === 'dark-theme'
                        ? import('./images/copy_pc.png')
                        : import('./images/copy_pc2.png')
                      " />
                    <span style="margin-left: 2px; margin-top: -2px">|</span>
                    <img :src="currentTheme === 'dark-theme'
                        ? import('./images/like_pc.png')
                        : import('./images/like_pc2.png')
                      " />
                    <img :src="currentTheme === 'dark-theme'
                        ? import('./images/critic_pc.png')
                        : import('./images/critic_pc2.png')
                      " />
                  </div>
                </div> -->
            </div>
          </div>
        </div>
        <div v-if="!isNewDialogue" class="box">
          <div
            v-for="(item, index) in messageList"
            :key="index"
            class="contextStyle"
          >
            <div class="contextStyle1" v-if="item.id">
              <div
                style="
                  display: flex;
                  align-items: center;
                  width: 34px;
                  margin-right: 10px;
                "
              >
                <img
                  :src="avatar || avat"
                  alt="avatar"
                  @error="changeImg"
                  class="avatar"
                />
              </div>
              <div
                style="
                  width: calc(100% - 44px);
                  line-height: 22px;
                  font-size: 14px;
                  overflow-wrap: anywhere;
                  text-align: 左侧历史记录列表ustify;
                  letter-spacing: 0.5px;
                "
                v-html="item.query"
              ></div>
            </div>
            <div class="contextStyle2" v-if="item.isTrue || item.id">
              <div class="contextStyle2Top" style="text-align: justify">
                <div
                  style="width: 2.5rem; height: 2.5rem; margin-right: 0.8rem"
                >
                  <img
                    src="./images/robot.png"
                    alt="resAvatar"
                    class="avatar"
                  />
                </div>
                <div style="width: calc(100% - 3.3rem)">
                  <v-md-preview
                    :text="
                      (item.answer || '')
                        .replace(/\\\[/g, '$[')
                        .replace(/\\\]/g, ']$')
                        .replace(/\\\(/g, '$(')
                        .replace(/\\\)/g, ')$')
                    "
                    v-if="!isSubscribe"
                    style="
                      width: calc(100% - 44px);
                      letter-spacing: 0.5px;
                      line-height: 22px;
                      margin-top: 5px;
                      font-size: 14px;
                      word-break: break-word;
                    "
                  />
                  <div v-if="isSubscribe">
                    {{ item.answer
                    }}<el-button
                      type="text"
                      v-if="index == messageList.length - 1"
                      @click="handleOrder"
                      >{{ $t("market.subscribe") }}</el-button
                    >
                  </div>
                  <div class="tags">
                    <div
                      class="tag"
                      v-for="items in item.suggested_questions"
                      :key="items"
                      @click="defoultAnswer(items)"
                    >
                      {{ items }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
    <footer
      style="
        text-align: center;
        position: relative;
        position: fixed;
        bottom: 0;
        width: 100%;
        padding: 0 15px 10px;
      "
    >
      <a
        :href="hrefUrl"
        v-if="!$route.query?.hideHistory"
        class="backImg"
        :class="currentTheme == 'dark-theme' ? 'backImg_dark' : 'backImg_light'"
        ><img
          src="https://img.medsci.cn/202502/aa9a00f8e05a4c5b86e00f493fbdac51-KU2AOKigINI4.png"
          alt="更多智能体"
        />{{ $t("tool.moreAgents") }}</a
      >
      <div class="footer">
        <!-- 下半部分内容 -->
        <div class="files"  id="scrollFiles">
          <div
                    class="documents"
                    v-for="(item, index) in fileList"
                    :key="index"
                  >
                    <div class="icon">
                      <el-icon @click.stop="handleDelete(item.upload_file_id)"
                        ><CloseBold
                      /></el-icon>
                    </div>

                    <div class="name">
                      <img
                        v-if="item.type == 'image'"
                        class="logo"
                        src="https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png"
                        alt=""
                      />
                      <img
                        v-if="item.type != 'image'"
                        class="logo"
                        src="https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png"
                        alt=""
                      />{{ item.name.slice(item.name.indexOf("_") + 1) }}
                    </div>
                    <div class="size" >
                      {{ item.extension }} ·
                      {{ (item.size / 1024).toFixed(2) }}KB
                    </div>
                    <!-- <img v-if="item.type=='image'" :src="item.url" alt=""/> -->
                  </div>
        </div>
        <div class="btn_box">
          <textarea
            :class="{ textareaStyle: contextDisable }"
            class="chatInput"
            v-model="message"
            @focus="focusSubscribe"
            @input="chatMessage"
            rows="1"
            :placeholder="
              messageList.length ? $t('faq.iMSL') : $t('faq.questionTemp')
            "
            maxlength="1024"
            :readonly="contextDisable"
            @keydown.enter.exact.prevent="sendMessage"
          ></textarea>
          <div class="upload" v-if="fileUploadType?.file_upload?.enabled">
            <el-upload
              :file-list="fileList"
              class="upload-demo"
              :show-file-list="false"
              multiple
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :http-request="customRequest"
              :on-Success="handleSuccess"
              :accept="checkFileType()"
              :limit="fileUploadType?.file_upload?.number_limits"
              :on-exceed="handleExceed"
            >
            <img style="width: 25px; height: 25px; margin-right: 4px"  :src="currentTheme == 'dark-theme'?'https://img.medsci.cn/202504/ebbbbbd8d4ee4beb9d2578930ac69fb5-NxjOfuavyQ26.png':'https://img.medsci.cn/202504/ed70d9316c644dfbb586bb23f55a7431-Jz8JLYrqoqid.png'"/>
              <!-- <template #tip>
                  <div class="el-upload__tip">
                    jpg/png files with a size less than 500KB.
                  </div>
                </template> -->
            </el-upload>
          </div>
          <button
            :class="{ textareaStyle: contextDisable }"
            :disabled="contextDisable||!message"
            class="chatButton"
            @click="sendMessage()"
            v-if="(messageList && messageList.length == 0) || message"
          >
            <img :src="imgpath.send" alt="icon" class="sendBtn" />
          </button>

          <button
            :class="{ textareaStyle: contextDisable }"
            :disabled="contextDisable||!message"
            class="chatButton"
            @click="reloadMessage()"
            v-else
          >
            <img :src="imgpath.refresh" alt="icon" class="refreshBtn" />
          </button>
        </div>
      </div>
      <div class="footer_box">
        <div class="change_background" style="position: absolute">
          <el-switch
            v-model="isLightTheme"
            size="small"
            inactive-color="#858585"
            active-color="#D1E7FF"
            :class="isLightTheme ? lights : darks"
          />
          <span>{{ $t("faq.gentleMode") }}</span>
        </div>
        <div style="text-align: right; width: 100%">
          <span class="linkStyle">
            {{
              $i18n.locale != "zh-CN"
                ? $t("faq.beforeUse").slice(0, 30) + "..."
                : $t("faq.beforeUse")
            }}
            <a
              href="https://www.medsci.cn/about/index.do?id=14"
              target="_blank"
            >
              {{ $t("faq.disclaimer") }}
            </a>
          </span>
        </div>
      </div>
    </footer>
    <!-- </right> -->
  </div>
</template>

<script>
import common from "./common";
import { showNotify } from "vant";
import { useHead } from "@vueuse/head";
import titles from "@/langs/lang.js";
import emptyDark from "./images/empty.png";
import empty2 from "./images/empty2.png";
import delete_pc2 from "./images/delete_pc2.png";
import delete_h5 from "./images/delete_h5.png";
import user2 from "./images/user2.png";
import user from "./images/user.png";
import feedback2 from "./images/feedback2.png";
import feedback from "./images/feedback.png";
import logo_pc from "./images/logo_pc.png";
import logo_h5 from "./images/logo_h5.png";
import beta_h5_l from "./images/beta_h5_l.png";
import beta_h5 from "./images/beta_h5.png";
import open_h5_l from "./images/open_h5_l.png";
import open_h5 from "./images/open_h5.png";
import editIcon_Pc2 from "./images/editIcon_Pc2.png";
import editIcon_Pc from "./images/editIcon_Pc.png";
import robot from "./images/robot.png";
import sendPc from "./images/sendPc.png";
import refresh from "./images/refresh.png";
import refreshtn_pc from "./images/refreshtn_pc.png";
import copy_h5_l from "./images/copy_h5_l.png";
import copy_h5_d from "./images/copy_h5_d.png";
import like_h5_l from "./images/like_h5_l.png";
import like_h5_d from "./images/like_h5_d.png";
import unlike_h5_l from "./images/unlike_h5_l.png";
import unlike_h5_d from "./images/unlike_h5_d.png";
import avat from "./images/avat.png";

export default {
  mixins: [common],
  setup() {
    const instance = getCurrentInstance().proxy; // 获取 Vue2 实例
    onMounted(() => {
      setTimeout(() => {
        if (instance.$data.currentItem) {
          useHead({
            title: () => `<${instance.$data.currentItem.appName}>${
          titles[localStorage.getItem("ai_apps_lang")]
        }`,
          });
        }
      }, 1000);
    });
  },
  data() {
    return {
      imgpath: {},
      avat: avat,
      lightpath: {
        delete: delete_pc2,
        user: user2,
        feedback: feedback2,
        logo: logo_pc,
        beta: beta_h5_l,
        open: open_h5_l,
        edit: editIcon_Pc2,
        robot: robot,
        send: sendPc,
        refresh: refresh,
        copy: copy_h5_l,
        like: like_h5_l,
        unlike: unlike_h5_l,
        empty2: empty2,
      },
      darkpath: {
        delete: delete_h5,
        user: user,
        feedback: feedback,
        logo: logo_h5,
        beta: beta_h5,
        open: open_h5,
        edit: editIcon_Pc,
        robot: robot,
        send: sendPc,
        refresh: refreshtn_pc,
        copy: copy_h5_d,
        like: like_h5_d,
        unlike: unlike_h5_d,
        empty: emptyDark,
      },
    };
  },
  created() {
    this.imgpath = { ...this.darkpath };
    const root = document.querySelector(":root");
    root.style.setProperty("--text-color", "#fff");
    root.style.setProperty("--gray-color", "rgb(121, 133, 142)");
    root.style.setProperty("--accent-color", "#4892E7");
    root.style.setProperty("--accent-color-light", "#A3EEFF");
    root.style.setProperty("--primary-color", "#263c4aff");
    root.style.setProperty("--secondary-color", "#132c3bff");
  },
  watch: {
    isLightTheme(val) {
      const root = document.querySelector(":root");
      root.style.setProperty("--text-color", val ? "#2F5883" : "#fff");
      root.style.setProperty(
        "--gray-color",
        val ? "#4892E7" : "rgb(121, 133, 142)"
      );
      root.style.setProperty("--accent-color", val ? "#4892E7" : "#4892E7");
      root.style.setProperty(
        "--accent-color-light",
        val ? "#A3EEFF" : "#A3EEFF"
      );
      this.imgpath = val ? this.lightpath : this.darkpath;
    },
  },
  mounted() {
    this.hrefUrl =
      import.meta.env.MODE == "dev"
        ? "http://localhost:3000"
        : import.meta.env.MODE == "test"
        ? "https://ai.medon.com.cn"
        : import.meta.env.MODE == "prd"
        ? "https://ai.medsci.cn"
        : "";
  },
  methods: {
    copy(text) {
      const input = document.createElement("input");
      input.setAttribute("readonly", "readonly");
      input.setAttribute("value", text);
      document.body.appendChild(input);
      input.select();
      if (document.execCommand("copy")) {
        document.execCommand("copy");
        showNotify({
          message: this.$t("tool.copysuccess"),
          type: "primary",
        });
      }
      document.body.removeChild(input);
    },
    like() {
      showNotify({
        message: this.$t("tool.thankyoulike"),
        type: "primary",
      });
    },
    unlike() {
      showNotify({
        message: this.$t("tool.thankyoufeedback"),
        type: "primary",
      });
    },
    // handleDelete(item) {
    //   showNotify({
    //     message: this.$t("tool.clickdelete"),
    //     type: "primary",
    //   });
    //   console.log(item);
    // },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">

:deep() {
  .el-button--text {
    padding: 0;
    line-height: 1;
    height: auto;
  }
}
.payMobile {
  :deep() {
    .van-popup__close-icon {
      top: 7px !important;
    }
    .payMobile {
      display: none;
    }
    .payPC {
      display: block;
    }
  }
}
.container_box {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: var(--text-color);
  font-size: 12px;
  transition: all 0.5s;
  padding-bottom: 35px;
  height: calc(var(--vh) * 100);
  position: relative;
}

.dark {

  .documents{
    position: relative;
    margin-right: 10px;
    border-radius: 4px;
    font-size: 12px;
    flex-wrap: wrap;
    padding: 10px;
    color: #666666;
    background-color: #32495A;
    margin-bottom:10px;
    .logo{
      width: 22px;
      height: 22px;
      margin-right: 6px;
    }
    .name{
      color: #fff;
      color: #fff;
      font-weight: 400;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;       /* 隐藏溢出内容 */
      text-overflow: ellipsis;/* 溢出显示省略号 */
      max-width: 200px;           /* 必须设置宽度（或父级有宽度约束） */
    }
    .size{
      display: flex;
      align-items: center;
      margin-left: 28px;
      font-weight: 400;
      font-size: 12px;
      color: #9AAFC5;
      .el-icon{
        margin-right: 4px;
        font-size: 13px;
      }
    }
    .icon{
      font-size: 12px;
      font-weight: 500;
      position: absolute;
      right: -10px;
      display: none;
      top: -10px;
      z-index: 100;
      background-color: #fff;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
    }
  }
  .files{
      width: 100%;
      position: relative;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .documents{
        position: relative;
        margin: 10px;
        border-radius: 4px;
        font-size: 12px;
        flex-wrap: wrap;
        padding: 10px;
        color: #666666;
        background-color: #32495A;
        flex-direction: column;
        .logo{
          width: 22px;
          height: 22px;
          margin-right: 6px;
        }
        .name{
          color: #fff;
          font-weight: 400;
            font-size: 14px;
        }
        .size{
          display: flex;
          align-items: center;
          margin-left: 28px;
          font-weight: 400;
            font-size: 12px;
            color: #9AAFC5;
          .el-icon{
            margin-right: 4px;
            font-size: 12px;
          }
        }
        .icon{
          font-size: 10px;
          font-weight: 500;
          position: absolute;
          right: -10px;
          display: flex;
          align-items: center;
          justify-content: center;
          top: -10px;
          z-index: 100;
          background-color: #fff;
          border-radius: 50%;
          width: 15px;
          height: 15px;
          line-height: 15px;
          text-align: center;
        }
      }
      .imgs{
        position: relative;
        width: 64px;
        height: 64px;
        margin:10px 10px 10px;
        .icon{
          font-size: 12px;
          font-weight: 500;
          position: absolute;
          right: -10px;
          top: -10px;
          z-index: 100;
          background-color: #fff;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
        }
        img{
          width: 100%;
          height: 100%;
          z-index: 10;
        }
      }
      
    }
  &.container_box {
    background-image: url("./images/bg_h5.png");
  }

  .startBtn {
    background: url("./images/newTalk.png") no-repeat 0% / 100%;
  }

  .chatInput {
    background: transparent;
  }

  .contextStyle2 {
    background: linear-gradient(
      to bottom,
      var(--primary-color),
      var(--secondary-color)
    );
  }

  .chatBar {
    background: linear-gradient(
      180deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%
    );
  }

  .container_box {
    background-image: url("./images/bg_h5.png");
  }
  .tag {
    background: #384c59;
    border: solid 1px #9ba5ab;
  }
}

.light {
  :deep(.github-markdown-body) h1,
  :deep(.github-markdown-body) h2,
  :deep(.github-markdown-body) h3,
  :deep(.github-markdown-body) h4 {
    font-size: 16px;
    border-bottom: 0;
    color: unset;
  }
  .footer{
  background: #fff;

  }
  .documents{
      position: relative;
      margin-right: 10px;
      border-radius: 4px;
      font-size: 12px;
      flex-wrap: wrap;
      padding: 10px;
      color: #666666;
      background-color: #fff;
      margin-bottom:10px;
      .logo{
        width: 22px;
        height: 22px;
        margin-right: 6px;
      }
      .name{
        color: #2F5883;
        font-weight: 400;
        font-size: 14px;
        color: #2F5883;
        white-space: nowrap;
      overflow: hidden;       /* 隐藏溢出内容 */
      text-overflow: ellipsis;/* 溢出显示省略号 */
      max-width: 200px;   
      }
      .size{
        display: flex;
        align-items: center;
        margin-left: 28px;
        font-weight: 400;
        font-size: 12px;
        color: #9AAFC5;
        .el-icon{
          margin-right: 4px;
          font-size: 13px;
        }
      }
      .icon{
        font-size: 12px;
        font-weight: 500;
        position: absolute;
        right: 0;
        display: none;
        top: 0;
        z-index: 100;
        background-color: #E9E9E9;
        border-radius: 50%;
        width: 15px;
        height: 15px;
        line-height: 15px;
        text-align: center;
      }
    }
    .files{
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .imgs:hover,.documents:hover{
          cursor: pointer;
          .icon{
           
          }
        }
        .documents{
          position: relative;
          margin: 10px;
          border-radius: 4px;
          font-size: 12px;
          flex-wrap: wrap;
          padding: 10px;
          background-color: #F0F8FF;
          flex-direction: column;
          .logo{
            width: 22px;
            height: 22px;
            margin-right: 6px;
          }
          .name{
            font-weight: 400;
            font-size: 14px;
            color: #2F5883;
            white-space: nowrap;
            overflow: hidden;       /* 隐藏溢出内容 */
            text-overflow: ellipsis;/* 溢出显示省略号 */
            max-width: 200px;   
          }
          .size{
            display: flex;
            align-items: center;
            margin-left: 28px;
            font-weight: 400;
            font-size: 12px;
            color: #9AAFC5;
            .el-icon{
              margin-right: 4px;
              font-size: 10px;
            }
          }
          .icon{
            font-size: 10px;
            font-weight: 500;
            position: absolute;
            right: -7.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            top: -7.5px;
            z-index: 100;
            background-color: #E9E9E9;
            border-radius: 50%;
            width: 15px;
            height: 15px;
            line-height: 15px;
            text-align: center;
          }
        }
        .imgs{
          position: relative;
          width: 64px;
          height: 64px;
          margin:10px 10px 10px;
          .icon{
            font-size: 12px;
            font-weight: 500;
            position: absolute;
            right: -10px;
            display: none;
            top: -10px;
            z-index: 100;
            background-color: #fff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
          }
          img{
            width: 100%;
            height: 100%;
            z-index: 10;
          }
        }
        
      }
  &.container_box {
    background-image: url("./images/bg_h5_L.png");
  }

  .startBtn {
    background: url("./images/newTalk2.png") no-repeat 0% / 100%;
  }

  .qContent {
    background: linear-gradient(221.4deg, #e2f0ff 0%, #e6ecff 100%);
  }

  .chatInput {
    background: transparent;
  }

  .contextStyle2 {
    background: linear-gradient(221.4deg, #e2f0ff 0%, #e6ecff 100%);
  }

  .chatBar {
    background: linear-gradient(180deg, #cfe5fb 0%, #e6f0fc 100%);
  }

  .logoBox {
    color: var(--accent-color);
  }

  .linkStyle {
    color: #8197ae;

    a {
      color: var(--accent-color);
    }
  }
  .tag {
    background: #f6fbff;
    border: solid 1px #2982e8;
  }
}

left {
  width: 284px;
}

right {
  width: 100%;
  background: #fff;
}

.chatBar {
  height: calc(var(--vh) * 100);
  padding-top: 1px;
  box-sizing: border-box;
  position: relative;
  width: 284px;
  opacity: 1;
}

.startBtn {
  height: 43px;
  line-height: 43px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  background-size: 87% 100%;
  margin: 24px 24px 0px 25px;
  cursor: pointer;
}

.startBtn span {
  opacity: 1;
  font-size: 16px;
  font-weight: 500;
  font-family: "PingFang SC";
  text-align: center;
  margin-left: -1%;
}

.logList {
  width: 258px;
  border-radius: 6px;

  margin: 0 auto 40px;
  margin-top: 19px;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: calc(var(--vh) * 100 - 220px);
}

.logList::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.logList::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--gray-color);
}

.logBox {
  padding: 0 14px 18px 14px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-direction: column;
}

.empty-box {
  width: 100%;
  text-align: center;

  img {
    margin: 87px 0 4px 0;
    width: 68px;
    height: 68px;
  }

  p {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    font-family: "PingFang SC", sans-serif;
  }
}

.chatTime {
  color: #8197ae;
  font-size: 12px;
  font-weight: 400;
  font-family: "PingFang SC";
  text-align: justify;
  padding-bottom: 10px;
  padding-top: 10px;
}

.chatItem {
  display: flex;
  cursor: pointer;
  position: relative;
  width: 100%;
}

.middle-dot {
  position: relative;
  display: inline-block;
}

.isActive .middle-dot::after {
  background-color: #4892e7 !important;
}

.logBox:first-child {
  padding-top: 0px;
}

.middle-dot::after {
  content: "";
  position: absolute;
  top: 44%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--text-color);
}

.icon {
  width: 18px;
  vertical-align: middle;
}

.chatTopic {
  width: auto;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 0 2px 0 7px;
  cursor: pointer;
  font-size: 14px;
}

.isActive .chatTopic {
  color: var(--accent-color);
}

.userList {
  position: absolute;
  bottom: 10px;
  width: 242px;
  left: 53%;
  transform: translateX(-50%);
}

.userBox {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 15px 0;
  font-size: 14px;
}

.userBox-text {
  display: flex;
}

.feedback {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.feedback a {
  color: var(--text-color);
  text-decoration: none;
  font-size: 14px;
  border-bottom: 1px solid var(--text-color);
}

.vertical-line {
  margin: 0px 4px;
  color: var(--gray-color);
}

.logout {
  cursor: pointer;
  color: var(--gray-color);
  font-size: 14px;
}

.userBox img {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

#scrollbar {
  height: calc(var(--vh) * 100 - 200px);
  display: flex;
  flex-direction: column;
  align-items: center;

  margin-bottom: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  margin-top: 15px;
}

#scrollbar::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

#scrollbar::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--text-color);
}

.newBox {
  width: 100%;
  height: 56px;
  opacity: 1;
  position: absolute;
  top: 1%;
  left: 0;
}

.titleBox {
  width: 250px;
  height: 34px;
  opacity: 1;

  font-size: 24px;
  font-weight: 600;
  font-family: "PingFang SC";
  text-align: justify;
}

.qTemplate {
  position: relative;
  // width:360px;
  height: 429px;
  border-radius: 4px;
  opacity: 1;
  margin-top: 82px;
  box-sizing: border-box;
  // background: url("./images/template_h5.png") no-repeat;
  background: url("./images/template_h5.png") no-repeat top left / 100% 100%;
}

.qContent {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}

.qContent > div:first-child {
  margin-top: 17px;
}

.qItemBox {
  width: 100%;
  min-height: 69px;
  box-sizing: border-box;
  border-radius: 6px;
  margin-top: 34px;
  padding: 0 24px;
  box-sizing: border-box;
  overflow: auto;
}

.qTop {
  display: flex;
  justify-content: space-between;
}

.qBottom {
  margin-top: 6px;
  display: block;
  opacity: 0.8;

  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC";
  text-align: left;
  letter-spacing: 0.5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 控制显示行数 */
  -webkit-box-orient: vertical;
}

.keyWord {
  opacity: 1;

  font-size: 16px;
  font-weight: 600;
  font-family: "PingFang SC";
  text-align: left;
}

.editBox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.editBox img {
  width: 12px;
  height: 12px;
  margin-right: 6px;
}

.editBtn {
  opacity: 1;
  color: var(--gray-color);
  font-size: 12px;
  font-weight: 400;
  font-family: "PingFang SC";
}

header {
  position: relative;
  height: 40px;
  padding: 0 15px;
}

.openBtn {
  display: block;
  position: absolute;
  right: 0;
  width: 18px;
  padding: 18px 19px;
  cursor: pointer;
  box-sizing: content-box;
}

.logoBox {
  position: absolute;
  top: 10px;

  display: flex;
  flex-direction: row;
  align-items: center;

  font-size: 13px;
  font-weight: 400;
  a {
    color: #fff !important;
  }
}

.logoBox img {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

.betaBox {
  position: absolute;
  left: 15px;
  top: 6px;
  width: 80%;
  height: 40px;

  display: flex;
  flex-direction: row;
  align-items: center;
}

.betaBox img {
  margin-right: 3px;
  margin-top: 1px;
}

.betaImg {
  width: 38px;
  height: 18px;
  margin-left: 86px;
}

.avatar {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.contextStyle {
  width: 100%;
  box-sizing: border-box;
}

.box {
  width: 100%;
  ::v-deep .github-markdown-body {
    font-size: 14px;
    blockquote,
    details,
    dl,
    ol,
    p,
    pre,
    table,
    ul {
      margin-bottom: 4px;
    }
    td {
      font-size: 12px;
    }
    blockquote {
      font-size: 14px;
    }
  }
}

.contextStyle:last-child {
  border-bottom: none;
}

.contextStyle1 {
  display: flex;
  flex-direction: row;
  padding: 12px;
  padding-left: 17px;
  font-size: 15px;
  text-align: justify;
  align-items: flex-start;
  .contentAnswer {
    width: calc(100% - 40px);
    font-size: 14px;
  }
}

.contextStyle2 {
  text-align: justify;
  position: relative;
  display: flex;
  flex-direction: row;
  // padding: 12px;
  // padding-bottom: 32px;
  padding: 16px;
  flex-wrap: wrap;
  border-radius: 6px;
  font-size: 15px;
  .contentAnswer {
    width: calc(100% - 40px);
  }
  .message-footer {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: right;
    bottom: 6px;
    left: 0;
    box-sizing: border-box;
    height: 24px;
    padding: 6px;
    width: 100%;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 12px;
    }

    .split {
      width: 1px;
      height: 10px;
      background-color: var(--gray-color);
      margin-right: 12px;
    }
  }
}

.contextStyle2Top {
  width: 100%;
  display: flex;
}

.contextStyle2Bottom {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 6px;
}

.contextStyle2Bottom img {
  width: 14px;
  height: 14px;
}


.textareaStyle {
  cursor: not-allowed;
}

.footer {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  width: 99%;
  min-height: 48px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  opacity: 1;
  border: 2px solid;
  position: relative;
  border-image: linear-gradient(
      to right,
      var(--accent-color-light),
      var(--accent-color)
    )
    1;
  clip-path: inset(0 round 3px);
}



.change_background {
  display: flex;
  align-items: center;
  .lights {
    ::v-deep .van-switch__node {
      background: #4795e8;
    }
  }

  .darks {
    ::v-deep .van-switch__node {
      background: #3a4650;
    }
  }
}

.change_background span {
  opacity: 1;

  font-size: 12px;
  font-weight: 400;
  font-family: "PingFang SC";
  text-align: justify;
  margin-left: 4px;
}

.footer span {
  opacity: 1;

  font-size: 12px;
  font-weight: 400;
  font-family: "PingFang SC";
  text-align: justify;
  margin-left: 4px;
}

.linkStyle {
  font-size: 12px;
  font-weight: 400;
  color: var(--gray-color);
}

.linkStyle a {
  text-decoration: none;
  color: var(--text-color);
}

.footer_box {
  padding: 14px 0 0;
  display: flex;
  align-items: center;
}

:deep(.github-markdown-body) {
  background: transparent;
  border-radius: 10px;
  padding: 0px;
  padding-right: 10px;
}
:deep(.github-markdown-body) table tr {
  background-color: unset;
}
:deep(.github-markdown-body) h1,
:deep(.github-markdown-body) h2,
:deep(.github-markdown-body) h3,
:deep(.github-markdown-body) h4 {
  color: white;
  font-size: 16px;
  border-bottom: 0;
}
:deep(.qr-code) {
  color: #666;
}
.tags {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .tag {
    padding: 4px 10px;
    font-size: 12px;
    margin-right: 8px;
    border-radius: 2px;
    margin-bottom: 5px;
  }
}
.backImg_dark {
  color: #fff;
}
.backImg_light {
  color: #65b0ef;
  background: #65b0ef !important;
}
.backImg:hover {
  cursor: pointer;
}
.backImg {
  border-radius: 8px;
  background: #ffffff1a;
  padding: 6px 10px;
  margin-bottom: 8px;
  color: #fff;
  font-size: 12px;
  width: max-content;
  color: #fff;
  display: flex;
  align-items: center;
  img {
    width: 18px;
    height: 18px;
    margin-right: 6px;
  }
}
.btn_box{
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  .chatInput {
  display: inline-block;
  margin-left: 9px;
  height: auto;
  border: none;
  flex-grow: 1;
  outline: none;
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 15px;
  resize: none;
}

.sendBtn {
  width: 25px;
  height: 25px;
  vertical-align: middle;
  cursor: pointer;
}

.refreshBtn {
  width: 20px;
  height: 20px;
  vertical-align: middle;
  cursor: pointer;
}
.chatButton {
  background-color: transparent;
  border: none;
  font-family: "Microsoft YaHei", sans-serif;
  padding: 0 10px 0 10px;
}
:deep(.upload) {
  padding: 0 0 0 5px;
  .upload-demo{
    height: 20px;
    width: 20px;
    img{
      width: 20px;
      height: 20px;
    }
  }
  .el-button {
    width: 75px;
    height: 24px;
    border: 1px solid #ffffff;
    border-radius: 12px;
    background: unset;
    span {
      font-size: 12px;
    }
  }
  .el-upload__input {
    display: none;
  }
  .el-upload-list {
    margin: 0;
  }
}
}
</style>
