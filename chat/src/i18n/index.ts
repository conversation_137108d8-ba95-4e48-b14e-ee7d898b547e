import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import Cookies from 'js-cookie'
import AppServices from '../services/app/request'
const appserver = new AppServices()

// 默认翻译资源（作为fallback）
const defaultResources = {
}
// 格式化后台语言包配置
 const transformArrayToObject = (array:any) => {
  const result:any = {};
  array.forEach((item:any)=> {
    // 假设 key 字段的格式总是 "xx.yy"，其中 "xx" 是语言代码，"yy" 是具体的键
    const [langCode] = item.key.split('.');
    const value = JSON.parse(item.value);
 
    // 如果语言代码对应的对象还不存在，则创建一个空对象
    if (!result[langCode]) {
      result[langCode] = {};
    }

    // 将具体的键值对添加到语言代码对应的对象中
    result[langCode] = {...result[langCode], ...value}; // 解析 value 字段的 JSON 字符串
  });
 
  return result;
}
// 从接口获取翻译资源
const getResourcesFromAPI = async () => {
  try {
    const result = await appserver.getConfigPage({ configName: 'aiBase国际化' })
    console.log('result', result) 
    if (result && result.code === 0 && result.data?.list) {
      const configData = result.data.list
      console.log('configData', configData)
      const langResources: any = transformArrayToObject(configData)
      console.log('langResources', langResources)

      // 包装为i18n需要的格式
      const resources: any = {}
      Object.keys(langResources).forEach(lang => {
        resources[lang] = { translation: langResources[lang] }
      })

      return Object.keys(resources).length > 0 ? resources : defaultResources
    }
  } catch (error) {
    console.warn('Failed to fetch i18n config from API, using default resources:', error)
  }

}

// 支持的语言列表
export const supportedLanguages = [
  { code: 'zh', name: '简体中文' },
  { code: 'tw', name: '繁體中文' },
  { code: 'en', name: 'English' },
  { code: 'vi', name: 'Tiếng Việt' },
  { code: 'es', name: 'Español' },
  { code: 'ar', name: 'العربية' },
  { code: 'id', name: 'Bahasa Indonesia' },
  { code: 'pt', name: 'Português' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' },
  { code: 'ms', name: 'Bahasa Melayu' },
]

// 获取默认语言
const getDefaultLanguage = (): string => {
  // 优先从cookie获取
  const cookieLang = Cookies.get('ai_apps_lang')
  if (cookieLang && supportedLanguages.some(lang => lang.code === cookieLang)) {
    return cookieLang
  }
  
  // 从浏览器语言检测
  const browserLang = (typeof navigator !== 'undefined' ? navigator.language : null) || 'zh'
  
  // 匹配支持的语言
  for (const lang of supportedLanguages) {
    if (browserLang.startsWith(lang.code) || browserLang.startsWith(lang.code.split('-')[0])) {
      return lang.code
    }
  }
  
  return 'zh' // 默认中文
}

// 异步初始化i18n
const initI18n = async () => {
  const resources = await getResourcesFromAPI()
  console.log('i18n resources:', resources)
  return i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources,
      lng: getDefaultLanguage(),
      fallbackLng: 'zh',

      // 语言检测配置
      detection: {
        order: ['cookie', 'navigator'],
        caches: ['cookie'],
        cookieMinutes: 60 * 24 * 30, // 30天
        cookieDomain: process.env.NODE_ENV === 'production' ? '.medsci.cn' : 'localhost',
      },

      interpolation: {
        escapeValue: false, // React已经默认转义
      },

      // 开发环境显示调试信息
      debug: process.env.NODE_ENV === 'development',

      // 命名空间
      defaultNS: 'translation',
      ns: ['translation'],
    })
}

// 立即初始化
initI18n().catch(async error => {
  const resources = await getResourcesFromAPI()
  // 如果初始化失败，使用默认资源重新初始化
  i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources:resources ,
      lng: getDefaultLanguage(),
      fallbackLng: 'zh',
      detection: {
        order: ['cookie', 'navigator'],
        caches: ['cookie'],
        cookieMinutes: 60 * 24 * 30,
        cookieDomain: process.env.NODE_ENV === 'production' ? '.medsci.cn' : 'localhost',
      },
      interpolation: {
        escapeValue: false,
      },
      debug: process.env.NODE_ENV === 'development',
      defaultNS: 'translation',
      ns: ['translation'],
    })
})

// 语言切换函数
export const changeLanguage = (language: string) => {
  i18n.changeLanguage(language)
  Cookies.set('ai_apps_lang', language, {
    expires: 30,
    domain: process.env.NODE_ENV === 'production' ? '.medsci.cn' : 'localhost'
  })
}

// 重新加载翻译资源
export const reloadI18nResources = async () => {
  try {
    const resources = await getResourcesFromAPI()

    if (resources) {
      // 清除现有资源
      Object.keys(resources).forEach(lang => {
        i18n.removeResourceBundle(lang, 'translation')
      })

      // 添加新资源
      Object.keys(resources).forEach(lang => {
        const resourceData = (resources as any)[lang]
        if (resourceData && resourceData.translation) {
          i18n.addResourceBundle(lang, 'translation', resourceData.translation, true, true)
        }
      })

      console.log('I18n resources reloaded successfully')
      return true
    }
  } catch (error) {
    console.error('Failed to reload i18n resources:', error)
  }
  return false
}

export default i18n
