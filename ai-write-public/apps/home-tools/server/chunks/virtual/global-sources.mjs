const sources = [
    {
        "context": {
            "name": "sitemap:urls",
            "description": "Set with the `sitemap.urls` config."
        },
        "urls": [
            {
                "loc": "/",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 1
            },
            {
                "loc": "/pt",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/en",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/ar",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/tw",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/id",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/ja",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/ko",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/vi",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/ms",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/es",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "/idoc",
                "lastmod": "2025-08-05T07:28:04.338Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/elavax-pro",
                "lastmod": "2025-06-10T08:19:21.142Z",
                "priority": 0.8
            },
            {
                "loc": "/en/tool/ip-srch",
                "lastmod": "2024-11-25T02:09:52.395Z",
                "priority": 0.8
            },
            {
                "loc": "//write/rct-write",
                "lastmod": "2025-03-27T12:57:19.444Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/novax-pro",
                "lastmod": "2025-06-12T07:35:45.082Z",
                "priority": 0.8
            },
            {
                "loc": "/pt/tool/pt-lang",
                "lastmod": "2024-11-25T02:49:23.709Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/datascore-base",
                "lastmod": "2025-07-21T02:00:39.544Z",
                "priority": 0.8
            },
            {
                "loc": "/es/tool/esp-lang",
                "lastmod": "2024-11-25T05:19:12.494Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/paper-out",
                "lastmod": "2025-02-06T09:35:30.836Z",
                "priority": 0.8
            },
            {
                "loc": "/tw/chat/test-0703",
                "lastmod": "2025-07-16T08:28:43.652Z",
                "priority": 0.8
            },
            {
                "loc": "/en/chat/elavax-base",
                "lastmod": "2025-06-10T02:58:39.305Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/user-guide",
                "lastmod": "2024-11-21T02:24:01.229Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/dify-bind",
                "lastmod": "2025-04-21T03:46:56.349Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/lit-review",
                "lastmod": "2025-02-20T08:41:45.858Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/career-guide",
                "lastmod": "2025-02-05T05:50:18.687Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/mes-info",
                "lastmod": "2025-03-10T03:49:05.120Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/medsci-qa",
                "lastmod": "2025-02-20T08:37:32.187Z",
                "priority": 0.8
            },
            {
                "loc": "/en/write/med-write",
                "lastmod": "2024-11-06T07:14:46.190Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/medsci-ask",
                "lastmod": "2025-02-21T06:39:49.517Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/tool-test",
                "lastmod": "2024-11-22T02:06:33.782Z",
                "priority": 0.8
            },
            {
                "loc": "/en/write/rev-wrt",
                "lastmod": "2024-11-20T07:19:17.317Z",
                "priority": 0.8
            },
            {
                "loc": "/en/chat/en-test",
                "lastmod": "2024-11-21T02:24:41.558Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/elavax-base",
                "lastmod": "2025-06-09T08:24:12.782Z",
                "priority": 0.8
            },
            {
                "loc": "/en/chat/novax-pro",
                "lastmod": "2025-06-12T07:35:45.082Z",
                "priority": 0.8
            },
            {
                "loc": "/en/chat/novax-base",
                "lastmod": "2025-06-12T07:34:49.939Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/smart-fix",
                "lastmod": "2024-11-25T03:37:31.214Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/txt-test",
                "lastmod": "2025-03-20T05:39:21.026Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/dev-guide",
                "lastmod": "2024-11-26T05:48:06.727Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/deep-think",
                "lastmod": "2025-04-18T08:33:41.848Z",
                "priority": 0.8
            },
            {
                "loc": "/ar/tool/arab-app",
                "lastmod": "2024-11-25T01:42:41.036Z",
                "priority": 0.8
            },
            {
                "loc": "/vi/tool/vn-expand",
                "lastmod": "2024-11-25T03:38:55.674Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/health-rep",
                "lastmod": "2025-01-15T05:39:03.819Z",
                "priority": 0.8
            },
            {
                "loc": "/ms/chat/my-lang",
                "lastmod": "2024-12-13T07:47:26.253Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/auto-trans",
                "lastmod": "2025-03-03T06:03:28.251Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/rare-disease",
                "lastmod": "2025-01-09T08:03:22.240Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/pub-rec",
                "lastmod": "2025-01-20T08:13:46.524Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/pro-trans",
                "lastmod": "2025-01-09T01:10:12.449Z",
                "priority": 0.8
            },
            {
                "loc": "/id/tool/indo-exp",
                "lastmod": "2024-12-13T07:36:16.948Z",
                "priority": 0.8
            },
            {
                "loc": "//write/write-pay",
                "lastmod": "2025-04-25T05:06:27.198Z",
                "priority": 0.8
            },
            {
                "loc": "/en/tool/tl-test",
                "lastmod": "2024-11-22T02:07:58.031Z",
                "priority": 0.8
            },
            {
                "loc": "/tw/write/rct-wr2",
                "lastmod": "2024-11-22T10:41:42.747Z",
                "priority": 0.8
            },
            {
                "loc": "/en/chat/elavax-pro",
                "lastmod": "2025-06-10T08:19:21.142Z",
                "priority": 0.8
            },
            {
                "loc": "//write/rev-write",
                "lastmod": "2024-11-20T07:17:26.787Z",
                "priority": 0.8
            },
            {
                "loc": "//chat/novax-base",
                "lastmod": "2025-06-12T07:34:49.939Z",
                "priority": 0.8
            },
            {
                "loc": "//tool/ip-query",
                "lastmod": "2024-11-25T01:55:19.029Z",
                "priority": 0.8
            }
        ],
        "sourceType": "user"
    },
    {
        "context": {
            "name": "nuxt:pages",
            "description": "Generated from your static page files.",
            "tips": [
                "Can be disabled with `{ excludeAppSources: ['nuxt:pages'] }`."
            ]
        },
        "urls": [
            {
                "loc": "/",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/en",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/tw",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/ar",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/es",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/id",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/ja",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/ko",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/pt",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/vi",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/ms",
                "alternatives": [
                    {
                        "hreflang": "zh-CN",
                        "href": "/"
                    },
                    {
                        "hreflang": "en-US",
                        "href": "/en"
                    },
                    {
                        "hreflang": "zh-TW",
                        "href": "/tw"
                    },
                    {
                        "hreflang": "ar-SA",
                        "href": "/ar"
                    },
                    {
                        "hreflang": "es-ES",
                        "href": "/es"
                    },
                    {
                        "hreflang": "id-ID",
                        "href": "/id"
                    },
                    {
                        "hreflang": "ja-JP",
                        "href": "/ja"
                    },
                    {
                        "hreflang": "ko-KR",
                        "href": "/ko"
                    },
                    {
                        "hreflang": "pt-BR",
                        "href": "/pt"
                    },
                    {
                        "hreflang": "vi-VN",
                        "href": "/vi"
                    },
                    {
                        "hreflang": "ms-MY",
                        "href": "/ms"
                    },
                    {
                        "hreflang": "x-default",
                        "href": "/"
                    }
                ]
            },
            {
                "loc": "/sign-up",
                "alternatives": []
            },
            {
                "loc": "/en/sign-up",
                "alternatives": []
            },
            {
                "loc": "/tw/sign-up",
                "alternatives": []
            },
            {
                "loc": "/ar/sign-up",
                "alternatives": []
            },
            {
                "loc": "/es/sign-up",
                "alternatives": []
            },
            {
                "loc": "/id/sign-up",
                "alternatives": []
            },
            {
                "loc": "/ja/sign-up",
                "alternatives": []
            },
            {
                "loc": "/ko/sign-up",
                "alternatives": []
            },
            {
                "loc": "/pt/sign-up",
                "alternatives": []
            },
            {
                "loc": "/vi/sign-up",
                "alternatives": []
            },
            {
                "loc": "/ms/sign-up",
                "alternatives": []
            },
            {
                "loc": "/login",
                "alternatives": []
            },
            {
                "loc": "/en/login",
                "alternatives": []
            },
            {
                "loc": "/tw/login",
                "alternatives": []
            },
            {
                "loc": "/ar/login",
                "alternatives": []
            },
            {
                "loc": "/es/login",
                "alternatives": []
            },
            {
                "loc": "/id/login",
                "alternatives": []
            },
            {
                "loc": "/ja/login",
                "alternatives": []
            },
            {
                "loc": "/ko/login",
                "alternatives": []
            },
            {
                "loc": "/pt/login",
                "alternatives": []
            },
            {
                "loc": "/vi/login",
                "alternatives": []
            },
            {
                "loc": "/ms/login",
                "alternatives": []
            },
            {
                "loc": "/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/en/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/tw/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/ar/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/es/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/id/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/ja/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/ko/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/pt/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/vi/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/ms/tool/destroy",
                "alternatives": []
            },
            {
                "loc": "/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/en/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/tw/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/ar/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/es/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/id/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/ja/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/ko/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/pt/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/vi/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/ms/tool/privacy-policy",
                "alternatives": []
            },
            {
                "loc": "/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/en/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/tw/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/ar/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/es/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/id/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/ja/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/ko/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/pt/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/vi/tool/components/InputField",
                "alternatives": []
            },
            {
                "loc": "/ms/tool/components/InputField",
                "alternatives": []
            }
        ],
        "sourceType": "app"
    }
];

export { sources };
//# sourceMappingURL=global-sources.mjs.map
