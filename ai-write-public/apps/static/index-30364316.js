import{a as e,_ as l}from"./Editor-70dc8876.js";/* empty css                  *//* empty css                    *//* empty css                  *//* empty css                 */import{m as a}from"./index-25c601ba.js";import{s as t}from"./index-3fcc390c.js";/* empty css                   */import{a as s,r as o,O as n,o as i,d as r,e as d,j as u,f as m,g as p,q as c,m as f,k as v,S as x,t as g,F as y,z as h,i as b,h as V,s as j,a6 as S,C as _,E as k,ah as w,aL as z,aM as C}from"./index-6e33a8f3.js";/* empty css                  *//* empty css                  */const M={class:"flex h-full overflow-auto"},T={class:"w-[58%]"},N={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},U={class:"flex items-center my-8"},H={class:"flex items-center mr-4"},L={class:"flex items-center mr-4"},W={class:"mr-2"},q=["innerHTML","onClick"],$=["innerHTML"],E={class:"flex-1 ml-4 box-right"},F=s({__name:"index",setup(s){const F=o("literature retrieval"),G=o(""),I=o(1),O=o(!0),P=o([]),A=o({}),B=o("请输入关键词或短句（中/英）"),D=n({pageNo:1,pageSize:20}),J=o(344),K=e=>{if(!F.value)return S.warning("请输入关键词或短句");1==e&&(D.pageNo=1);let l=P.value.map((e=>"cns"==e?{field:"cns",opt:2,vals:[e],val:"",synonymsWordVos:[]}:{field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]}));a("review",{key:F.value,page:D.pageNo-1,size:D.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l}).then((e=>{e&&e.data&&(A.value=e.data,A.value.content=t(A.value.content))}))},Q=()=>{I.value++};return i((()=>{K(),J.value=document.querySelector(".box-right").offsetWidth,window.onresize=()=>{J.value=document.querySelector(".box-right").offsetWidth}})),(a,t)=>{const s=_,o=k,n=w,i=z,S=C,R=e,X=l;return r(),d("div",M,[u("div",T,[u("div",N,[m(s,{class:"h-full !text-[24px]",modelValue:p(F),"onUpdate:modelValue":t[0]||(t[0]=e=>c(F)?F.value=e:null),placeholder:p(B),clearable:""},null,8,["modelValue","placeholder"]),m(o,{type:"primary",onClick:t[1]||(t[1]=e=>K(1))},{default:f((()=>t[7]||(t[7]=[v("查 询")]))),_:1})]),u("div",U,[u("div",H,[u("span",{class:x(["mr-2",p(O)?"text-[#409eff]":""])},"翻译",2),m(n,{modelValue:p(O),"onUpdate:modelValue":t[2]||(t[2]=e=>c(O)?O.value=e:null)},null,8,["modelValue"])]),u("div",L,[t[12]||(t[12]=u("span",{class:"mr-2"},"影响因子：",-1)),m(S,{modelValue:p(P),"onUpdate:modelValue":t[3]||(t[3]=e=>c(P)?P.value=e:null)},{default:f((()=>[m(i,{label:"3"},{default:f((()=>t[8]||(t[8]=[v(g("<3分"))]))),_:1}),m(i,{label:"5"},{default:f((()=>t[9]||(t[9]=[v("3-10分")]))),_:1}),m(i,{label:"10"},{default:f((()=>t[10]||(t[10]=[v(g(">10分"))]))),_:1}),m(i,{label:"cns"},{default:f((()=>t[11]||(t[11]=[v(g("CNS"))]))),_:1})])),_:1},8,["modelValue"])])]),u("div",null,[(r(!0),d(y,null,h(p(A).content,((e,l)=>(r(),d("div",{class:"flex mb-8",key:l},[u("div",W,g(l+1)+".",1),u("div",null,[u("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");G.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,q),p(O)?(r(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,$)):b("",!0)])])))),128))]),p(A)&&p(A).eleTotal?(r(),V(R,{key:0,class:"pb-10",total:p(A).eleTotal,page:p(D).pageNo,"onUpdate:page":t[4]||(t[4]=e=>p(D).pageNo=e),limit:p(D).pageSize,"onUpdate:limit":t[5]||(t[5]=e=>p(D).pageSize=e),onPagination:K},null,8,["total","page","limit"])):b("",!0)]),u("div",E,[(r(),V(X,{class:"h-[380px] fixed z-99",style:j({width:p(J)+"px"}),modelValue:p(G),"onUpdate:modelValue":t[6]||(t[6]=e=>c(G)?G.value=e:null),onClear:Q,key:p(I)},null,8,["style","modelValue"]))])])}}},[["__scopeId","data-v-300433dc"]]);export{F as default};
