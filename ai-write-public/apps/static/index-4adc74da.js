/* empty css                  *//* empty css                     *//* empty css                 */import{a as e,b as o,u as s,W as l,r as t,O as n,y as a,o as i,aE as c,d as r,e as d,j as u,t as p,f as m,m as g,k as f,g as I,aF as _,aG as v,C as k,aH as h,E as B,aI as y,aJ as b,a9 as w,aa as x}from"./index-ac66b6ea.js";const S=e=>(w("data-v-********"),e=e(),x(),e),T={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},U={class:"cl-rootBox cl-signUp-root justify-center"},j={class:"cl-cardBox cl-signUp-start"},$={class:"cl-card cl-signUp-start"},O={class:"cl-header"},A={class:"cl-headerTitle"},J={class:"cl-headerSubtitle"},C={class:"cl-main"},N={class:"cl-socialButtonsRoot"},z={class:"cl-socialButtons"},V={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},q={class:"cl-socialButtonsBlockButton-d"},L=S((()=>u("span",null,[u("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),P={class:"cl-socialButtons"},R={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},E={class:"cl-socialButtonsBlockButton-d"},G=S((()=>u("span",null,[u("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),W={class:"cl-dividerRow"},F=S((()=>u("div",{class:"cl-dividerLine"},null,-1))),H={class:"cl-dividerText"},M=S((()=>u("div",{class:"cl-dividerLine"},null,-1))),Z={class:"cl-socialButtonsRoot"},Q={class:"cl-internal-1pnppin"},D=S((()=>u("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1))),K={class:"cl-internal-742eeh"},X={class:"cl-internal-2iusy0"},Y=S((()=>u("svg",{class:"cl-buttonArrowIcon cl-internal-1c4ikgf"},[u("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))),ee={class:"cl-footer cl-internal-4x6jej"},oe={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},se={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},le=e({__name:"index",setup(e){var w;const{t:x}=o(),S=s(),le=l(),te=S.params.socialType,ne=S.query.authCode,ae=S.query.authState,ie=t({email:"",password:""}),ce=(null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn")),re=t(),de=n({password:[{required:!0,message:x("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:x("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:x("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],email:[{required:!0,message:x("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:x("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),ue=a.get("userInfo")?null==(w=JSON.parse(a.get("userInfo")))?void 0:w.userId:"",pe=e=>{_(e).then((e=>{window.location.href=e}))},me=async e=>{e&&await e.validate(((e,o)=>{e&&v(ie.value).then((e=>{if((null==e?void 0:e.token)&&(null==e?void 0:e.htoken)){localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):a.set("userInfo",JSON.stringify(e.userInfo),{expires:365});const o=window.sessionStorage.getItem("redirectUrl");o?(window.location.href=o,window.sessionStorage.removeItem("redirectUrl")):this.$router.push("/")}}))}))};return i((()=>{ue?le.push("/"):te&&ne&&ae&&c(te,ne,ae).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):a.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),le.push("/"))}))})),(e,o)=>{const s=k,l=h,t=B,n=y,a=b;return r(),d("div",T,[u("div",U,[u("div",j,[u("div",$,[u("div",O,[u("div",null,[u("h1",A,p(e.$t("tool.login_to_MedSci_xAI")),1),u("p",J,p(e.$t("tool.welcome_back_please_login_to_continue")),1)])]),u("div",C,[u("div",N,[u("div",z,[u("button",V,[u("span",q,[L,u("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[0]||(o[0]=e=>pe(35))},p(e.$t("tool.continue_with_google")),1)])])]),u("div",P,[u("button",R,[u("span",E,[G,u("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[1]||(o[1]=e=>pe(36))},p(e.$t("tool.continue_with_facebook")),1)])])])]),u("div",W,[F,u("p",H,p(e.$t("tool.or")),1),M]),u("div",Z,[m(n,{ref_key:"ruleFormRef",ref:re,style:{"max-width":"600px"},model:ie.value,rules:de,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:g((()=>[m(l,{label:e.$t("tool.email"),prop:"email"},{default:g((()=>[m(s,{modelValue:ie.value.email,"onUpdate:modelValue":o[2]||(o[2]=e=>ie.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),m(l,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:g((()=>[m(s,{modelValue:ie.value.password,"onUpdate:modelValue":o[3]||(o[3]=e=>ie.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),m(l,null,{default:g((()=>[u("div",Q,[D,u("div",K,[m(t,{class:"cl-formButtonPrimary cl-button cl-internal-ttumny",onClick:o[4]||(o[4]=e=>me(re.value))},{default:g((()=>[u("span",X,[f(p(e.$t("tool.continue")),1),Y])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),u("div",ee,[u("div",oe,[u("span",se,p(e.$t("tool.no_account_yet")),1),m(a,{href:I(ce)?"/apps/sign-up":"/sign-up",class:"cl-footerActionLink"},{default:g((()=>[f(p(e.$t("tool.signUp")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-********"]]);export{le as default};
