/* empty css                  */import{_ as e,c as i}from"./index-16d5b466.js";import{_ as a}from"./_plugin-vue_export-helper-1b428a4d.js";import{M as s,r as o,O as r,e as n,w as l,U as c,c as d,d as u,h as p,i as h,t as m,ae as g,af as f,a as v,V as y,L as w,aj as b,o as k,f as x,q as C,F as _,y as I,$ as T,S as A,T as S,j as P,l as B,a2 as D,ag as L,ak as E,E as M,a4 as N,X as O,ai as R,al as U,x as $,am as j,b as F,g as z,u as H,a3 as G,an as q,ao as V,s as J,k as W,Z as Q,p as X,ac as K,ap as Y,Y as Z,aq as ee,G as te,B as ie,ar as ae,as as se,at as oe,v as re}from"./index-0ee7a7ea.js";/* empty css                 *//* empty css                *//* empty css                   *//* empty css                   */import{a as ne,M as le,n as ce,e as de,D as ue,t as pe,E as he,L as me,x as ge,U as fe,N as ve,p as ye}from"./use-touch-84cdbcb6.js";import{I as we,T as be,P as ke}from"./index-faaaa110.js";import{g as xe}from"./index-23147e67.js";const[Ce,_e]=ne("checkbox-group"),Ie=Symbol(Ce),Te={name:le,disabled:Boolean,iconSize:ce,modelValue:le,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var Ae=s({props:de({},Te,{bem:ue(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:pe,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(e,{emit:t,slots:i}){const a=o(),s=t=>{if(e.parent&&e.bindGroup)return e.parent.props[t]},l=r((()=>{if(e.parent&&e.bindGroup){const t=s("disabled")||e.disabled;if("checkbox"===e.role){const i=s("modelValue").length,a=s("max");return t||a&&i>=+a&&!e.checked}return t}return e.disabled})),c=r((()=>s("direction"))),d=r((()=>{const t=e.checkedColor||s("checkedColor");if(t&&e.checked&&!l.value)return{borderColor:t,backgroundColor:t}})),u=r((()=>e.shape||s("shape")||"round")),p=i=>{const{target:s}=i,o=a.value,r=o===s||(null==o?void 0:o.contains(s));l.value||!r&&e.labelDisabled||t("toggle"),t("click",i)},h=()=>{var t,o;const{bem:r,checked:c,indeterminate:p}=e,h=e.iconSize||s("iconSize");return n("div",{ref:a,class:r("icon",[u.value,{disabled:l.value,checked:c,indeterminate:p}]),style:"dot"!==u.value?{fontSize:he(h)}:{width:he(h),height:he(h),borderColor:null==(t=d.value)?void 0:t.borderColor}},[i.icon?i.icon({checked:c,disabled:l.value}):"dot"!==u.value?n(we,{name:p?"minus":"success",style:d.value},null):n("div",{class:r("icon--dot__icon"),style:{backgroundColor:null==(o=d.value)?void 0:o.backgroundColor}},null)])},m=()=>{const{checked:t}=e;if(i.default)return n("span",{class:e.bem("label",[e.labelPosition,{disabled:l.value}])},[i.default({checked:t,disabled:l.value})])};return()=>{const t="left"===e.labelPosition?[m(),h()]:[h(),m()];return n("div",{role:e.role,class:e.bem([{disabled:l.value,"label-disabled":e.labelDisabled},c.value]),tabindex:l.value?void 0:0,"aria-checked":e.checked,onClick:p},[t])}}});const[Se,Pe]=ne("checkbox");const Be=ye(s({name:Se,props:de({},Te,{shape:String,bindGroup:pe,indeterminate:{type:Boolean,default:null}}),emits:["change","update:modelValue"],setup(e,{emit:t,slots:i}){const{parent:a}=me(Ie),s=r((()=>a&&e.bindGroup?-1!==a.props.modelValue.indexOf(e.name):!!e.modelValue)),o=(i=!s.value)=>{a&&e.bindGroup?(t=>{const{name:i}=e,{max:s,modelValue:o}=a.props,r=o.slice();if(t)s&&r.length>=+s||r.includes(i)||(r.push(i),e.bindGroup&&a.updateValue(r));else{const t=r.indexOf(i);-1!==t&&(r.splice(t,1),e.bindGroup&&a.updateValue(r))}})(i):t("update:modelValue",i),null!==e.indeterminate&&t("change",i)};return l((()=>e.modelValue),(i=>{null===e.indeterminate&&t("change",i)})),ge({toggle:o,props:e,checked:s}),fe((()=>e.modelValue)),()=>n(Ae,c({bem:Pe,role:"checkbox",parent:a,checked:s.value,onToggle:o},e),ve(i,["default","icon"]))}})),De={name:"FooterNav",data:()=>({showFooter:!1}),head(){},computed:{},mounted(){jQuery(document).ready((function(){jQuery("#footOwl").owlCarousel({items:3,margin:17,responsiveClass:!0,dots:!0,loop:!0,autoplay:!0,autoplayTimeout:1e4,autoplayHoverPause:!0,responsive:{}})}))},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},Le=e=>(g("data-v-9c198f31"),e=e(),f(),e),Ee={class:"bg-[#F7F7F7] bg"},Me={key:0,id:"footer"},Ne=[Le((()=>h("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[h("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[h("div",{class:"widget-split item phone-hidden"},[h("div",{class:"widget ms-footer-img"},[h("div",null,[h("p",null,[h("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),h("p",{class:"bold w-footer-bold"},"梅斯医学MedSci-临床医生发展平台"),h("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),h("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[h("div",{class:"widget"},[h("h3",{class:"w-footer-h3"},"关于我们"),h("div",{class:"clearfix"},[h("ul",{class:"menu left"},[h("li",{class:"ms-link iconfont"},[h("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),h("li",{class:"ms-link iconfont"},[h("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),h("li",{class:"ms-link iconfont"},[h("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),h("li",{class:"ms-link iconfont"},[h("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),h("li",{class:"ms-link iconfont"},[h("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),h("li",{class:"ms-link iconfont"},[h("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),h("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[h("div",{class:"widget"},[h("h3",{class:"w-footer-h3"},"我们的业务"),h("div",{class:"clearfix"},[h("ul",{class:"menu left"},[h("li",{class:"ms-link iconfont"},[h("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),h("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[h("div",{class:"widget"},[h("h3",{class:"w-footer-h3"},"我们的产品"),h("div",{class:"clearfix"},[h("ul",{class:"menu left"},[h("li",{class:"ms-link iconfont"},[h("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),h("li",{class:"ms-link iconfont"},[h("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),h("div",{class:"w-footer-right phone-hidden"},[h("div",{class:"widget"},[h("h3",{class:"w-footer-h3"},"新媒体矩阵"),h("div",{id:"footOwl",class:"owl-carousel"},[h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),h("span",null,"梅斯医学")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),h("span",null,"肿瘤新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),h("span",null,"血液新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),h("span",null,"风湿新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),h("span",null,"呼吸新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),h("span",null,"皮肤新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),h("span",null,"神经新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),h("span",null,"消化新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),h("span",null,"心血管新前沿")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),h("span",null,"生物谷")]),h("div",{class:"item w-owl-item"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),h("span",null,"MedSci App")])])])])])],-1)))],Oe={class:"footer-copyright ms-footer-copy w-footer-copy"},Re={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},Ue=Le((()=>h("span",{style:{margin:"0px 20px"}},"|",-1))),$e={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};const je=a(De,[["render",function(e,t,i,a,s,o){return d(),u("footer",Ee,[s.showFooter?(d(),u("div",Me,Ne)):p("",!0),h("div",Oe,[h("p",null,[h("a",Re,m(e.$t("market.privacyPolicy")),1),Ue,h("a",$e,m(e.$t("market.termService")),1)])])])}],["__scopeId","data-v-9c198f31"]]);function Fe(e){return""===e?e:"true"===e||"1"==e}function ze(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function He(e,t){for(var i,a="",s=0,o=-1,r=0,n=0;n<=e.length;++n){if(n<e.length)i=e.charCodeAt(n);else{if(47===i)break;i=47}if(47===i){if(o===n-1||1===r);else if(o!==n-1&&2===r){if(a.length<2||2!==s||46!==a.charCodeAt(a.length-1)||46!==a.charCodeAt(a.length-2))if(a.length>2){var l=a.lastIndexOf("/");if(l!==a.length-1){-1===l?(a="",s=0):s=(a=a.slice(0,l)).length-1-a.lastIndexOf("/"),o=n,r=0;continue}}else if(2===a.length||1===a.length){a="",s=0,o=n,r=0;continue}t&&(a.length>0?a+="/..":a="..",s=2)}else a.length>0?a+="/"+e.slice(o+1,n):a=e.slice(o+1,n),s=n-o-1;o=n,r=0}else 46===i&&-1!==r?++r:r=-1}return a}var Ge={resolve:function(){for(var e,t="",i=!1,a=arguments.length-1;a>=-1&&!i;a--){var s;a>=0?s=arguments[a]:(void 0===e&&(e=process.cwd()),s=e),ze(s),0!==s.length&&(t=s+"/"+t,i=47===s.charCodeAt(0))}return t=He(t,!i),i?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(e){if(ze(e),0===e.length)return".";var t=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=He(e,!t)).length||t||(e="."),e.length>0&&i&&(e+="/"),t?"/"+e:e},isAbsolute:function(e){return ze(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var i=arguments[t];ze(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":Ge.normalize(e)},relative:function(e,t){if(ze(e),ze(t),e===t)return"";if((e=Ge.resolve(e))===(t=Ge.resolve(t)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var a=e.length,s=a-i,o=1;o<t.length&&47===t.charCodeAt(o);++o);for(var r=t.length-o,n=s<r?s:r,l=-1,c=0;c<=n;++c){if(c===n){if(r>n){if(47===t.charCodeAt(o+c))return t.slice(o+c+1);if(0===c)return t.slice(o+c)}else s>n&&(47===e.charCodeAt(i+c)?l=c:0===c&&(l=0));break}var d=e.charCodeAt(i+c);if(d!==t.charCodeAt(o+c))break;47===d&&(l=c)}var u="";for(c=i+l+1;c<=a;++c)c!==a&&47!==e.charCodeAt(c)||(0===u.length?u+="..":u+="/..");return u.length>0?u+t.slice(o+l):(o+=l,47===t.charCodeAt(o)&&++o,t.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(ze(e),0===e.length)return".";for(var t=e.charCodeAt(0),i=47===t,a=-1,s=!0,o=e.length-1;o>=1;--o)if(47===(t=e.charCodeAt(o))){if(!s){a=o;break}}else s=!1;return-1===a?i?"/":".":i&&1===a?"//":e.slice(0,a)},basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');ze(e);var i,a=0,s=-1,o=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var r=t.length-1,n=-1;for(i=e.length-1;i>=0;--i){var l=e.charCodeAt(i);if(47===l){if(!o){a=i+1;break}}else-1===n&&(o=!1,n=i+1),r>=0&&(l===t.charCodeAt(r)?-1==--r&&(s=i):(r=-1,s=n))}return a===s?s=n:-1===s&&(s=e.length),e.slice(a,s)}for(i=e.length-1;i>=0;--i)if(47===e.charCodeAt(i)){if(!o){a=i+1;break}}else-1===s&&(o=!1,s=i+1);return-1===s?"":e.slice(a,s)},extname:function(e){ze(e);for(var t=-1,i=0,a=-1,s=!0,o=0,r=e.length-1;r>=0;--r){var n=e.charCodeAt(r);if(47!==n)-1===a&&(s=!1,a=r+1),46===n?-1===t?t=r:1!==o&&(o=1):-1!==t&&(o=-1);else if(!s){i=r+1;break}}return-1===t||-1===a||0===o||1===o&&t===a-1&&t===i+1?"":e.slice(t,a)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var i=t.dir||t.root,a=t.base||(t.name||"")+(t.ext||"");return i?i===t.root?i+a:i+e+a:a}("/",e)},parse:function(e){ze(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var i,a=e.charCodeAt(0),s=47===a;s?(t.root="/",i=1):i=0;for(var o=-1,r=0,n=-1,l=!0,c=e.length-1,d=0;c>=i;--c)if(47!==(a=e.charCodeAt(c)))-1===n&&(l=!1,n=c+1),46===a?-1===o?o=c:1!==d&&(d=1):-1!==o&&(d=-1);else if(!l){r=c+1;break}return-1===o||-1===n||0===d||1===d&&o===n-1&&o===r+1?-1!==n&&(t.base=t.name=0===r&&s?e.slice(1,n):e.slice(r,n)):(0===r&&s?(t.name=e.slice(1,o),t.base=e.slice(1,n)):(t.name=e.slice(r,o),t.base=e.slice(r,n)),t.ext=e.slice(o,n)),r>0?t.dir=e.slice(0,r-1):s&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};Ge.posix=Ge;const qe=Ge.extname,Ve=Ge.basename;class Je{constructor(){let e="undefined"==typeof global,t="image/png",i="image/jpeg",a="image/jpeg",s="image/webp",o="application/pdf",r="image/svg+xml";Object.assign(this,{toMime:this.toMime.bind(this),fromMime:this.fromMime.bind(this),expected:e?'"png", "jpg", or "webp"':'"png", "jpg", "pdf", or "svg"',formats:e?{png:t,jpg:i,jpeg:a,webp:s}:{png:t,jpg:i,jpeg:a,pdf:o,svg:r},mimes:e?{[t]:"png",[i]:"jpg",[s]:"webp"}:{[t]:"png",[i]:"jpg",[o]:"pdf",[r]:"svg"}})}toMime(e){return this.formats[(e||"").replace(/^\./,"").toLowerCase()]}fromMime(e){return this.mimes[e]}}class We{static for(e){return(new We).append(e).get()}constructor(){this.crc=-1}get(){return~this.crc}append(e){for(var t=0|this.crc,i=this.table,a=0,s=0|e.length;a<s;a++)t=t>>>8^i[255&(t^e[a])];return this.crc=t,this}}function Qe(e){let t=new Uint8Array(e),i=new DataView(t.buffer),a={array:t,view:i,size:e,set8:(e,t)=>(i.setUint8(e,t),a),set16:(e,t)=>(i.setUint16(e,t,!0),a),set32:(e,t)=>(i.setUint32(e,t,!0),a),bytes:(e,i)=>(t.set(i,e),a)};return a}We.prototype.table=(()=>{var e,t,i,a=[];for(e=0;e<256;e++){for(i=e,t=0;t<8;t++)i=1&i?i>>>1^3988292384:i>>>1;a[e]=i}return a})();class Xe{constructor(e){let t=new Date;Object.assign(this,{directory:e,offset:0,files:[],time:(t.getHours()<<6|t.getMinutes())<<5|t.getSeconds()/2,date:(t.getFullYear()-1980<<4|t.getMonth()+1)<<5|t.getDate()}),this.add(e)}async add(e,t){let i=!t,a=Xe.encoder.encode(`${this.directory}/${i?"":e}`),s=new Uint8Array(i?0:await t.arrayBuffer()),o=30+a.length,r=o+s.length,{offset:n}=this,l=Qe(26).set32(0,134742036).set16(6,this.time).set16(8,this.date).set32(10,We.for(s)).set32(14,s.length).set32(18,s.length).set16(22,a.length);n+=o;let c=Qe(o+s.length+16).set32(0,67324752).bytes(4,l.array).bytes(30,a).bytes(o,s);n+=s.length,c.set32(r,134695760).bytes(r+4,l.array.slice(10,22)),n+=16,this.files.push({offset:n,folder:i,name:a,header:l,payload:c}),this.offset=n}toBuffer(){let e=this.files.reduce(((e,{name:t})=>46+t.length+e),0),t=Qe(e+22),i=0;for(var{offset:a,name:s,header:o,folder:r}of this.files)t.set32(i,33639248).set16(i+4,20).bytes(i+6,o.array).set8(i+38,r?16:0).set32(i+42,a).bytes(i+46,s),i+=46+s.length;t.set32(i,101010256).set16(i+8,this.files.length).set16(i+10,this.files.length).set32(i+12,e).set32(i+16,this.offset);let n=new Uint8Array(this.offset+t.size),l=0;for(var{payload:c}of this.files)n.set(c.array,l),l+=c.size;return n.set(t.array,l),n}get blob(){return new Blob([this.toBuffer()],{type:"application/zip"})}}Xe.encoder=new TextEncoder;const Ke=(e,t,i,a)=>{if(a){let{width:t,height:i}=e,s=Object.assign(document.createElement("canvas"),{width:t,height:i}),o=s.getContext("2d");o.fillStyle=a,o.fillRect(0,0,t,i),o.drawImage(e,0,0),e=s}return new Promise(((a,s)=>e.toBlob(a,t,i)))},Ye=(e,t)=>{const i=window.URL.createObjectURL(t),a=document.createElement("a");a.style.display="none",a.href=i,a.setAttribute("download",e),void 0===a.download&&a.setAttribute("target","_blank"),document.body.appendChild(a),a.click(),document.body.removeChild(a),setTimeout((()=>window.URL.revokeObjectURL(i)),100)},Ze={asBuffer:(...e)=>Ke(...e).then((e=>e.arrayBuffer())),asDownload:async(e,t,i,a,s)=>{Ye(s,await Ke(e,t,i,a))},asZipDownload:async(e,t,i,a,s,o,r)=>{let n=Ve(s,".zip")||"archive",l=new Xe(n);await Promise.all(e.map((async(e,s)=>{let n=(e=>o.replace("{}",String(e+1).padStart(r,"0")))(s);await l.add(n,await Ke(e,t,i,a))}))),Ye(`${n}.zip`,l.blob)},atScale:(e,t,i)=>e.map((e=>{if(1==t&&!i)return e.canvas;let a=document.createElement("canvas"),s=a.getContext("2d"),o=e.canvas?e.canvas:e;return a.width=o.width*t,a.height=o.height*t,i&&(s.fillStyle=i,s.fillRect(0,0,a.width,a.height)),s.scale(t,t),s.drawImage(o,0,0),a})),options:function(e,{filename:t="",extension:i="",format:a,page:s,quality:o,matte:r,density:n,outline:l,archive:c}={}){var{fromMime:d,toMime:u,expected:p}=new Je,h=(c=c||"canvas",a||i.replace(/@\d+x$/i,"")||qe(t)),m=(a=d(u(h)||h),u(a)),g=e.length;if(!h)throw new Error("Cannot determine image format (use a filename extension or 'format' argument)");if(!a)throw new Error(`Unsupported file format "${h}" (expected ${p})`);if(!g)throw new RangeError("Canvas has no associated contexts (try calling getContext or newPage first)");let f,v,y=t.replace(/{(\d*)}/g,((e,t)=>(v=!0,t=parseInt(t,10),f=isFinite(t)?t:isFinite(f)?f:-1,"{}"))),w=s>0?s-1:s<0?g+s:void 0;if(isFinite(w)&&w<0||w>=g)throw new RangeError(1==g?`Canvas only has a ‘page 1’ (${w} is out of bounds)`:`Canvas has pages 1–${g} (${w} is out of bounds)`);if(e=isFinite(w)?[e[w]]:v||"pdf"==a?e:e.slice(-1),void 0===o)o=.92;else if("number"!=typeof o||!isFinite(o)||o<0||o>1)throw new TypeError("The quality option must be an number in the 0.0–1.0 range");if(void 0===n){let e=(i||Ve(t,h)).match(/@(\d+)x$/i);n=e?parseInt(e[1],10):1}else if("number"!=typeof n||!Number.isInteger(n)||n<1)throw new TypeError("The density option must be a non-negative integer");return void 0===l?l=!0:"svg"==a&&(l=!!l),{filename:t,pattern:y,format:a,mime:m,pages:e,padding:f,quality:o,matte:r,density:n,outline:l,archive:c}}},{asBuffer:et,asDownload:tt,asZipDownload:it,atScale:at,options:st}=Ze,ot=Symbol.for("toDataURL");const{CanvasRenderingContext2D:rt,CanvasGradient:nt,CanvasPattern:lt,Image:ct,ImageData:dt,Path2D:ut,DOMMatrix:pt,DOMRect:ht,DOMPoint:mt}=window,gt={Canvas:class{constructor(e,t){let i=document.createElement("canvas"),a=[];for(var[s,o]of(Object.defineProperty(i,"async",{value:!0,writable:!1,enumerable:!0}),Object.entries({png:()=>et(i,"image/png"),jpg:()=>et(i,"image/jpeg"),pages:()=>a.concat(i).map((e=>e.getContext("2d")))})))Object.defineProperty(i,s,{get:o});return Object.assign(i,{width:e,height:t,newPage(...e){var{width:t,height:s}=i,o=Object.assign(document.createElement("canvas"),{width:t,height:s});o.getContext("2d").drawImage(i,0,0),a.push(o);var[t,s]=e.length?e:[t,s];return Object.assign(i,{width:t,height:s}).getContext("2d")},saveAs(e,t){t="number"==typeof t?{quality:t}:t;let i=st(this.pages,{filename:e,...t}),{pattern:a,padding:s,mime:o,quality:r,matte:n,density:l,archive:c}=i,d=at(i.pages,l);return null==s?tt(d[0],o,r,n,e):it(d,o,r,n,c,a,s)},toBuffer(e="png",t={}){t="number"==typeof t?{quality:t}:t;let i=st(this.pages,{extension:e,...t}),{mime:a,quality:s,matte:o,pages:r,density:n}=i,l=at(r,n,o)[0];return et(l,a,s,o)},[ot]:i.toDataURL.bind(i),toDataURL(e="png",t={}){t="number"==typeof t?{quality:t}:t;let a=st(this.pages,{extension:e,...t}),{mime:s,quality:o,matte:r,pages:n,density:l}=a,c=at(n,l,r)[0],d=c[c===i?ot:"toDataURL"](s,o);return Promise.resolve(d)}})}},loadImage:e=>new Promise(((t,i)=>Object.assign(new ct,{crossOrigin:"Anonymous",onload:t,onerror:i,src:e}))),CanvasRenderingContext2D:rt,CanvasGradient:nt,CanvasPattern:lt,Image:ct,ImageData:dt,Path2D:ut,DOMMatrix:pt,DOMRect:ht,DOMPoint:mt},ft=(e,t,i={},a=i)=>{if(Array.isArray(t))t.forEach((t=>ft(e,t,i,a)));else if("function"==typeof t)t(e,i,a,ft);else{const s=Object.keys(t)[0];Array.isArray(t[s])?(a[s]={},ft(e,t[s],i,a[s])):a[s]=t[s](e,i,a,ft)}return i},vt=(e,t)=>(i,a,s,o)=>{t(i,a,s)&&o(i,e,a,s)},yt=(e=0)=>t=>t.data[t.pos+e],wt=e=>t=>t.data.subarray(t.pos,t.pos+=e),bt=e=>t=>t.data.subarray(t.pos,t.pos+e),kt=e=>t=>Array.from(wt(e)(t)).map((e=>String.fromCharCode(e))).join(""),xt=e=>t=>{const i=wt(2)(t);return e?(i[1]<<8)+i[0]:(i[0]<<8)+i[1]},Ct=(e,t)=>(i,a,s)=>{const o="function"==typeof t?t(i,a,s):t,r=wt(e),n=new Array(o);for(var l=0;l<o;l++)n[l]=r(i);return n},_t=e=>t=>{const i=(e=>e.data[e.pos++])(t),a=new Array(8);for(var s=0;s<8;s++)a[7-s]=!!(i&1<<s);return Object.keys(e).reduce(((t,i)=>{const s=e[i];return s.length?t[i]=((e,t,i)=>{for(var a=0,s=0;s<i;s++)a+=e[t+s]&&2**(i-s-1);return a})(a,s.index,s.length):t[i]=a[s.index],t}),{})};var It={blocks:e=>{const t=[],i=e.data.length;for(var a=0,s=(e=>e.data[e.pos++])(e);0!==s&&s;s=(e=>e.data[e.pos++])(e)){if(e.pos+s>=i){const s=i-e.pos;t.push(wt(s)(e)),a+=s;break}t.push(wt(s)(e)),a+=s}const o=new Uint8Array(a);for(var r=0,n=0;n<t.length;n++)o.set(t[n],r),r+=t[n].length;return o}};const Tt=vt({gce:[{codes:wt(2)},{byteSize:e=>e.data[e.pos++]},{extras:_t({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:xt(!0)},{transparentColorIndex:e=>e.data[e.pos++]},{terminator:e=>e.data[e.pos++]}]},(e=>{var t=bt(2)(e);return 33===t[0]&&249===t[1]})),At=vt({image:[{code:e=>e.data[e.pos++]},{descriptor:[{left:xt(!0)},{top:xt(!0)},{width:xt(!0)},{height:xt(!0)},{lct:_t({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},vt({lct:Ct(3,((e,t,i)=>Math.pow(2,i.descriptor.lct.size+1)))},((e,t,i)=>i.descriptor.lct.exists)),{data:[{minCodeSize:e=>e.data[e.pos++]},It]}]},(e=>44===yt()(e))),St=vt({text:[{codes:wt(2)},{blockSize:e=>e.data[e.pos++]},{preData:(e,t,i)=>wt(i.text.blockSize)(e)},It]},(e=>{var t=bt(2)(e);return 33===t[0]&&1===t[1]})),Pt=vt({application:[{codes:wt(2)},{blockSize:e=>e.data[e.pos++]},{id:(e,t,i)=>kt(i.blockSize)(e)},It]},(e=>{var t=bt(2)(e);return 33===t[0]&&255===t[1]})),Bt=vt({comment:[{codes:wt(2)},It]},(e=>{var t=bt(2)(e);return 33===t[0]&&254===t[1]})),Dt=[{header:[{signature:kt(3)},{version:kt(3)}]},{lsd:[{width:xt(!0)},{height:xt(!0)},{gct:_t({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:e=>e.data[e.pos++]},{pixelAspectRatio:e=>e.data[e.pos++]}]},vt({gct:Ct(3,((e,t)=>Math.pow(2,t.lsd.gct.size+1)))},((e,t)=>t.lsd.gct.exists)),{frames:(Lt=[Tt,Pt,Bt,At,St],Et=e=>{var t=yt()(e);return 33===t||44===t},(e,t,i,a)=>{const s=[];let o=e.pos;for(;Et(e,t,i);){const i={};if(a(e,Lt,t,i),e.pos===o)break;o=e.pos,s.push(i)}return s})}];var Lt,Et;const Mt=(e,t,i)=>{if(!e.image)return;const{image:a}=e,s=a.descriptor.width*a.descriptor.height;var o=((e,t,i)=>{const a=4096,s=i;var o,r,n,l,c,d,u,p,h,m;const g=new Array(i),f=new Array(a),v=new Array(a),y=new Array(4097);for(c=1+(r=1<<(m=e)),o=r+2,u=-1,n=(1<<(l=m+1))-1,p=0;p<r;p++)f[p]=0,v[p]=p;var w,b,k,x,C,_;for(w=b=k=x=C=_=0,h=0;h<s;){if(0===x){if(b<l){w+=t[_]<<b,b+=8,_++;continue}if(p=w&n,w>>=l,b-=l,p>o||p==c)break;if(p==r){n=(1<<(l=m+1))-1,o=r+2,u=-1;continue}if(-1==u){y[x++]=v[p],u=p,k=p;continue}for(d=p,p==o&&(y[x++]=k,p=u);p>r;)y[x++]=v[p],p=f[p];k=255&v[p],y[x++]=k,o<a&&(f[o]=u,v[o]=k,0==(++o&n)&&o<a&&(l++,n+=o)),u=d}x--,g[C++]=y[x],h++}for(h=C;h<s;h++)g[h]=0;return g})(a.data.minCodeSize,a.data.blocks,s);a.descriptor.lct.interlaced&&(o=((e,t)=>{const i=new Array(e.length),a=e.length/t,s=function(a,s){const o=e.slice(s*t,(s+1)*t);i.splice.apply(i,[a*t,t].concat(o))},o=[0,4,2,1],r=[8,8,4,2];for(var n=0,l=0;l<4;l++)for(var c=o[l];c<a;c+=r[l])s(c,n),n++;return i})(o,a.descriptor.width));const r={pixels:o,dims:{top:e.image.descriptor.top,left:e.image.descriptor.left,width:e.image.descriptor.width,height:e.image.descriptor.height}};return a.descriptor.lct&&a.descriptor.lct.exists?r.colorTable=a.lct:r.colorTable=t,e.gce&&(r.delay=10*(e.gce.delay||10),r.disposalType=e.gce.extras.disposal,e.gce.extras.transparentColorGiven&&(r.transparentIndex=e.gce.transparentColorIndex)),i&&(r.patch=(e=>{const t=e.pixels.length,i=new Uint8ClampedArray(4*t);for(var a=0;a<t;a++){const t=4*a,s=e.pixels[a],o=e.colorTable[s];i[t]=o[0],i[t+1]=o[1],i[t+2]=o[2],i[t+3]=s!==e.transparentIndex?255:0}return i})(r)),r};function Nt(e){var t=encodeURI(e).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return t.length+(t.length!=Number(e)?3:0)}class Ot{constructor(e){this.mode=$t.MODE_8BIT_BYTE,this.parsedData=[],this.data=e;const t=[];for(let i=0,a=this.data.length;i<a;i++){const e=[],a=this.data.charCodeAt(i);a>65536?(e[0]=240|(1835008&a)>>>18,e[1]=128|(258048&a)>>>12,e[2]=128|(4032&a)>>>6,e[3]=128|63&a):a>2048?(e[0]=224|(61440&a)>>>12,e[1]=128|(4032&a)>>>6,e[2]=128|63&a):a>128?(e[0]=192|(1984&a)>>>6,e[1]=128|63&a):e[0]=a,t.push(e)}this.parsedData=Array.prototype.concat.apply([],t),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}getLength(){return this.parsedData.length}write(e){for(let t=0,i=this.parsedData.length;t<i;t++)e.put(this.parsedData[t],8)}}class Rt{constructor(e=-1,t=Ut.L){this.moduleCount=0,this.dataList=[],this.typeNumber=e,this.errorCorrectLevel=t,this.moduleCount=0,this.dataList=[]}addData(e){if(this.typeNumber<=0)this.typeNumber=function(e,t){for(var i=1,a=Nt(e),s=0,o=Vt.length;s<o;s++){var r=0;switch(t){case Ut.L:r=Vt[s][0];break;case Ut.M:r=Vt[s][1];break;case Ut.Q:r=Vt[s][2];break;case Ut.H:r=Vt[s][3]}if(a<=r)break;i++}if(i>Vt.length)throw new Error("Too long data");return i}(e,this.errorCorrectLevel);else{if(this.typeNumber>40)throw new Error(`Invalid QR version: ${this.typeNumber}`);if(!function(e,t,i){const a=Nt(t),s=e-1;let o=0;switch(i){case Ut.L:o=Vt[s][0];break;case Ut.M:o=Vt[s][1];break;case Ut.Q:o=Vt[s][2];break;case Ut.H:o=Vt[s][3]}return a<=o}(this.typeNumber,e,this.errorCorrectLevel))throw new Error(`Data is too long for QR version: ${this.typeNumber}`)}const t=new Ot(e);this.dataList.push(t),this.dataCache=void 0}isDark(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(`${e},${t}`);return this.modules[e][t]}getModuleCount(){return this.moduleCount}make(){this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(let i=0;i<this.moduleCount;i++){this.modules[i]=new Array(this.moduleCount);for(let e=0;e<this.moduleCount;e++)this.modules[i][e]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=Rt.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)}setupPositionProbePattern(e,t){for(let i=-1;i<=7;i++)if(!(e+i<=-1||this.moduleCount<=e+i))for(let a=-1;a<=7;a++)t+a<=-1||this.moduleCount<=t+a||(this.modules[e+i][t+a]=0<=i&&i<=6&&(0==a||6==a)||0<=a&&a<=6&&(0==i||6==i)||2<=i&&i<=4&&2<=a&&a<=4)}getBestMaskPattern(){if(Number.isInteger(this.maskPattern)&&Object.values(jt).includes(this.maskPattern))return this.maskPattern;let e=0,t=0;for(let i=0;i<8;i++){this.makeImpl(!0,i);const a=Ft.getLostPoint(this);(0==i||e>a)&&(e=a,t=i)}return t}setupTimingPattern(){for(let e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(let e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)}setupPositionAdjustPattern(){const e=Ft.getPatternPosition(this.typeNumber);for(let t=0;t<e.length;t++)for(let i=0;i<e.length;i++){const a=e[t],s=e[i];if(null==this.modules[a][s])for(let e=-2;e<=2;e++)for(let t=-2;t<=2;t++)this.modules[a+e][s+t]=-2==e||2==e||-2==t||2==t||0==e&&0==t}}setupTypeNumber(e){const t=Ft.getBCHTypeNumber(this.typeNumber);for(var i=0;i<18;i++){var a=!e&&1==(t>>i&1);this.modules[Math.floor(i/3)][i%3+this.moduleCount-8-3]=a}for(i=0;i<18;i++){a=!e&&1==(t>>i&1);this.modules[i%3+this.moduleCount-8-3][Math.floor(i/3)]=a}}setupTypeInfo(e,t){const i=this.errorCorrectLevel<<3|t,a=Ft.getBCHTypeInfo(i);for(var s=0;s<15;s++){var o=!e&&1==(a>>s&1);s<6?this.modules[s][8]=o:s<8?this.modules[s+1][8]=o:this.modules[this.moduleCount-15+s][8]=o}for(s=0;s<15;s++){o=!e&&1==(a>>s&1);s<8?this.modules[8][this.moduleCount-s-1]=o:s<9?this.modules[8][15-s-1+1]=o:this.modules[8][15-s-1]=o}this.modules[this.moduleCount-8][8]=!e}mapData(e,t){let i=-1,a=this.moduleCount-1,s=7,o=0;for(let r=this.moduleCount-1;r>0;r-=2)for(6==r&&r--;;){for(let i=0;i<2;i++)if(null==this.modules[a][r-i]){let n=!1;o<e.length&&(n=1==(e[o]>>>s&1));Ft.getMask(t,a,r-i)&&(n=!n),this.modules[a][r-i]=n,s--,-1==s&&(o++,s=7)}if(a+=i,a<0||this.moduleCount<=a){a-=i,i=-i;break}}}static createData(e,t,i){const a=Gt.getRSBlocks(e,t),s=new qt;for(var o=0;o<i.length;o++){const t=i[o];s.put(t.mode,4),s.put(t.getLength(),Ft.getLengthInBits(t.mode,e)),t.write(s)}let r=0;for(o=0;o<a.length;o++)r+=a[o].dataCount;if(s.getLengthInBits()>8*r)throw new Error(`code length overflow. (${s.getLengthInBits()}>${8*r})`);for(s.getLengthInBits()+4<=8*r&&s.put(0,4);s.getLengthInBits()%8!=0;)s.putBit(!1);for(;!(s.getLengthInBits()>=8*r||(s.put(Rt.PAD0,8),s.getLengthInBits()>=8*r));)s.put(Rt.PAD1,8);return Rt.createBytes(s,a)}static createBytes(e,t){let i=0,a=0,s=0;const o=new Array(t.length),r=new Array(t.length);for(var n=0;n<t.length;n++){const c=t[n].dataCount,d=t[n].totalCount-c;a=Math.max(a,c),s=Math.max(s,d),o[n]=new Array(c);for(var l=0;l<o[n].length;l++)o[n][l]=255&e.buffer[l+i];i+=c;const u=Ft.getErrorCorrectPolynomial(d),p=new Ht(o[n],u.getLength()-1).mod(u);r[n]=new Array(u.getLength()-1);for(l=0;l<r[n].length;l++){const e=l+p.getLength()-r[n].length;r[n][l]=e>=0?p.get(e):0}}let c=0;for(l=0;l<t.length;l++)c+=t[l].totalCount;const d=new Array(c);let u=0;for(l=0;l<a;l++)for(n=0;n<t.length;n++)l<o[n].length&&(d[u++]=o[n][l]);for(l=0;l<s;l++)for(n=0;n<t.length;n++)l<r[n].length&&(d[u++]=r[n][l]);return d}}Rt.PAD0=236,Rt.PAD1=17;const Ut={L:1,M:0,Q:3,H:2},$t={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},jt={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};class Ft{static getBCHTypeInfo(e){let t=e<<10;for(;Ft.getBCHDigit(t)-Ft.getBCHDigit(Ft.G15)>=0;)t^=Ft.G15<<Ft.getBCHDigit(t)-Ft.getBCHDigit(Ft.G15);return(e<<10|t)^Ft.G15_MASK}static getBCHTypeNumber(e){let t=e<<12;for(;Ft.getBCHDigit(t)-Ft.getBCHDigit(Ft.G18)>=0;)t^=Ft.G18<<Ft.getBCHDigit(t)-Ft.getBCHDigit(Ft.G18);return e<<12|t}static getBCHDigit(e){let t=0;for(;0!=e;)t++,e>>>=1;return t}static getPatternPosition(e){return Ft.PATTERN_POSITION_TABLE[e-1]}static getMask(e,t,i){switch(e){case jt.PATTERN000:return(t+i)%2==0;case jt.PATTERN001:return t%2==0;case jt.PATTERN010:return i%3==0;case jt.PATTERN011:return(t+i)%3==0;case jt.PATTERN100:return(Math.floor(t/2)+Math.floor(i/3))%2==0;case jt.PATTERN101:return t*i%2+t*i%3==0;case jt.PATTERN110:return(t*i%2+t*i%3)%2==0;case jt.PATTERN111:return(t*i%3+(t+i)%2)%2==0;default:throw new Error(`bad maskPattern:${e}`)}}static getErrorCorrectPolynomial(e){let t=new Ht([1],0);for(let i=0;i<e;i++)t=t.multiply(new Ht([1,zt.gexp(i)],0));return t}static getLengthInBits(e,t){if(1<=t&&t<10)switch(e){case $t.MODE_NUMBER:return 10;case $t.MODE_ALPHA_NUM:return 9;case $t.MODE_8BIT_BYTE:case $t.MODE_KANJI:return 8;default:throw new Error(`mode:${e}`)}else if(t<27)switch(e){case $t.MODE_NUMBER:return 12;case $t.MODE_ALPHA_NUM:return 11;case $t.MODE_8BIT_BYTE:return 16;case $t.MODE_KANJI:return 10;default:throw new Error(`mode:${e}`)}else{if(!(t<41))throw new Error(`type:${t}`);switch(e){case $t.MODE_NUMBER:return 14;case $t.MODE_ALPHA_NUM:return 13;case $t.MODE_8BIT_BYTE:return 16;case $t.MODE_KANJI:return 12;default:throw new Error(`mode:${e}`)}}}static getLostPoint(e){const t=e.getModuleCount();let i=0;for(var a=0;a<t;a++)for(var s=0;s<t;s++){let o=0;const r=e.isDark(a,s);for(let i=-1;i<=1;i++)if(!(a+i<0||t<=a+i))for(let n=-1;n<=1;n++)s+n<0||t<=s+n||0==i&&0==n||r==e.isDark(a+i,s+n)&&o++;o>5&&(i+=3+o-5)}for(a=0;a<t-1;a++)for(s=0;s<t-1;s++){let t=0;e.isDark(a,s)&&t++,e.isDark(a+1,s)&&t++,e.isDark(a,s+1)&&t++,e.isDark(a+1,s+1)&&t++,0!=t&&4!=t||(i+=3)}for(a=0;a<t;a++)for(s=0;s<t-6;s++)e.isDark(a,s)&&!e.isDark(a,s+1)&&e.isDark(a,s+2)&&e.isDark(a,s+3)&&e.isDark(a,s+4)&&!e.isDark(a,s+5)&&e.isDark(a,s+6)&&(i+=40);for(s=0;s<t;s++)for(a=0;a<t-6;a++)e.isDark(a,s)&&!e.isDark(a+1,s)&&e.isDark(a+2,s)&&e.isDark(a+3,s)&&e.isDark(a+4,s)&&!e.isDark(a+5,s)&&e.isDark(a+6,s)&&(i+=40);let o=0;for(s=0;s<t;s++)for(a=0;a<t;a++)e.isDark(a,s)&&o++;return i+=10*(Math.abs(100*o/t/t-50)/5),i}}Ft.PATTERN_POSITION_TABLE=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],Ft.G15=1335,Ft.G18=7973,Ft.G15_MASK=21522;class zt{static glog(e){if(e<1)throw new Error(`glog(${e})`);return zt.LOG_TABLE[e]}static gexp(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return zt.EXP_TABLE[e]}}zt.EXP_TABLE=new Array(256),zt.LOG_TABLE=new Array(256),zt._constructor=function(){for(var e=0;e<8;e++)zt.EXP_TABLE[e]=1<<e;for(e=8;e<256;e++)zt.EXP_TABLE[e]=zt.EXP_TABLE[e-4]^zt.EXP_TABLE[e-5]^zt.EXP_TABLE[e-6]^zt.EXP_TABLE[e-8];for(e=0;e<255;e++)zt.LOG_TABLE[zt.EXP_TABLE[e]]=e}();class Ht{constructor(e,t){if(null==e.length)throw new Error(`${e.length}/${t}`);let i=0;for(;i<e.length&&0==e[i];)i++;this.num=new Array(e.length-i+t);for(let a=0;a<e.length-i;a++)this.num[a]=e[a+i]}get(e){return this.num[e]}getLength(){return this.num.length}multiply(e){const t=new Array(this.getLength()+e.getLength()-1);for(let i=0;i<this.getLength();i++)for(let a=0;a<e.getLength();a++)t[i+a]^=zt.gexp(zt.glog(this.get(i))+zt.glog(e.get(a)));return new Ht(t,0)}mod(e){if(this.getLength()-e.getLength()<0)return this;const t=zt.glog(this.get(0))-zt.glog(e.get(0)),i=new Array(this.getLength());for(var a=0;a<this.getLength();a++)i[a]=this.get(a);for(a=0;a<e.getLength();a++)i[a]^=zt.gexp(zt.glog(e.get(a))+t);return new Ht(i,0).mod(e)}}class Gt{constructor(e,t){this.totalCount=e,this.dataCount=t}static getRSBlocks(e,t){const i=Gt.getRsBlockTable(e,t);if(null==i)throw new Error(`bad rs block @ typeNumber:${e}/errorCorrectLevel:${t}`);const a=i.length/3,s=[];for(let o=0;o<a;o++){const e=i[3*o+0],t=i[3*o+1],a=i[3*o+2];for(let i=0;i<e;i++)s.push(new Gt(t,a))}return s}static getRsBlockTable(e,t){switch(t){case Ut.L:return Gt.RS_BLOCK_TABLE[4*(e-1)+0];case Ut.M:return Gt.RS_BLOCK_TABLE[4*(e-1)+1];case Ut.Q:return Gt.RS_BLOCK_TABLE[4*(e-1)+2];case Ut.H:return Gt.RS_BLOCK_TABLE[4*(e-1)+3];default:return}}}Gt.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];class qt{constructor(){this.buffer=[],this.length=0}get(e){const t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)}put(e,t){for(let i=0;i<t;i++)this.putBit(1==(e>>>t-i-1&1))}getLengthInBits(){return this.length}putBit(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}}const Vt=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];var Jt=256,Wt=1024,Qt=1<<18;function Xt(e,t){var i,a,s,o,r;function n(e,t,a,s,o){i[t][0]-=e*(i[t][0]-a)/Wt,i[t][1]-=e*(i[t][1]-s)/Wt,i[t][2]-=e*(i[t][2]-o)/Wt}function l(e,t,a,s,o){for(var n,l,c=Math.abs(t-e),d=Math.min(t+e,Jt),u=t+1,p=t-1,h=1;u<d||p>c;)l=r[h++],u<d&&((n=i[u++])[0]-=l*(n[0]-a)/Qt,n[1]-=l*(n[1]-s)/Qt,n[2]-=l*(n[2]-o)/Qt),p>c&&((n=i[p--])[0]-=l*(n[0]-a)/Qt,n[1]-=l*(n[1]-s)/Qt,n[2]-=l*(n[2]-o)/Qt)}function c(e,t,a){var r,n,l,c,d,u=~(1<<31),p=u,h=-1,m=h;for(r=0;r<Jt;r++)n=i[r],(l=Math.abs(n[0]-e)+Math.abs(n[1]-t)+Math.abs(n[2]-a))<u&&(u=l,h=r),(c=l-(s[r]>>12))<p&&(p=c,m=r),d=o[r]>>10,o[r]-=d,s[r]+=d<<10;return o[h]+=64,s[h]-=65536,m}this.buildColormap=function(){!function(){var e,t;for(i=[],a=new Int32Array(256),s=new Int32Array(Jt),o=new Int32Array(Jt),r=new Int32Array(32),e=0;e<Jt;e++)t=(e<<12)/Jt,i[e]=new Float64Array([t,t,t,0]),o[e]=256,s[e]=0}(),function(){var i,a,s,o,d,u,p=e.length,h=30+(t-1)/3,m=p/(3*t),g=~~(m/100),f=Wt,v=2048,y=v>>6;for(y<=1&&(y=0),i=0;i<y;i++)r[i]=f*(256*(y*y-i*i)/(y*y));p<1509?(t=1,a=3):a=p%499!=0?1497:p%491!=0?1473:p%487!=0?1461:1509;var w=0;for(i=0;i<m;)if(n(f,u=c(s=(255&e[w])<<4,o=(255&e[w+1])<<4,d=(255&e[w+2])<<4),s,o,d),0!==y&&l(y,u,s,o,d),(w+=a)>=p&&(w-=p),0===g&&(g=1),++i%g==0)for(f-=f/h,(y=(v-=v/30)>>6)<=1&&(y=0),u=0;u<y;u++)r[u]=f*(256*(y*y-u*u)/(y*y))}(),function(){for(var e=0;e<Jt;e++)i[e][0]>>=4,i[e][1]>>=4,i[e][2]>>=4,i[e][3]=e}(),function(){var e,t,s,o,r,n,l=0,c=0;for(e=0;e<Jt;e++){for(r=e,n=(s=i[e])[1],t=e+1;t<Jt;t++)(o=i[t])[1]<n&&(r=t,n=o[1]);if(o=i[r],e!=r&&(t=o[0],o[0]=s[0],s[0]=t,t=o[1],o[1]=s[1],s[1]=t,t=o[2],o[2]=s[2],s[2]=t,t=o[3],o[3]=s[3],s[3]=t),n!=l){for(a[l]=c+e>>1,t=l+1;t<n;t++)a[t]=e;l=n,c=e}}for(a[l]=c+255>>1,t=l+1;t<256;t++)a[t]=255}()},this.getColormap=function(){for(var e=[],t=[],a=0;a<Jt;a++)t[i[a][3]]=a;for(var s=0,o=0;o<Jt;o++){var r=t[o];e[s++]=i[r][0],e[s++]=i[r][1],e[s++]=i[r][2]}return e},this.lookupRGB=function(e,t,s){for(var o,r,n,l=1e3,c=-1,d=a[t],u=d-1;d<Jt||u>=0;)d<Jt&&((n=(r=i[d])[1]-t)>=l?d=Jt:(d++,n<0&&(n=-n),(o=r[0]-e)<0&&(o=-o),(n+=o)<l&&((o=r[2]-s)<0&&(o=-o),(n+=o)<l&&(l=n,c=r[3])))),u>=0&&((n=t-(r=i[u])[1])>=l?u=-1:(u--,n<0&&(n=-n),(o=r[0]-e)<0&&(o=-o),(n+=o)<l&&((o=r[2]-s)<0&&(o=-o),(n+=o)<l&&(l=n,c=r[3]))));return c}}var Kt=5003,Yt=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535];function Zt(e,t,i,a){var s,o,r,n,l,c,d,u,p,h=Math.max(2,a),m=new Uint8Array(256),g=new Int32Array(Kt),f=new Int32Array(Kt),v=0,y=0,w=!1;function b(e,t){m[o++]=e,o>=254&&C(t)}function k(e){x(Kt),y=l+2,w=!0,T(l,e)}function x(e){for(var t=0;t<e;++t)g[t]=-1}function C(e){o>0&&(e.writeByte(o),e.writeBytes(m,0,o),o=0)}function _(e){return(1<<e)-1}function I(){return 0===d?-1:(--d,255&i[u++])}function T(e,t){for(s&=Yt[v],v>0?s|=e<<v:s=e,v+=p;v>=8;)b(255&s,t),s>>=8,v-=8;if((y>r||w)&&(w?(r=_(p=n),w=!1):(++p,r=12==p?4096:_(p))),e==c){for(;v>0;)b(255&s,t),s>>=8,v-=8;C(t)}}this.encode=function(i){i.writeByte(h),d=e*t,u=0,function(e,t){var i,a,s,d,u,h,m;for(w=!1,r=_(p=n=e),c=1+(l=1<<e-1),y=l+2,o=0,d=I(),m=0,i=Kt;i<65536;i*=2)++m;m=8-m,x(h=Kt),T(l,t);e:for(;-1!=(a=I());)if(i=(a<<12)+d,g[s=a<<m^d]!==i){if(g[s]>=0){u=h-s,0===s&&(u=1);do{if((s-=u)<0&&(s+=h),g[s]===i){d=f[s];continue e}}while(g[s]>=0)}T(d,t),d=a,y<4096?(f[s]=y++,g[s]=i):k(t)}else d=f[s];T(d,t),T(c,t)}(h+1,i),i.writeByte(0)}}function ei(){this.page=-1,this.pages=[],this.newPage()}ei.pageSize=4096,ei.charMap={};for(var ti=0;ti<256;ti++)ei.charMap[ti]=String.fromCharCode(ti);function ii(e,t){this.width=~~e,this.height=~~t,this.transparent=null,this.transIndex=0,this.repeat=-1,this.delay=0,this.image=null,this.pixels=null,this.indexedPixels=null,this.colorDepth=null,this.colorTab=null,this.neuQuant=null,this.usedEntry=new Array,this.palSize=7,this.dispose=-1,this.firstFrame=!0,this.sample=10,this.dither=!1,this.globalPalette=!1,this.out=new ei}ei.prototype.newPage=function(){this.pages[++this.page]=new Uint8Array(ei.pageSize),this.cursor=0},ei.prototype.getData=function(){for(var e="",t=0;t<this.pages.length;t++)for(var i=0;i<ei.pageSize;i++)e+=ei.charMap[this.pages[t][i]];return e},ei.prototype.toFlattenUint8Array=function(){const e=[];for(var t=0;t<this.pages.length;t++)if(t===this.pages.length-1){const i=Uint8Array.from(this.pages[t].slice(0,this.cursor));e.push(i)}else e.push(this.pages[t]);const i=new Uint8Array(e.reduce(((e,t)=>e+t.length),0));return e.reduce(((e,t)=>(i.set(t,e),e+t.length)),0),i},ei.prototype.writeByte=function(e){this.cursor>=ei.pageSize&&this.newPage(),this.pages[this.page][this.cursor++]=e},ei.prototype.writeUTFBytes=function(e){for(var t=e.length,i=0;i<t;i++)this.writeByte(e.charCodeAt(i))},ei.prototype.writeBytes=function(e,t,i){for(var a=i||e.length,s=t||0;s<a;s++)this.writeByte(e[s])},ii.prototype.setDelay=function(e){this.delay=Math.round(e/10)},ii.prototype.setFrameRate=function(e){this.delay=Math.round(100/e)},ii.prototype.setDispose=function(e){e>=0&&(this.dispose=e)},ii.prototype.setRepeat=function(e){this.repeat=e},ii.prototype.setTransparent=function(e){this.transparent=e},ii.prototype.addFrame=function(e){this.image=e,this.colorTab=this.globalPalette&&this.globalPalette.slice?this.globalPalette:null,this.getImagePixels(),this.analyzePixels(),!0===this.globalPalette&&(this.globalPalette=this.colorTab),this.firstFrame&&(this.writeHeader(),this.writeLSD(),this.writePalette(),this.repeat>=0&&this.writeNetscapeExt()),this.writeGraphicCtrlExt(),this.writeImageDesc(),this.firstFrame||this.globalPalette||this.writePalette(),this.writePixels(),this.firstFrame=!1},ii.prototype.finish=function(){this.out.writeByte(59)},ii.prototype.setQuality=function(e){e<1&&(e=1),this.sample=e},ii.prototype.setDither=function(e){!0===e&&(e="FloydSteinberg"),this.dither=e},ii.prototype.setGlobalPalette=function(e){this.globalPalette=e},ii.prototype.getGlobalPalette=function(){return this.globalPalette&&this.globalPalette.slice&&this.globalPalette.slice(0)||this.globalPalette},ii.prototype.writeHeader=function(){this.out.writeUTFBytes("GIF89a")},ii.prototype.analyzePixels=function(){this.colorTab||(this.neuQuant=new Xt(this.pixels,this.sample),this.neuQuant.buildColormap(),this.colorTab=this.neuQuant.getColormap()),this.dither?this.ditherPixels(this.dither.replace("-serpentine",""),null!==this.dither.match(/-serpentine/)):this.indexPixels(),this.pixels=null,this.colorDepth=8,this.palSize=7,null!==this.transparent&&(this.transIndex=this.findClosest(this.transparent,!0))},ii.prototype.indexPixels=function(e){var t=this.pixels.length/3;this.indexedPixels=new Uint8Array(t);for(var i=0,a=0;a<t;a++){var s=this.findClosestRGB(255&this.pixels[i++],255&this.pixels[i++],255&this.pixels[i++]);this.usedEntry[s]=!0,this.indexedPixels[a]=s}},ii.prototype.ditherPixels=function(e,t){var i={FalseFloydSteinberg:[[3/8,1,0],[3/8,0,1],[2/8,1,1]],FloydSteinberg:[[7/16,1,0],[3/16,-1,1],[5/16,0,1],[1/16,1,1]],Stucki:[[8/42,1,0],[4/42,2,0],[2/42,-2,1],[4/42,-1,1],[8/42,0,1],[4/42,1,1],[2/42,2,1],[1/42,-2,2],[2/42,-1,2],[4/42,0,2],[2/42,1,2],[1/42,2,2]],Atkinson:[[1/8,1,0],[1/8,2,0],[1/8,-1,1],[1/8,0,1],[1/8,1,1],[1/8,0,2]]};if(!e||!i[e])throw"Unknown dithering kernel: "+e;var a=i[e],s=0,o=this.height,r=this.width,n=this.pixels,l=t?-1:1;this.indexedPixels=new Uint8Array(this.pixels.length/3);for(var c=0;c<o;c++){t&&(l*=-1);for(var d=1==l?0:r-1,u=1==l?r:0;d!==u;d+=l){var p=3*(s=c*r+d),h=n[p],m=n[p+1],g=n[p+2];p=this.findClosestRGB(h,m,g),this.usedEntry[p]=!0,this.indexedPixels[s]=p,p*=3;for(var f=h-this.colorTab[p],v=m-this.colorTab[p+1],y=g-this.colorTab[p+2],w=1==l?0:a.length-1,b=1==l?a.length:0;w!==b;w+=l){var k=a[w][1],x=a[w][2];if(k+d>=0&&k+d<r&&x+c>=0&&x+c<o){var C=a[w][0];p=s+k+x*r,n[p*=3]=Math.max(0,Math.min(255,n[p]+f*C)),n[p+1]=Math.max(0,Math.min(255,n[p+1]+v*C)),n[p+2]=Math.max(0,Math.min(255,n[p+2]+y*C))}}}}},ii.prototype.findClosest=function(e,t){return this.findClosestRGB((16711680&e)>>16,(65280&e)>>8,255&e,t)},ii.prototype.findClosestRGB=function(e,t,i,a){if(null===this.colorTab)return-1;if(this.neuQuant&&!a)return this.neuQuant.lookupRGB(e,t,i);for(var s=0,o=16777216,r=this.colorTab.length,n=0,l=0;n<r;l++){var c=e-(255&this.colorTab[n++]),d=t-(255&this.colorTab[n++]),u=i-(255&this.colorTab[n++]),p=c*c+d*d+u*u;(!a||this.usedEntry[l])&&p<o&&(o=p,s=l)}return s},ii.prototype.getImagePixels=function(){var e=this.width,t=this.height;this.pixels=new Uint8Array(e*t*3);for(var i=this.image,a=0,s=0,o=0;o<t;o++)for(var r=0;r<e;r++)this.pixels[s++]=i[a++],this.pixels[s++]=i[a++],this.pixels[s++]=i[a++],a++},ii.prototype.writeGraphicCtrlExt=function(){var e,t;this.out.writeByte(33),this.out.writeByte(249),this.out.writeByte(4),null===this.transparent?(e=0,t=0):(e=1,t=2),this.dispose>=0&&(t=7&this.dispose),t<<=2,this.out.writeByte(0|t|e),this.writeShort(this.delay),this.out.writeByte(this.transIndex),this.out.writeByte(0)},ii.prototype.writeImageDesc=function(){this.out.writeByte(44),this.writeShort(0),this.writeShort(0),this.writeShort(this.width),this.writeShort(this.height),this.firstFrame||this.globalPalette?this.out.writeByte(0):this.out.writeByte(128|this.palSize)},ii.prototype.writeLSD=function(){this.writeShort(this.width),this.writeShort(this.height),this.out.writeByte(240|this.palSize),this.out.writeByte(0),this.out.writeByte(0)},ii.prototype.writeNetscapeExt=function(){this.out.writeByte(33),this.out.writeByte(255),this.out.writeByte(11),this.out.writeUTFBytes("NETSCAPE2.0"),this.out.writeByte(3),this.out.writeByte(1),this.writeShort(this.repeat),this.out.writeByte(0)},ii.prototype.writePalette=function(){this.out.writeBytes(this.colorTab);for(var e=768-this.colorTab.length,t=0;t<e;t++)this.out.writeByte(0)},ii.prototype.writeShort=function(e){this.out.writeByte(255&e),this.out.writeByte(e>>8&255)},ii.prototype.writePixels=function(){new Zt(this.width,this.height,this.indexedPixels,this.colorDepth).encode(this.out)},ii.prototype.stream=function(){return this.out};var ai=globalThis&&globalThis.__awaiter||function(e,t,i,a){return new(i||(i=Promise))((function(s,o){function r(e){try{l(a.next(e))}catch(t){o(t)}}function n(e){try{l(a.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?s(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(r,n)}l((a=a.apply(e,t||[])).next())}))};const{Canvas:si}=gt,oi=.4;function ri(e){if(e)return new Promise((function(i,a){if("data"==e.slice(0,4)){let s=new Image;return s.onload=function(){i(s),t(s)},s.onerror=function(){a("Image load error"),t(s)},void(s.src=e)}let s=new Image;s.setAttribute("crossOrigin","Anonymous"),s.onload=function(){i(s)},s.onerror=function(){a("Image load error")},s.src=e}));function t(e){e.onload=null,e.onerror=null}}class ni{constructor(e){const t=Object.assign({},e);if(Object.keys(ni.defaultOptions).forEach((e=>{e in t||Object.defineProperty(t,e,{value:ni.defaultOptions[e],enumerable:!0,writable:!0})})),t.components?"object"==typeof t.components&&Object.keys(ni.defaultComponentOptions).forEach((e=>{e in t.components?Object.defineProperty(t.components,e,{value:Object.assign(Object.assign({},ni.defaultComponentOptions[e]),t.components[e]),enumerable:!0,writable:!0}):Object.defineProperty(t.components,e,{value:ni.defaultComponentOptions[e],enumerable:!0,writable:!0})})):t.components=ni.defaultComponentOptions,null!==t.dotScale&&void 0!==t.dotScale){if(t.dotScale<=0||t.dotScale>1)throw new Error("dotScale should be in range (0, 1].");t.components.data.scale=t.dotScale,t.components.timing.scale=t.dotScale,t.components.alignment.scale=t.dotScale}this.options=t,this.canvas=new si(e.size,e.size),this.canvasContext=this.canvas.getContext("2d"),this.qrCode=new Rt(-1,this.options.correctLevel),Number.isInteger(this.options.maskPattern)&&(this.qrCode.maskPattern=this.options.maskPattern),Number.isInteger(this.options.version)&&(this.qrCode.typeNumber=this.options.version),this.qrCode.addData(this.options.text),this.qrCode.make()}draw(){return new Promise((e=>this._draw().then(e)))}_clear(){this.canvasContext.clearRect(0,0,this.canvas.width,this.canvas.height)}static _prepareRoundedCornerClip(e,t,i,a,s,o){e.beginPath(),e.moveTo(t,i),e.arcTo(t+a,i,t+a,i+s,o),e.arcTo(t+a,i+s,t,i+s,o),e.arcTo(t,i+s,t,i,o),e.arcTo(t,i,t+a,i,o),e.closePath()}static _getAverageRGB(e){const t={r:0,g:0,b:0};let i,a,s=-4;const o={r:0,g:0,b:0};let r=0;a=e.naturalHeight||e.height,i=e.naturalWidth||e.width;const n=new si(i,a).getContext("2d");if(!n)return t;let l;n.drawImage(e,0,0);try{l=n.getImageData(0,0,i,a)}catch(c){return t}for(;(s+=20)<l.data.length;)l.data[s]>200||l.data[s+1]>200||l.data[s+2]>200||(++r,o.r+=l.data[s],o.g+=l.data[s+1],o.b+=l.data[s+2]);return o.r=~~(o.r/r),o.g=~~(o.g/r),o.b=~~(o.b/r),o}static _drawDot(e,t,i,a,s=0,o=1){e.fillRect((t+s)*a,(i+s)*a,o*a,o*a)}static _drawAlignProtector(e,t,i,a){e.clearRect((t-2)*a,(i-2)*a,5*a,5*a),e.fillRect((t-2)*a,(i-2)*a,5*a,5*a)}static _drawAlign(e,t,i,a,s=0,o=1,r,n){const l=e.fillStyle;e.fillStyle=r,new Array(4).fill(0).map(((r,n)=>{ni._drawDot(e,t-2+n,i-2,a,s,o),ni._drawDot(e,t+2,i-2+n,a,s,o),ni._drawDot(e,t+2-n,i+2,a,s,o),ni._drawDot(e,t-2,i+2-n,a,s,o)})),ni._drawDot(e,t,i,a,s,o),n||(e.fillStyle="rgba(255, 255, 255, 0.6)",new Array(2).fill(0).map(((r,n)=>{ni._drawDot(e,t-1+n,i-1,a,s,o),ni._drawDot(e,t+1,i-1+n,a,s,o),ni._drawDot(e,t+1-n,i+1,a,s,o),ni._drawDot(e,t-1,i+1-n,a,s,o)}))),e.fillStyle=l}_draw(){var e,t,i,a,s,o,r,n,l,c,d,u,p,h,m,g,f,v,y;return ai(this,void 0,void 0,(function*(){const w=null===(e=this.qrCode)||void 0===e?void 0:e.moduleCount,b=this.options.size;let k=this.options.margin;(k<0||2*k>=b)&&(k=0);const x=Math.ceil(k),C=b-2*k,_=this.options.whiteMargin,I=this.options.backgroundDimming,T=Math.ceil(C/w),A=T*w,S=A+2*x,P=new si(S,S),B=P.getContext("2d");this._clear(),B.save(),B.translate(x,x);const D=new si(S,S),L=D.getContext("2d");let E=null,M=[];if(this.options.gifBackground){const e=(e=>{const t=new Uint8Array(e);return ft({data:t,pos:0},Dt)})(this.options.gifBackground);if(E=e,O=!0,M=(N=e).frames.filter((e=>e.image)).map((e=>Mt(e,N.gct,O))),this.options.autoColor){let e=0,t=0,i=0,a=0;for(let s=0;s<M[0].colorTable.length;s++){const o=M[0].colorTable[s];o[0]>200||o[1]>200||o[2]>200||(0===o[0]&&0===o[1]&&0===o[2]||(a++,e+=o[0],t+=o[1],i+=o[2]))}e=~~(e/a),t=~~(t/a),i=~~(i/a),this.options.colorDark=`rgb(${e},${t},${i})`}}else if(this.options.backgroundImage){const e=yield ri(this.options.backgroundImage);if(this.options.autoColor){const t=ni._getAverageRGB(e);this.options.colorDark=`rgb(${t.r},${t.g},${t.b})`}L.drawImage(e,0,0,e.width,e.height,0,0,S,S),L.rect(0,0,S,S),L.fillStyle=I,L.fill()}else L.rect(0,0,S,S),L.fillStyle=this.options.colorLight,L.fill();var N,O;const R=Ft.getPatternPosition(this.qrCode.typeNumber),U=(null===(i=null===(t=this.options.components)||void 0===t?void 0:t.data)||void 0===i?void 0:i.scale)||oi,$=.5*(1-U);for(let e=0;e<w;e++)for(let t=0;t<w;t++){const i=this.qrCode.isDark(e,t),a=t<8&&(e<8||e>=w-8)||t>=w-8&&e<8;let s=a||(6==e&&t>=8&&t<=w-8||6==t&&e>=8&&e<=w-8);for(let n=1;n<R.length-1;n++)s=s||e>=R[n]-2&&e<=R[n]+2&&t>=R[n]-2&&t<=R[n]+2;const o=t*T+(s?0:$*T),r=e*T+(s?0:$*T);if(B.strokeStyle=i?this.options.colorDark:this.options.colorLight,B.lineWidth=.5,B.fillStyle=i?this.options.colorDark:this.options.colorLight,0===R.length)s||B.fillRect(o,r,(s?1:U)*T,(s?1:U)*T);else{s||t<w-4&&t>=w-4-5&&e<w-4&&e>=w-4-5||B.fillRect(o,r,(s?1:U)*T,(s?1:U)*T)}}const j=R[R.length-1],F=this.options.colorLight;if(B.fillStyle=F,B.fillRect(0,0,8*T,8*T),B.fillRect(0,(w-8)*T,8*T,8*T),B.fillRect((w-8)*T,0,8*T,8*T),(null===(s=null===(a=this.options.components)||void 0===a?void 0:a.timing)||void 0===s?void 0:s.protectors)&&(B.fillRect(8*T,6*T,(w-8-8)*T,T),B.fillRect(6*T,8*T,T,(w-8-8)*T)),(null===(r=null===(o=this.options.components)||void 0===o?void 0:o.cornerAlignment)||void 0===r?void 0:r.protectors)&&ni._drawAlignProtector(B,j,j,T),null===(l=null===(n=this.options.components)||void 0===n?void 0:n.alignment)||void 0===l?void 0:l.protectors)for(let e=0;e<R.length;e++)for(let t=0;t<R.length;t++){const i=R[t],a=R[e];(6!==i||6!==a&&a!==j)&&((6!==a||6!==i&&i!==j)&&(i===j&&a===j||ni._drawAlignProtector(B,i,a,T)))}B.fillStyle=this.options.colorDark,B.fillRect(0,0,7*T,T),B.fillRect((w-7)*T,0,7*T,T),B.fillRect(0,6*T,7*T,T),B.fillRect((w-7)*T,6*T,7*T,T),B.fillRect(0,(w-7)*T,7*T,T),B.fillRect(0,(w-7+6)*T,7*T,T),B.fillRect(0,0,T,7*T),B.fillRect(6*T,0,T,7*T),B.fillRect((w-7)*T,0,T,7*T),B.fillRect((w-7+6)*T,0,T,7*T),B.fillRect(0,(w-7)*T,T,7*T),B.fillRect(6*T,(w-7)*T,T,7*T),B.fillRect(2*T,2*T,3*T,3*T),B.fillRect((w-7+2)*T,2*T,3*T,3*T),B.fillRect(2*T,(w-7+2)*T,3*T,3*T);const z=(null===(d=null===(c=this.options.components)||void 0===c?void 0:c.timing)||void 0===d?void 0:d.scale)||oi,H=.5*(1-z);for(let e=0;e<w-8;e+=2)ni._drawDot(B,8+e,6,T,H,z),ni._drawDot(B,6,8+e,T,H,z);const G=(null===(p=null===(u=this.options.components)||void 0===u?void 0:u.cornerAlignment)||void 0===p?void 0:p.scale)||oi,q=.5*(1-G);ni._drawAlign(B,j,j,T,q,G,this.options.colorDark,(null===(m=null===(h=this.options.components)||void 0===h?void 0:h.cornerAlignment)||void 0===m?void 0:m.protectors)||!1);const V=(null===(f=null===(g=this.options.components)||void 0===g?void 0:g.alignment)||void 0===f?void 0:f.scale)||oi,J=.5*(1-V);for(let e=0;e<R.length;e++)for(let t=0;t<R.length;t++){const i=R[t],a=R[e];(6!==i||6!==a&&a!==j)&&((6!==a||6!==i&&i!==j)&&(i===j&&a===j||ni._drawAlign(B,i,a,T,J,V,this.options.colorDark,(null===(y=null===(v=this.options.components)||void 0===v?void 0:v.alignment)||void 0===y?void 0:y.protectors)||!1)))}if(_&&(B.fillStyle=this.options.backgroundColor,B.fillRect(-x,-x,S,x),B.fillRect(-x,A,S,x),B.fillRect(A,-x,x,S),B.fillRect(-x,-x,x,S)),this.options.logoImage){const e=yield ri(this.options.logoImage);let t=this.options.logoScale,i=this.options.logoMargin,a=this.options.logoCornerRadius;(t<=0||t>=1)&&(t=.2),i<0&&(i=0),a<0&&(a=0);const s=A*t,o=.5*(S-s),r=o;B.restore(),B.fillStyle=this.options.logoBackgroundColor,B.save(),ni._prepareRoundedCornerClip(B,o-i,r-i,s+2*i,s+2*i,a+i),B.clip();const n=B.globalCompositeOperation;B.globalCompositeOperation="destination-out",B.fill(),B.globalCompositeOperation=n,B.restore(),B.save(),ni._prepareRoundedCornerClip(B,o,r,s,s,a),B.clip(),B.drawImage(e,o,r,s,s),B.restore(),B.save(),B.translate(x,x)}if(E){let e,t,i,a,s,o;if(M.forEach((function(r){e||(e=new ii(b,b),e.setDelay(r.delay),e.setRepeat(0));const{width:n,height:l}=r.dims;t||(t=new si(n,l),i=t.getContext("2d"),i.rect(0,0,t.width,t.height),i.fillStyle="#ffffff",i.fill()),a&&o&&n===a.width&&l===a.height||(a=new si(n,l),s=a.getContext("2d"),o=s.createImageData(n,l)),o.data.set(r.patch),s.putImageData(o,0,0),i.drawImage(a.getContext("2d").canvas,r.dims.left,r.dims.top);const c=new si(S,S),d=c.getContext("2d");d.drawImage(t.getContext("2d").canvas,0,0,S,S),d.rect(0,0,S,S),d.fillStyle=I,d.fill(),d.drawImage(P.getContext("2d").canvas,0,0,S,S);const u=new si(b,b),p=u.getContext("2d");p.drawImage(c.getContext("2d").canvas,0,0,b,b),e.addFrame(p.getImageData(0,0,u.width,u.height).data)})),!e)throw new Error("No frames.");if(e.finish(),li(this.canvas)){const t=e.stream().toFlattenUint8Array().reduce(((e,t)=>e+String.fromCharCode(t)),"");return Promise.resolve(`data:image/gif;base64,${window.btoa(t)}`)}return Promise.resolve(Buffer.from(e.stream().toFlattenUint8Array()))}{L.drawImage(P.getContext("2d").canvas,0,0,S,S),B.drawImage(D.getContext("2d").canvas,-x,-x,S,S);const e=new si(b,b);e.getContext("2d").drawImage(P.getContext("2d").canvas,0,0,b,b),this.canvas=e;const t=this.options.gifBackground?"gif":"png";return li(this.canvas)?Promise.resolve(this.canvas.toDataURL(t)):Promise.resolve(this.canvas.toBuffer(t))}}))}}function li(e){try{return e instanceof HTMLElement}catch(t){return"object"==typeof e&&1===e.nodeType&&"object"==typeof e.style&&"object"==typeof e.ownerDocument}}ni.CorrectLevel=Ut,ni.defaultComponentOptions={data:{scale:.4},timing:{scale:.5,protectors:!1},alignment:{scale:.5,protectors:!1},cornerAlignment:{scale:.5,protectors:!0}},ni.defaultOptions={text:"",size:400,margin:20,colorDark:"#000000",colorLight:"rgba(255, 255, 255, 0.6)",correctLevel:Ut.M,backgroundImage:void 0,backgroundDimming:"rgba(0,0,0,0)",logoImage:void 0,logoScale:.2,logoMargin:4,logoCornerRadius:8,whiteMargin:!0,components:ni.defaultComponentOptions,autoColor:!0,logoBackgroundColor:"#ffffff",backgroundColor:"#ffffff"};const ci={props:{text:{type:String,required:!0},qid:{type:String},correctLevel:{type:Number,default:1},size:{type:Number,default:200},margin:{type:Number,default:20},colorDark:{type:String,default:"#000000"},colorLight:{type:String,default:"#FFFFFF"},bgSrc:{type:String,default:void 0},background:{type:String,default:"rgba(0,0,0,0)"},backgroundDimming:{type:String,default:"rgba(0,0,0,0)"},logoSrc:{type:String,default:void 0},logoBackgroundColor:{type:String,default:"rgba(255,255,255,1)"},gifBgSrc:{type:String,default:void 0},logoScale:{type:Number,default:.2},logoMargin:{type:Number,default:0},logoCornerRadius:{type:Number,default:8},whiteMargin:{type:[Boolean,String],default:!0},dotScale:{type:Number,default:1},autoColor:{type:[Boolean,String],default:!0},binarize:{type:[Boolean,String],default:!1},binarizeThreshold:{type:Number,default:128},callback:{type:Function,default:function(){}},bindElement:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},components:{default:function(){return{data:{scale:1},timing:{scale:1,protectors:!1},alignment:{scale:1,protectors:!1},cornerAlignment:{scale:1,protectors:!0}}}}},name:"vue-qr",data:()=>({imgUrl:""}),watch:{$props:{deep:!0,handler(){this.main()}}},mounted(){this.main()},methods:{async main(){if(this.gifBgSrc){const t=await(e=this.gifBgSrc,new Promise(((t,i)=>{var a=new XMLHttpRequest;a.responseType="blob",a.onload=function(){var e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsArrayBuffer(a.response)},a.open("GET",e),a.send()}))),i=this.logoSrc;return void this.render(void 0,i,t)}var e;const t=this.bgSrc,i=this.logoSrc;this.render(t,i)},async render(e,t,i){const a=this;new ni({gifBackground:i,text:a.text,size:a.size,margin:a.margin,colorDark:a.colorDark,colorLight:a.colorLight,backgroundColor:a.backgroundColor,backgroundImage:e,backgroundDimming:a.backgroundDimming,logoImage:t,logoScale:a.logoScale,logoBackgroundColor:a.logoBackgroundColor,correctLevel:a.correctLevel,logoMargin:a.logoMargin,logoCornerRadius:a.logoCornerRadius,whiteMargin:Fe(a.whiteMargin),dotScale:a.dotScale,autoColor:Fe(a.autoColor),binarize:Fe(a.binarize),binarizeThreshold:a.binarizeThreshold,components:a.components}).draw().then((e=>{this.imgUrl=e,a.callback&&a.callback(e,a.qid)}))}}},di=["src"];const ui=a(ci,[["render",function(e,t,i,a,s,o){return i.bindElement?(d(),u("img",{key:0,style:{display:"inline-block"},src:s.imgUrl},null,8,di)):p("",!0)}]]),pi={id:"app"},hi={class:"scale"},mi={class:"micro_header"},gi={class:"micro_left"},fi={class:"avatar"},vi=["src"],yi={class:"info"},wi={class:"t1"},bi={class:"micro_main"},ki={class:"micro_main_top"},xi={class:"micro_main-sp"},Ci={class:"micro_main_temp"},_i=["onClick"],Ii={class:"title"},Ti={class:"price"},Ai={class:"micro_main_middle"},Si=["src"],Pi={key:0,class:"micro_main_bottom"},Bi={class:"micro_pay"},Di={class:"micro_pay_right"},Li={class:"noQrCode"},Ei={class:"price"},Mi={class:"micro_way"},Ni=(e=>(g("data-v-f51bb970"),e=e(),f(),e))((()=>h("div",{class:"box"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})],-1))),Oi={class:"t1"},Ri={class:"bd"},Ui={class:"t2"},$i={key:1,class:"btns"},ji={key:2,class:"btns"},Fi=a({__name:"index",props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})}},emits:["close"],setup(e,{emit:t}){const{t:i}=v(),a=o(!1),s=y(),r=((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location||location.origin.includes("medsci.cn")),o(!1)),l=e,c=l.userInfo||{},g=l.currentItem,f=o({}),$=o(),j=t,F=o(""),z=o(),H=o(),G=o(),q=o(localStorage.getItem("socialType")),V=o((null==c?void 0:c.avatar)?null==c?void 0:c.avatar:"https://img.medsci.cn/web/img/user_icon.png"),J=()=>{V.value="https://img.medsci.cn/web/img/user_icon.png"},W=()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},Q=()=>{const e=window.innerWidth;r.value=e>768},X=()=>{clearInterval(G.value),j("close")},K=(e,t)=>{f.value=e,$.value=t,0==q.value&&0!=e.feePrice&&Y(e,g.appUuid)};w((()=>{clearInterval(G.value)}));const Y=async(e,t)=>{if(!f.value.coinType)return void D.warning("请选择订阅服务周期");let o=await(async()=>{let e=O("current_location_country",1);if(!e){const t=await getLocation();R("current_location_country",t),e=t}return e})();if(null==c?void 0:c.userId){const s={appUuid:t,priceId:e.priceId,monthNum:e.monthNum};try{a.value=!0;let t=await L(s);if(t)if(a.value=!1,0==q.value&&0!=e.feePrice){const e=t;location.origin.includes(".medsci.cn")||location.origin.includes(".medon.com.cn")?F.value=location.origin+"/apps/payLink/"+encodeURIComponent(e):F.value=location.origin+"/payLink/"+encodeURIComponent(e),H.value=JSON.parse(e).piId,await void(G.value=setInterval((async()=>{"PAID"===(await U(H.value)).payStatus&&(location.reload(),clearInterval(G.value))}),2e3))}else D({type:"success",message:i("tool.sS")}),setTimeout((()=>{location.href=t}),1e3)}catch(r){a.value=!1}}else o&&"中国"!=o?s.push("/login"):window.addLoginDom()};return b((()=>{window.removeEventListener("resize",Q)})),k((()=>{Q(),window.addEventListener("resize",Q),(async()=>{const e=await E("homePayImg");z.value=e.list[0].value})(),r.value&&1==g.feeTypes.length&&(g.feeTypes[0].feePrice>0&&0==q.value&&K(g.feeTypes[0],0),g.feeTypes[0].feePrice>0&&0!=q.value&&K(g.feeTypes[0],0),0==g.feeTypes[0].feePrice&&K(g.feeTypes[0],0))})),(e,t)=>{var i,s,o,r,l,v,y,w,b,k;const D=M,L=N;return d(),u("div",pi,[h("div",hi,[h("div",mi,[h("div",gi,[h("div",fi,[h("img",{src:x(V),onError:J,alt:""},null,40,vi)]),h("div",yi,[h("span",wi,m((null==(i=x(c))?void 0:i.realName)?null==(s=x(c))?void 0:s.realName:null==(o=x(c))?void 0:o.userName),1)])]),h("div",{class:"micro_right"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:X})])]),h("div",bi,[h("div",ki,[h("div",xi,[h("div",Ci,[0!=x(q)||x(g).feeTypes.length>1?(d(),u("div",{key:0,class:"swiper-vip",showIndicator:!1,autoPlay:!1,style:C({transform:`translate(${e.translateVipVal}px)`})},[(d(!0),u(_,null,I(x(g).feeTypes,((t,i)=>(d(),u("div",{class:"swiper-vip-item",key:i,onClick:e=>K(t,i)},[h("div",{class:"newer",style:C({left:i%4==0&&0!=i?"6px":"-1px"})},null,4),h("div",{class:T(["swiper-vip-item-child",{sactvie:x($)==i}])},[h("div",Ii,m(e.$t(`tool.${t.type}`)),1),h("div",Ti,m("人民币"==t.coinType?"¥":"$")+m(t.feePrice),1)],2)],8,_i)))),128))],4)):p("",!0)])])]),h("div",Ai,[h("img",{class:"vip-banner",alt:"",src:x(z)},null,8,Si)]),(null==(r=x(f))?void 0:r.coinType)&&0!=x(f).feePrice&&0==x(q)?(d(),u("div",Pi,[h("div",Bi,[h("div",Di,[A(h("div",Li,null,512),[[L,x(a)],[S,x(a)]]),A(n(ui,{ref:"qrcode",class:"qr-code",id:"qrcode",correctLevel:3,autoColor:!1,colorDark:"#000000",text:x(F),size:95,margin:0,logoMargin:3},null,8,["text"]),[[S,!x(a)]]),h("div",Ei,[h("div",Mi,[Ni,h("span",null,m(e.$t("tool.Support_Alipay_Payment")),1)]),h("span",Oi,[P(m(e.$t("tool.Support_Alipay_Payment")),1),h("span",Ri,m(null==(l=x(f))?void 0:l.feePrice),1),P(m("人民币"==(null==(v=x(f))?void 0:v.coinType)?"¥":"$")+"/"+m(3==(null==(y=x(f))?void 0:y.monthNum)?e.$t("tool.Quarter"):12==(null==(w=x(f))?void 0:w.monthNum)?e.$t("tool.Year"):e.$t("tool.Month")),1)]),h("span",Ui,m(e.$t("tool.Meisi_Account"))+"："+m(x(c).userName),1),h("span",{class:"t3",onClick:W},[P(m(e.$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"))+" ",1),h("img",{onClick:W,src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})])])])])])):p("",!0),(null==(b=x(f))?void 0:b.coinType)&&0==x(f).feePrice?(d(),u("div",$i,[n(D,{type:"primary",onClick:t[0]||(t[0]=e=>Y(x(f),x(g).appUuid))},{default:B((()=>[P(m(e.$t("tool.Free_Trial")),1)])),_:1})])):p("",!0),(null==(k=x(f))?void 0:k.coinType)&&x(f).feePrice>0&&0!=x(q)?(d(),u("div",ji,[n(D,{type:"primary",onClick:t[1]||(t[1]=e=>Y(x(f),x(g).appUuid))},{default:B((()=>[P(m(e.$t("market.subscribe")),1)])),_:1})])):p("",!0)])])])}}},[["__scopeId","data-v-f51bb970"]]),zi={name:"Vip",data:()=>({isCheckW:!0,isCheckZ:!1,isLogin:!1,activeItem:{},appId:"wx9096048917ec59ab",appOrderId:"",isClick:!1,openId:"",isWx:!1,choseUserVip:{},isFromMedsci:!1,showAll:!1,checkCount:0,vipTypeList:[],activeType:0,active:0,radio:"",isShaking:!1,avatar:"",socialType:localStorage.getItem("socialType")}),components:{VanCheckbox:Be},props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})}},created(){},mounted(){var e,t;this.avatar=(null==(e=this.userInfo)?void 0:e.avatar)?null==(t=this.userInfo)?void 0:t.avatar:"https://img.medsci.cn/web/img/user_icon.png",this.isUp=(null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn")),1==this.currentItem.feeTypes.length&&(this.currentItem.feeTypes[0].feePrice>0&&0==localStorage.getItem("socialType")&&this.CheckItem(this.currentItem.feeTypes[0],this.currentItem.appUuid),0==this.currentItem.feeTypes[0].feePrice&&this.CheckItem(this.currentItem.feeTypes[0],this.currentItem.appUuid)),$.get("userInfo")&&JSON.parse($.get("userInfo")).userId&&(this.isLogin=!0,this.initUser()),this.init(),this.$route.query.source&&"medsci"==this.$route.query.source&&(this.isFromMedsci=!0)},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},async getLocationData(){let e=O("current_location_country",1);if(!e){const t=await getLocation();R("current_location_country",t),e=t}return e},async subscribe(e,t,i){var a;if(!this.radio&&i)return this.isShaking=!0,void setTimeout((()=>{this.isShaking=!1}),500);let s=await this.getLocationData();if(null==(a=this.userInfo)?void 0:a.userId){const i={appUuid:t,priceId:e.priceId,monthNum:e.monthNum};try{let t=await L(i);if(t)if(0==localStorage.getItem("socialType")&&0!=e.feePrice){let e=await j(JSON.parse(t));D({type:"success",message:this.$t("tool.sS")}),setTimeout((()=>{location.href=e}),1e3)}else D({type:"success",message:this.$t("tool.sS")}),setTimeout((()=>{location.href=t}),1e3)}catch(o){}}else s&&"中国"!=s?this.$router.push((isUp,"/login")):window.addLoginDom()},CheckItem(e,t){this.activeItem=e,this.active=t},openActivity(e){e&&(window.location.href=e)},login(){addLoginDom()},initUser(){},init(){},isMedSci:()=>navigator.userAgent.includes("medsci_app"),goBack(){window.history.back(-1)},checkFn1(){this.isCheckW=!0,this.isCheckZ=!1},checkFn2(){this.isCheckW=!1,this.isCheckZ=!0},goAgreent(){const e="https://portal-test.medon.com.cn/agreement/27";this.isMedSci()?window.location.href=e:window.open(e)},createOrder(){this.isWx&&(this.isCheckW=!0,this.isCheckZ=!1);const e={accessAppId:"college",appOrderId:this.appOrderId,payChannel:this.isCheckW?"WX":"ALI",paySource:"MEDSCI_WEB",payType:this.isWx?"JSAPI":"MWEB"};this.$axios.post(api.payBuild,e).then((e=>{this.orderList(e.data.payOrderId)}))},orderList(e){const t={};this.$route.query.from&&(t.from="app"),this.isFromMedsci&&(t.sourcefrom="main",t.redirectUrl=this.$route.query.redirectUrl);const i={accessAppId:"college",openId:this.isWx?this.openId:"",payOrderId:e,extParam:JSON.stringify(t)};this.$axios.post(api.payOrder,i).then((e=>{if(this.isCheckW){if(this.isWx)return void this.wxOrder(e.data.wechatJsapi);window.location.href=e.data.wechatH5.h5Url}else if(e.data.aliH5.html){const t=document.createElement("div");t.innerHTML=e.data.aliH5.html,document.body.appendChild(t),document.forms[0].submit()}}))},wxOrder(e){WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.appId,timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.packageStr,signType:e.signType,paySign:e.paySign},(function(e){"get_brand_wcpay_request:ok"==e.err_msg?(be.success("支付成功！"),setTimeout((()=>{window.location.reload()}),1e3)):e.err_msg}))}}},Hi=e=>(g("data-v-39e55036"),e=e(),f(),e),Gi={key:0,class:"vip-head"},qi={class:"vip-introduce"},Vi=Hi((()=>h("img",{class:"crown",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""},null,-1))),Ji={class:"box"},Wi={key:0,class:"box-left-1"},Qi=Hi((()=>h("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:""},null,-1))),Xi={class:"left2"},Ki=Hi((()=>h("span",{class:"t2"},"请登录后购买",-1))),Yi={key:1,class:"box-left"},Zi=["src"],ea={class:"box-word"},ta={class:"t1"},ia={class:"vip-main"},aa={class:"vip-one"},sa={class:"big"},oa={ref:"scroll"},ra=["onClick"],na={class:"title"},la={class:"price"},ca={key:0,class:"isfava"},da={key:0,class:"vip-two"},ua=Hi((()=>h("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:""},null,-1))),pa=[Hi((()=>h("div",{class:"item-left"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""}),h("span",null,"支付宝支付")],-1))),Hi((()=>h("div",{class:"item-right isCheck"},[h("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:""})],-1)))],ha={key:1,class:"vip-pay btns"},ma={key:0,class:"pay-left"},ga={class:"t1"},fa=Hi((()=>h("span",null,"协议",-1)));const va=a(zi,[["render",function(e,t,i,a,s,o){const r=F("van-checkbox"),l=M;return d(),u("div",{class:T(["vip",{sp:o.isMedSci()}])},[o.isMedSci()?p("",!0):(d(),u("div",Gi,m(i.currentItem.appName),1)),h("div",qi,[Vi,h("div",Ji,[s.isLogin?(d(),u("div",Yi,[h("img",{class:"avatar",src:s.avatar,alt:"",onError:t[1]||(t[1]=(...e)=>o.changeImg&&o.changeImg(...e))},null,40,Zi),h("div",ea,[h("span",ta,m(i.userInfo.realName||i.userInfo.userName),1)])])):(d(),u("div",Wi,[Qi,h("div",Xi,[h("span",{class:"t1",style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...e)=>o.login&&o.login(...e))},"立即登录"),Ki])]))])]),h("div",ia,[h("div",aa,[h("div",sa,[h("ul",oa,[(d(!0),u(_,null,I(i.currentItem.feeTypes,((t,i)=>(d(),u("li",{key:i,class:T({sactvie:t.type==s.activeItem.type}),onClick:e=>o.CheckItem(t)},[h("div",na,m(e.$t(`tool.${t.type}`)),1),h("div",la,"¥"+m(t.feePrice),1),t.originalPrice?(d(),u("div",ca,m("人民币"==t.coinType?"¥":"$")+m(t.feePrice),1)):p("",!0)],10,ra)))),128))],512)])]),s.activeItem.feePrice>0&&0==s.socialType?(d(),u("div",da,[h("div",{class:T(["pay",{isWx:s.isWx}])},[ua,h("div",{class:"item",onClick:t[2]||(t[2]=(...e)=>o.checkFn2&&o.checkFn2(...e))},pa)],2)])):p("",!0)]),s.activeItem.feePrice>=0?(d(),u("div",ha,[0!=s.activeItem.feePrice&&0==s.socialType?(d(),u("div",ma,[h("div",ga,m(i.currentItem.appName),1),h("div",{class:T(["t2",{shake:s.isShaking}])},[n(r,{modelValue:s.radio,"onUpdate:modelValue":t[3]||(t[3]=e=>s.radio=e)},null,8,["modelValue"]),h("span",{onClick:t[4]||(t[4]=(...e)=>o.goAgreent&&o.goAgreent(...e))},[P("请在阅读并同意"),fa,P("后开通")])],2)])):p("",!0),0!=s.activeItem.feePrice&&0==s.socialType?(d(),u("div",{key:1,class:"pay-right",onClick:t[5]||(t[5]=e=>o.subscribe(s.activeItem,i.currentItem.appUuid,"ali"))},[h("span",null,m(s.activeItem.feePrice)+"元确认协议并支付",1)])):p("",!0),0==s.activeItem.feePrice?(d(),z(l,{key:2,onClick:t[6]||(t[6]=e=>o.subscribe(s.activeItem,i.currentItem.appUuid)),type:"primary"},{default:B((()=>[P(m(e.$t("tool.Free_Trial")),1)])),_:1})):p("",!0),s.activeItem.feePrice>0&&0!=s.socialType?(d(),z(l,{key:3,onClick:t[7]||(t[7]=e=>o.subscribe(s.activeItem,i.currentItem.appUuid)),type:"primary"},{default:B((()=>[P(m(e.$t("market.subscribe")),1)])),_:1})):p("",!0)])):p("",!0)],2)}],["__scopeId","data-v-39e55036"]]);function ya(e,t){const i=Date.now();localStorage.setItem(e+"_value",t),localStorage.setItem(e+"_timestamp",i)}function wa(e,t){const i=e+"_value",a=e+"_timestamp",s=localStorage.getItem(i),o=localStorage.getItem(a);if(null!==s&&null!==o){const e=new Date(o);return(new Date-e)/864e5>t?(localStorage.removeItem(i),localStorage.removeItem(a),null):s}return null}function ba(){let e=wa("current_langs_pack",7),t=wa("current_langs_pack_umo",7);if(!e||!t){fetch("https://ai.medon.com.cn/dev-api/ai-base/index/getConfigPage").then((e=>{if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((t=>{if(0!==t.data.list.length){e=JSON.stringify(function(e){const t={};return e.forEach((e=>{const[i]=e.key.split("."),a=JSON.parse(e.value);t[i]||(t[i]={}),t[i]={...t[i],...a}})),t}(t.data.list)),ya("current_langs_pack",e);let i=t.data.list.filter((e=>"{}"!=e.value&&e.key.includes(".dify"))).reduce(((e,t)=>(e[t.key.substr(0,t.key.indexOf("."))]||(e[t.key.substr(0,t.key.indexOf("."))]={}),e[t.key.substr(0,t.key.indexOf("."))]=JSON.parse(t.value),e)),{});ya("current_langs_pack_umo",JSON.stringify(i))}})).catch((e=>{}))}}const ka={class:"bg-[#F9F9F9] overflow-auto"},xa={class:"pt-[75px] text-white mb-[30px] font-bold"},Ca={class:"flex justify-center"},_a=(e=>(g("data-v-0f06f250"),e=e(),f(),e))((()=>h("img",{class:"w-[24px] h-[24px]",src:"/apps/static/搜索-8381f2c3.svg",alt:""},null,-1))),Ia={class:"content"},Ta={class:"flex justify-center my-8 bg-[#F9F9F9]"},Aa={class:"flex items-center"},Sa=["onClick"],Pa={key:0,class:"menu-box flex flex-wrap justify-between"},Ba={class:"flex mb-1 card-item"},Da={class:"flex",style:{width:"75%","align-items":"center"}},La=["src"],Ea=["title","innerHTML"],Ma={style:{width:"30%","text-align":"right","font-size":"14px"}},Na=["title","innerHTML"],Oa={class:"flex justify-between items-center"},Ra={class:"text-[#B0B0B0]"},Ua={key:0,class:"during_order"},$a={key:1,class:"delay_order"},ja={key:1,class:"tab_box"},Fa={class:"menu-box flex flex-wrap justify-between"},za={class:"flex mb-1 card-item"},Ha={class:"flex",style:{width:"75%","align-items":"center"}},Ga=["src"],qa=["title","innerHTML"],Va={style:{width:"30%","text-align":"right"}},Ja=["innerHTML"],Wa={class:"flex justify-between items-center"},Qa={class:"text-[#B0B0B0]"},Xa={key:0,class:"during_order"},Ka={key:1,class:"delay_order"},Ya=a({__name:"index",setup(a){(null==location?void 0:location.origin.includes("medon.com.cn"))||null==location||location.origin.includes("medsci.cn");const s=xe("基于AI的写作文本加工.png"),r=H(),l=y(),c=o(""),g=o([]),f=o([{value:"我的应用",remark:"我的应用"},{value:"",remark:"全部"}]),v=o(!1),w=o(1),b=o(null),A=o(null),S=o("first"),E=o(null),N=o(!1),U=o({appType:"",socialUserId:"",appLang:"",order:2,isMine:2}),j=()=>{N.value=!1},F=async()=>{let e=O("current_location_country",1);if(!e){const t=await getLocation();R("current_location_country",t),e=t}return e},ne=async(e,i)=>{var a;let s=await F();if(null==(a=A.value)?void 0:a.userId){const a={appUuid:i,priceId:e.priceId,monthNum:e.monthNum};let s=await L(a);s&&(D({type:"success",message:t("tool.sS")}),setTimeout((()=>{location.href=s}),1e3))}else s&&"中国"!=s?l.push("/login"):window.addLoginDom()},le=e=>{var t;1==(null==(t=e.appUser)?void 0:t.status)?de(e):ye(e)},ce=e=>{let t=[],i=[];1==w.value?t=JSON.parse(JSON.stringify(ue)):0!=w.value?t=JSON.parse(JSON.stringify(ue)).filter((e=>e.appType===f.value[w.value].value)):0==w.value&&(t=JSON.parse(JSON.stringify(g.value))),i=t.filter((t=>{if(t.appName.includes(e)||t.appDescription.includes(e)||t.mapType.includes(e))return t})),g.value.forEach((e=>{e.appName=e.appName.replace(/<[^>]+>/g,""),e.appDescription=e.appDescription.replace(/<[^>]+>/g,""),e.mapType=e.mapType.replace(/<[^>]+>/g,"")}));let a=new RegExp(e,"gi");g.value=i.map((t=>(e&&(t.appName=t.appName.replace(a,`<span style="color: #409eff">${e}</span>`),t.appDescription=t.appDescription.replace(a,`<span style="color: #409eff">${e}</span>`),t.mapType=t.mapType.replace(a,`<span style="color: #409eff">${e}</span>`)),t)))},de=async e=>{if(!(null==e?void 0:e.dAppUuid))return void D({message:"请先至后台绑定应用实例",type:"warning"});ee(e.appUuid,localStorage.getItem("openid")),sessionStorage.setItem("nodeInfo",JSON.stringify(e));const t=window.location.href.replace(/\/$/,"");"工具"!==e.appType?"问答"!==e.appType?"写作"===e.appType&&(await ba(),localStorage.setItem("appWrite",JSON.stringify({appUuid:e.appUuid,directoryMd:e.directoryMd})),window.open(`${window.location.origin}/ai-write/write/${e.appUuid}?appName=${e.appName}`)):window.open(`${t}/chat/${null==e?void 0:e.dAppUuid}/${null==e?void 0:e.appUuid}?appName=${e.appName}`,"_blank"):window.open(`${t}/tool/${null==e?void 0:e.dAppUuid}/${null==e?void 0:e.appUuid}?appName=${e.appName}`,"_blank")};let ue=[];const pe=o(["https://img.medsci.cn/medsci-ai/bg1.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg2.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg3.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg4.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg5.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg6.png?imageMogr2/format/webp"]);k((async()=>{var e,t,i;const a=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${a}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=r.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=r.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(v.value=!0),A.value=$.get("userInfo")?JSON.parse($.get("userInfo")):null,A.value||(localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken")),r.query.lang?U.value.appLang=q[r.query.lang]:U.value.appLang=G();let s=Math.floor(6*Math.random());b.value=pe.value[s],(null==(i=A.value)?void 0:i.userId)?(U.value.socialUserId=A.value.plaintextUserId,U.value.appLang=G()||location.pathname.replaceAll("/",""),fe(),ve()):(U.value.socialUserId=0,G()?U.value.appLang=G():ge(location.pathname.replaceAll("/","")),fe()),await he(),(async()=>{var e,t;let i=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=A.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:"",userUuid:null==(t=A.value)?void 0:t.openid}];await Z.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",i)})()}));const he=()=>{V().then((e=>{f.value.push(...e)})).catch()},me=e=>{v.value=e},ge=e=>{U.value.appLang=q[e],fe()},fe=()=>{J(U.value).then((e=>{var t,i;g.value=null==e?void 0:e.map((e=>({...e,mapType:W[e.appType]}))),""==U.value.appType&&(ue=[...g.value]),1==U.value.isMine&&("first"==S.value&&(g.value=null==(t=g.value)?void 0:t.filter((e=>{var t;return 1==(null==(t=e.appUser)?void 0:t.status)}))),"second"==S.value&&(g.value=null==(i=g.value)?void 0:i.filter((e=>{var t;return 2==(null==(t=e.appUser)?void 0:t.status)}))))})).catch((e=>{}))},ve=()=>{if(localStorage.getItem("yudaoToken"))return void fe();const e=$.get("userInfo");if(e){const i=JSON.parse(e);try{Q({userId:i.userId,userName:i.userName,realName:i.realName,avatar:i.avatar,plaintextUserId:i.plaintextUserId,mobile:i.mobile,email:i.email}).then((e=>{(null==e?void 0:e.token)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),fe())}))}catch(t){}}},ye=async e=>{E.value=e,N.value=!0},we=()=>{fe()};return(t,a)=>{const o=e,r=te,y=ie,k=M,D=ae,L=se,O=oe,R=je,$=re;return d(),u("div",ka,[n(o,{onGetAppLang:ge,onIsZHChange:me}),h("div",{class:"flex flex-col items-center h-[246px] relative min-w-[980px]",style:C({background:`url(${x(b)}) no-repeat center`,backgroundSize:"cover"})},[h("h1",xa,m(t.$t("faq.xAI")),1),h("div",Ca,[n(y,{class:"!w-[888px] !h-[54px]",modelValue:x(c),"onUpdate:modelValue":a[0]||(a[0]=e=>X(c)?c.value=e:null),placeholder:t.$t("market.keywords"),clearable:"",onInput:ce},{prefix:B((()=>[n(r,{size:"24",class:"cursor-pointer mt-[2px]"},{default:B((()=>[_a])),_:1})])),_:1},8,["modelValue","placeholder"])])],4),h("main",null,[h("div",Ia,[h("div",Ta,[h("div",Aa,[(d(!0),u(_,null,I(x(f),((e,i)=>(d(),u("div",{class:T(["mr-2 px-4 py-1 cursor-pointer m_font",x(w)==i?"bg-[#409eff] text-white rounded-4xl":""]),key:i,onClick:t=>(async(e,t)=>{var i;let a=await F();if(w.value=e,c.value="",c.value&&ce(c.value),!(null==(i=A.value)?void 0:i.userId)&&0==w.value)return g.value=[],void(a&&"中国"!=a?l.push("/login"):window.addLoginDom());0!=w.value?(U.value.isMine=2,U.value.order=2,"全部"==t.remark?U.value.appType="":U.value.appType=t.value):(S.value="first",U.value.appType="",U.value.isMine=1,U.value.order=1),fe()})(i,e)},m(t.$t(`${x(W)[e.remark]}`)),11,Sa)))),128))])]),0!=x(w)?(d(),u("div",Pa,[(d(!0),u(_,null,I(x(g),((e,i)=>(d(),z(D,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:C({background:`url(${x(s)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)"}),key:i,onClick:t=>le(e)},{default:B((()=>{var i,a,s,o,l,c;return[h("div",Ba,[h("div",Da,[h("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:e.appIcon,alt:"icon"},null,8,La),h("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:e.appName,innerHTML:e.appName},null,8,Ea)]),h("div",Ma,[1==(null==(i=e.appUser)?void 0:i.status)?(d(),z(k,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:K((t=>de(e)),["stop"])},{default:B((()=>[P(m(t.$t("market.open")),1),n(r,null,{default:B((()=>[n(x(Y),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):p("",!0),2==(null==(a=e.appUser)?void 0:a.status)?(d(),z(k,{key:1,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:K((t=>ye(e)),["stop"])},{default:B((()=>[P(m(t.$t("market.renew")),1),n(r,null,{default:B((()=>[n(x(Y),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):p("",!0),e.appUser?p("",!0):(d(),z(k,{key:2,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:K((t=>ye(e)),["stop"])},{default:B((()=>[P(m(t.$t("market.subscribe")),1),n(r,null,{default:B((()=>[n(x(Y),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"]))])]),h("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",title:e.appDescription,innerHTML:e.appDescription},null,8,Na),h("div",Oa,[h("div",Ra,m(t.$t(`${x(W)[e.appType]}`)),1),1==(null==(s=e.appUser)?void 0:s.status)?(d(),u("div",Ua,m(t.$t("market.subUntil"))+m(null==(o=e.appUser)?void 0:o.expireAt)+m(t.$t("market.expiredOn")),1)):p("",!0),2==(null==(l=e.appUser)?void 0:l.status)?(d(),u("div",$a,m(t.$t("market.haveBeen"))+m(null==(c=e.appUser)?void 0:c.expireAt)+m(t.$t("market.expiredOn")),1)):p("",!0)])]})),_:2},1032,["style","onClick"])))),128))])):(d(),u("div",ja,[n(O,{modelValue:x(S),"onUpdate:modelValue":a[1]||(a[1]=e=>X(S)?S.value=e:null),class:"demo-tabs",onTabChange:we},{default:B((()=>[n(L,{label:t.$t("market.subscribed"),name:"first"},null,8,["label"]),n(L,{label:t.$t("market.expired"),name:"second"},null,8,["label"])])),_:1},8,["modelValue"]),h("div",Fa,[(d(!0),u(_,null,I(x(g),((e,i)=>(d(),z(D,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:C({background:`url(${x(s)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)",maxHeight:"189.5px"}),key:i,onClick:t=>le(e)},{default:B((()=>{var i,a,s,o,l,c;return[h("div",za,[h("div",Ha,[h("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:e.appIcon,alt:"icon"},null,8,Ga),h("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:e.appName,innerHTML:e.appName},null,8,qa)]),h("div",Va,[1==(null==(i=e.appUser)?void 0:i.status)?(d(),z(k,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:K((t=>de(e)),["stop"])},{default:B((()=>[P(m(t.$t("market.open")),1),n(r,null,{default:B((()=>[n(x(Y),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):p("",!0),2==(null==(a=e.appUser)?void 0:a.status)?(d(),z(k,{key:1,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:K((t=>ye(e)),["stop"])},{default:B((()=>[P(m(t.$t("market.renew")),1),n(r,null,{default:B((()=>[n(x(Y),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):p("",!0),e.appUser?p("",!0):(d(),z(k,{key:2,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:K((t=>ye(e)),["stop"])},{default:B((()=>[P(m(t.$t("market.subscribe")),1),n(r,null,{default:B((()=>[n(x(Y),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"]))])]),h("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",innerHTML:e.appDescription},null,8,Ja),h("div",Wa,[h("div",Qa,m(t.$t(`${x(W)[e.appType]}`)),1),1==(null==(s=e.appUser)?void 0:s.status)?(d(),u("div",Xa,m(t.$t("market.subUntil"))+m(null==(o=e.appUser)?void 0:o.expireAt)+m(t.$t("market.expiredOn")),1)):p("",!0),2==(null==(l=e.appUser)?void 0:l.status)?(d(),u("div",Ka,m(t.$t("market.haveBeen"))+m(null==(c=e.appUser)?void 0:c.expireAt)+m(t.$t("market.expiredOn")),1)):p("",!0)])]})),_:2},1032,["style","onClick"])))),128))])]))])]),x(v)?(d(),z(i,{key:0})):p("",!0),n(R,{class:"mobile_footer"}),x(N)?(d(),z($,{key:1,modelValue:x(N),"onUpdate:modelValue":a[2]||(a[2]=e=>X(N)?N.value=e:null),class:"payPC","show-close":!1},{default:B((()=>[n(Fi,{userInfo:x(A),appTypes:x(W),currentItem:x(E),onToAgreement:t.toAgreement,onClose:j,onSubscribe:ne},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):p("",!0),n(x(ke),{show:x(N),"onUpdate:show":a[3]||(a[3]=e=>X(N)?N.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"80%"}},{default:B((()=>[n(va,{userInfo:x(A),appTypes:x(W),currentItem:x(E),onToAgreement:t.toAgreement,onClose:j},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])])}}},[["__scopeId","data-v-0f06f250"]]);export{Ya as default};
