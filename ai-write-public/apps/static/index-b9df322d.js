import{a as e,_ as l}from"./Editor-a3906a01.js";/* empty css                  *//* empty css                    *//* empty css                  *//* empty css                 */import{m as a}from"./index-49085cc8.js";import{s as t}from"./index-cd40f614.js";/* empty css                   */import{a as s,r as o,N as n,o as i,d as r,e as d,j as u,f as p,g as m,p as c,m as f,k as v,S as x,t as y,F as g,y as h,i as b,h as j,q as V,a6 as k,B as S,E as _,aj as w,aM as N,aN as z}from"./index-0073a309.js";/* empty css                  *//* empty css                  */const M={class:"flex h-full overflow-auto"},T={class:"w-[58%]"},C={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},U={class:"flex items-center my-8"},H={class:"flex items-center mr-4"},L={class:"flex items-center mr-4"},W={class:"mr-2"},q=["innerHTML","onClick"],$=["innerHTML"],E={class:"flex-1 ml-4 box-right"},B=s({__name:"index",setup(s){const B=o("literature retrieval"),F=o(""),G=o(1),I=o(!0),P=o([]),A=o({}),D=o("请输入关键词或短句（中/英）"),J=n({pageNo:1,pageSize:20}),K=o(344),O=e=>{if(!B.value)return k.warning("请输入关键词或短句");1==e&&(J.pageNo=1);let l=P.value.map((e=>"cns"==e?{field:"cns",opt:2,vals:[e],val:"",synonymsWordVos:[]}:{field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]}));a("review",{key:B.value,page:J.pageNo-1,size:J.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l}).then((e=>{e&&e.data&&(A.value=e.data,A.value.content=t(A.value.content))}))},Q=()=>{G.value++};return i((()=>{O(),K.value=document.querySelector(".box-right").offsetWidth,window.onresize=()=>{K.value=document.querySelector(".box-right").offsetWidth}})),(a,t)=>{const s=S,o=_,n=w,i=N,k=z,R=e,X=l;return r(),d("div",M,[u("div",T,[u("div",C,[p(s,{class:"h-full !text-[24px]",modelValue:m(B),"onUpdate:modelValue":t[0]||(t[0]=e=>c(B)?B.value=e:null),placeholder:m(D),clearable:""},null,8,["modelValue","placeholder"]),p(o,{type:"primary",onClick:t[1]||(t[1]=e=>O(1))},{default:f((()=>t[7]||(t[7]=[v("查 询")]))),_:1})]),u("div",U,[u("div",H,[u("span",{class:x(["mr-2",m(I)?"text-[#409eff]":""])},"翻译",2),p(n,{modelValue:m(I),"onUpdate:modelValue":t[2]||(t[2]=e=>c(I)?I.value=e:null)},null,8,["modelValue"])]),u("div",L,[t[12]||(t[12]=u("span",{class:"mr-2"},"影响因子：",-1)),p(k,{modelValue:m(P),"onUpdate:modelValue":t[3]||(t[3]=e=>c(P)?P.value=e:null)},{default:f((()=>[p(i,{label:"3"},{default:f((()=>t[8]||(t[8]=[v(y("<3分"))]))),_:1}),p(i,{label:"5"},{default:f((()=>t[9]||(t[9]=[v("3-10分")]))),_:1}),p(i,{label:"10"},{default:f((()=>t[10]||(t[10]=[v(y(">10分"))]))),_:1}),p(i,{label:"cns"},{default:f((()=>t[11]||(t[11]=[v(y("CNS"))]))),_:1})])),_:1},8,["modelValue"])])]),u("div",null,[(r(!0),d(g,null,h(m(A).content,((e,l)=>(r(),d("div",{class:"flex mb-8",key:l},[u("div",W,y(l+1)+".",1),u("div",null,[u("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");F.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,q),m(I)?(r(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,$)):b("",!0)])])))),128))]),m(A)&&m(A).eleTotal?(r(),j(R,{key:0,class:"pb-10",total:m(A).eleTotal,page:m(J).pageNo,"onUpdate:page":t[4]||(t[4]=e=>m(J).pageNo=e),limit:m(J).pageSize,"onUpdate:limit":t[5]||(t[5]=e=>m(J).pageSize=e),onPagination:O},null,8,["total","page","limit"])):b("",!0)]),u("div",E,[(r(),j(X,{class:"h-[380px] fixed z-99",style:V({width:m(K)+"px"}),modelValue:m(F),"onUpdate:modelValue":t[6]||(t[6]=e=>c(F)?F.value=e:null),onClear:Q,key:m(G)},null,8,["style","modelValue"]))])])}}},[["__scopeId","data-v-b3f40a2c"]]);export{B as default};
