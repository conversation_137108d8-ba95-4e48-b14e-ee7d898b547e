/* empty css                  *//* empty css                   *//* empty css                */import{r as e,z as a,u as t,w as l,d as o,e as i,j as n,t as s,g as r,h as u,m as d,F as c,A as v,f as p,B as m,k as f,i as g,C as h,D as y,G as b,H as w,I as x,E as _,J as k,K as I,L as S,M as T,N as C,O as $,P as j,Q as z,R as B,o as N,S as A,T as q,U,V,W as O,X as R,a as E,b as M,Y as L,Z as H,$ as D,a0 as W,a1 as P,a2 as J,c as F,a3 as X,a4 as Z,p as Y,a5 as G,a6 as K,a7 as Q,a8 as ee,a9 as ae,aa as te,ab as le,ac as oe,v as ie,x as ne,y as se}from"./index-837d36db.js";import{g as re}from"./index-5fc9c050.js";/* empty css                 *//* empty css                  *//* empty css                  */import{c as ue,r as de,g as ce,s as ve,i as pe,o as me,a as fe,n as ge,m as he,b as ye,u as be,d as we,e as xe,f as _e,h as ke,j as Ie,k as Se,w as Te,l as Ce,p as $e,t as je,q as ze,v as Be,x as Ne,y as Ae,z as qe,A as Ue,B as Ve,C as Oe,D as Re,E as Ee,F as Me,G as Le,H as He,I as De,J as We,K as Pe,L as Je,M as Fe,N as Xe,O as Ze,P as Ye,Q as Ge}from"./index-0bf8abbd.js";import{r as Ke,a as Qe,f as ea}from"./use-route-a3f82fc9.js";/* empty css                   */const aa={class:"p-3 flex-1 rounded-md"},ta={class:"text-[14px] font-bold mb-2 text-gray-600"},la={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(I,{expose:S,emit:T}){const C=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),$=t(),j=e([]),z=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),B=e(""),N=I,A=N.type,q=N.fileVerify,U=N.label,V=N.required,O=N.max_length,R=N.options;"file"==A&&(B.value=null),"file-list"==A&&(B.value=[]);const E={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},M=()=>{let e="";return q.forEach(((a,t)=>{t<q.length-1?e+=E[a].join(",")+",":e+=E[a].join(",")})),e},L=T,H=(e,a,t)=>{},D=()=>{B.value=""},W=async e=>{const{file:a,onSuccess:t,onError:l}=e,o=new FormData;o.append("file",a),o.append("appId",$.params.uuid),o.append("user",C.value.userName);try{const e=await h(o);"file-list"==A?B.value.push({type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):B.value={type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(i){l(i)}return!1};R&&R.length>0&&(B.value=R[0]);return S({updateMessage:()=>{R&&R.length>0?B.value=R[0]:"file"==A?(B.value=null,j.value=[]):"file-list"==A?(B.value=[],j.value=[]):B.value=""}}),l(B,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=y,l=b,h=w,I=x,S=_,T=k;return o(),i("div",aa,[n("div",ta,s(r(U)),1),"paragraph"===r(A)||"text-input"===r(A)?(o(),u(t,{key:0,modelValue:B.value,"onUpdate:modelValue":a[0]||(a[0]=e=>B.value=e),type:"paragraph"===r(A)?"textarea":"text",rows:5,required:r(V),placeholder:`${r(U)}`,"show-word-limit":"",resize:"none",maxlength:r(O)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===r(A)?(o(),u(t,{key:1,modelValue:B.value,"onUpdate:modelValue":a[1]||(a[1]=e=>B.value=e),modelModifiers:{number:!0},type:"number",required:r(V),placeholder:`${r(U)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===r(A)?(o(),u(h,{key:2,modelValue:B.value,"onUpdate:modelValue":a[2]||(a[2]=e=>B.value=e),required:r(V),placeholder:`${r(U)}`},{default:d((()=>[(o(!0),i(c,null,v(r(R),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===r(A)||"file-list"===r(A)?(o(),u(T,{key:3,"file-list":j.value,"onUpdate:fileList":a[3]||(a[3]=e=>j.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":D,"before-remove":e.beforeRemove,limit:r(O),accept:M(),"auto-upload":!0,"on-Success":H,"http-request":W,"on-exceed":e.handleExceed},{default:d((()=>[p(S,{disabled:"file"===r(A)?1==j.value.length:j.value.length==r(O)},{default:d((()=>[p(I,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(r(m))])),_:1}),f("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):g("",!0)])}}},oa=Array.isArray,ia=e=>"string"==typeof e,na=e=>null!==e&&"object"==typeof e,sa=/\B([A-Z])/g,ra=(e=>{const a=Object.create(null);return t=>a[t]||(a[t]=e(t))})((e=>e.replace(sa,"-$1").toLowerCase()));function ua(e){if(oa(e)){const a={};for(let t=0;t<e.length;t++){const l=e[t],o=ia(l)?pa(l):ua(l);if(o)for(const e in o)a[e]=o[e]}return a}if(ia(e)||na(e))return e}const da=/;(?![^(]*\))/g,ca=/:([^]+)/,va=/\/\*[^]*?\*\//g;function pa(e){const a={};return e.replace(va,"").split(da).forEach((e=>{if(e){const t=e.split(ca);t.length>1&&(a[t[0].trim()]=t[1].trim())}})),a}function ma(e){let a="";if(ia(e))a=e;else if(oa(e))for(let t=0;t<e.length;t++){const l=ma(e[t]);l&&(a+=l+" ")}else if(na(e))for(const t in e)e[t]&&(a+=t+" ");return a.trim()}let fa=0;function ga(){const e=I(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++fa}`}function ha(e,a){if(!pe||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};T(l),C(l),me((()=>{e.value&&t.observe(e.value)}))}const[ya,ba]=fe("sticky");const wa=$e($({name:ya,props:{zIndex:ge,position:he("top"),container:Object,offsetTop:ye(0),offsetBottom:ye(0)},emits:["scroll","change"],setup(a,{emit:t,slots:o}){const i=e(),n=be(i),s=j({fixed:!1,width:0,height:0,transform:0}),r=e(!1),u=z((()=>we("top"===a.position?a.offsetTop:a.offsetBottom))),d=z((()=>{if(r.value)return;const{fixed:e,height:a,width:t}=s;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=z((()=>{if(!s.fixed||r.value)return;const e=xe(_e(a.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[a.position]:`${u.value}px`});return s.transform&&(e.transform=`translate3d(0, ${s.transform}px, 0)`),e})),v=()=>{if(!i.value||Ie(i))return;const{container:e,position:l}=a,o=Se(i),n=ce(window);if(s.width=o.width,s.height=o.height,"top"===l)if(e){const a=Se(e),t=a.bottom-u.value-s.height;s.fixed=u.value>o.top&&a.bottom>0,s.transform=t<0?t:0}else s.fixed=u.value>o.top;else{const{clientHeight:a}=document.documentElement;if(e){const t=Se(e),l=a-t.top-u.value-s.height;s.fixed=a-u.value<o.bottom&&a>t.top,s.transform=l<0?-l:0}else s.fixed=a-u.value<o.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:s.fixed})})(n)};return l((()=>s.fixed),(e=>t("change",e))),ke("scroll",v,{target:n,passive:!0}),ha(i,v),l([Te,Ce],(()=>{i.value&&!Ie(i)&&s.fixed&&(r.value=!0,B((()=>{const e=Se(i);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:ba({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[xa,_a]=fe("swipe"),ka={loop:je,width:ge,height:ge,vertical:Boolean,autoplay:ye(0),duration:ye(500),touchable:je,lazyRender:Boolean,initialSwipe:ye(0),indicatorColor:String,showIndicators:je,stopPropagation:je},Ia=Symbol(xa);const Sa=$e($({name:xa,props:ka,emits:["change","dragStart","dragEnd"],setup(a,{emit:t,slots:o}){const i=e(),n=e(),s=j({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=ze(),{children:d,linkChildren:c}=Be(Ia),v=z((()=>d.length)),m=z((()=>s[a.vertical?"height":"width"])),f=z((()=>a.vertical?u.deltaY.value:u.deltaX.value)),g=z((()=>{if(s.rect){return(a.vertical?s.rect.height:s.rect.width)-m.value*v.value}return 0})),h=z((()=>m.value?Math.ceil(Math.abs(g.value)/m.value):v.value)),y=z((()=>v.value*m.value)),b=z((()=>(s.active+v.value)%v.value)),w=z((()=>{const e=a.vertical?"vertical":"horizontal";return u.direction.value===e})),x=z((()=>{const e={transitionDuration:`${s.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(m.value){const t=a.vertical?"height":"width",l=a.vertical?"width":"height";e[t]=`${y.value}px`,e[l]=a[l]?`${a[l]}px`:""}return e})),_=(e,t=0)=>{let l=e*m.value;a.loop||(l=Math.min(l,-g.value));let o=t-l;return a.loop||(o=Oe(o,g.value,0)),o},k=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=s,n=(e=>{const{active:t}=s;return e?a.loop?Oe(t+e,-1,v.value):Oe(t+e,0,h.value):t})(e),r=_(n,l);if(a.loop){if(d[0]&&r!==g.value){const e=r<g.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=n,s.offset=r,o&&n!==i&&t("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Ue((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let $;const q=()=>clearTimeout($),U=()=>{q(),+a.autoplay>0&&v.value>1&&($=setTimeout((()=>{S(),U()}),+a.autoplay))},V=(e=+a.initialSwipe)=>{if(!i.value)return;const t=()=>{var t,l;if(!Ie(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};s.rect=e,s.width=+(null!=(t=a.width)?t:e.width),s.height=+(null!=(l=a.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),s.active=e,s.swiping=!0,s.offset=_(e),d.forEach((e=>{e.setOffset(0)})),U()};Ie(i)?B().then(t):t()},O=()=>V(s.active);let R;const E=e=>{!a.touchable||e.touches.length>1||(u.start(e),r=!1,R=Date.now(),q(),I())},M=()=>{if(!a.touchable||!s.swiping)return;const e=Date.now()-R,l=f.value/e;if((Math.abs(l)>.25||Math.abs(f.value)>m.value/2)&&w.value){const e=a.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=a.loop?e>0?f.value>0?-1:1:0:-Math[f.value>0?"ceil":"floor"](f.value/m.value),k({pace:t,emitChange:!0})}else f.value&&k({pace:0});r=!1,s.swiping=!1,t("dragEnd",{index:b.value}),U()},L=(e,t)=>{const l=t===b.value,o=l?{backgroundColor:a.indicatorColor}:void 0;return p("i",{style:o,class:_a("indicator",{active:l})},null)};return Ne({prev:()=>{I(),u.reset(),Ue((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:O,swipeTo:(e,t={})=>{I(),u.reset(),Ue((()=>{let l;l=a.loop&&e===v.value?0===s.active?0:e:e%v.value,t.immediate?Ue((()=>{s.swiping=!1})):s.swiping=!1,k({pace:l-s.active,emitChange:!0})}))}}),c({size:m,props:a,count:v,activeIndicator:b}),l((()=>a.initialSwipe),(e=>V(+e))),l(v,(()=>V(s.active))),l((()=>a.autoplay),U),l([Te,Ce,()=>a.width,()=>a.height],O),l(Ae(),(e=>{"visible"===e?U():q()})),N(V),A((()=>V(s.active))),qe((()=>V(s.active))),T(q),C(q),ke("touchmove",(e=>{if(a.touchable&&s.swiping&&(u.move(e),w.value)){!a.loop&&(0===s.active&&f.value>0||s.active===v.value-1&&f.value<0)||(Ve(e,a.stopPropagation),k({offset:f.value}),r||(t("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:_a()},[p("div",{ref:n,style:x.value,class:_a("track",{vertical:a.vertical}),onTouchstartPassive:E,onTouchend:M,onTouchcancel:M},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):a.showIndicators&&v.value>1?p("div",{class:_a("indicators",{vertical:a.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[Ta,Ca]=fe("tabs");var $a=$({name:Ta,props:{count:Re(Number),inited:Boolean,animated:Boolean,duration:Re(ge),swipeable:Boolean,lazyRender:Boolean,currentIndex:Re(Number)},emits:["change"],setup(a,{emit:t,slots:o}){const i=e(),n=e=>t("change",e),s=()=>{var e;const t=null==(e=o.default)?void 0:e.call(o);return a.animated||a.swipeable?p(Sa,{ref:i,loop:!1,class:Ca("track"),duration:1e3*+a.duration,touchable:a.swipeable,lazyRender:a.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},r=e=>{const t=i.value;t&&t.state.active!==e&&t.swipeTo(e,{immediate:!a.inited})};return l((()=>a.currentIndex),r),N((()=>{r(a.currentIndex)})),Ne({swipeRef:i}),()=>p("div",{class:Ca("content",{animated:a.animated||a.swipeable})},[s()])}});const[ja,za]=fe("tabs"),Ba={type:he("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ye(0),duration:ye(.3),animated:Boolean,ellipsis:je,swipeable:Boolean,scrollspy:Boolean,offsetTop:ye(0),background:String,lazyRender:je,showHeader:je,lineWidth:ge,lineHeight:ge,beforeChange:Function,swipeThreshold:ye(5),titleActiveColor:String,titleInactiveColor:String},Na=Symbol(ja);var Aa=$({name:ja,props:Ba,emits:["change","scroll","rendered","clickTab","update:active"],setup(a,{emit:t,slots:o}){let i,n,s,r,u;const d=e(),c=e(),v=e(),m=e(),f=ga(),g=be(d),[h,y]=function(){const a=e([]),t=[];return S((()=>{a.value=[]})),[a,e=>(t[e]||(t[e]=t=>{a.value[e]=t}),t[e])]}(),{children:b,linkChildren:w}=Be(Na),x=j({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=z((()=>b.length>+a.swipeThreshold||!a.ellipsis||a.shrink)),k=z((()=>({borderColor:a.color,background:a.background}))),I=(e,a)=>{var t;return null!=(t=e.name)?t:a},T=z((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),C=z((()=>we(a.offsetTop))),$=z((()=>a.sticky?C.value+i:0)),N=e=>{const t=c.value,l=h.value;if(!(_.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,t){let l,o=0;const i=e.scrollLeft,n=0===t?1:Math.round(1e3*t/16);let s=i;return function t(){s+=(a-i)/n,e.scrollLeft=s,++o<n&&(l=de(t))}(),function(){ue(l)}}(t,i,e?0:+a.duration)},q=()=>{const e=x.inited;B((()=>{const t=h.value;if(!t||!t[x.currentIndex]||"line"!==a.type||Ie(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=a,n=l.offsetLeft+l.offsetWidth/2,s={width:Ee(o),backgroundColor:a.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(s.transitionDuration=`${a.duration}s`),Me(i)){const e=Ee(i);s.height=e,s.borderRadius=e}x.lineStyle=s}))},U=(e,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(e);if(!Me(o))return;const i=b[o],n=I(i,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||N(),q()),n!==a.active&&(t("update:active",n),r&&t("change",n,i.title)),s&&!a.scrollspy&&He(Math.ceil(De(d.value)-C.value))},V=(e,a)=>{const t=b.find(((a,t)=>I(a,t)===e)),l=t?b.indexOf(t):0;U(l,a)},O=(e=!1)=>{if(a.scrollspy){const t=b[x.currentIndex].$el;if(t&&g.value){const l=De(t,g.value)-$.value;n=!0,u&&u(),u=function(e,a,t,l){let o,i=ce(e);const n=i<a,s=0===t?1:Math.round(1e3*t/16),r=(a-i)/s;return function t(){i+=r,(n&&i>a||!n&&i<a)&&(i=a),ve(e,i),n&&i<a||!n&&i>a?o=de(t):l&&(o=de(l))}(),function(){ue(o)}}(g.value,l,e?0:+a.duration,(()=>{n=!1}))}}},R=(e,l,o)=>{const{title:i,disabled:n}=b[l],s=I(b[l],l);n||(We(a.beforeChange,{args:[s],done:()=>{U(l),O()}}),Ke(e)),t("clickTab",{name:s,title:i,event:o,disabled:n})},E=e=>{s=e.isFixed,t("scroll",e)},M=()=>{if("line"===a.type&&b.length)return p("div",{class:za("line"),style:x.lineStyle},null)},L=()=>{var e,t,l;const{type:i,border:n,sticky:s}=a,r=[p("div",{ref:s?void 0:v,class:[za("wrap"),{[Le]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:za("nav",[i,{shrink:a.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(R))),M(),null==(t=o["nav-right"])?void 0:t.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return s?p("div",{ref:v},[r]):r},H=()=>{q(),B((()=>{var e,a;N(!0),null==(a=null==(e=m.value)?void 0:e.swipeRef.value)||a.resize()}))};l((()=>[a.color,a.duration,a.lineWidth,a.lineHeight]),q),l(Te,H),l((()=>a.active),(e=>{e!==T.value&&V(e)})),l((()=>b.length),(()=>{x.inited&&(V(a.active),q(),B((()=>{N(!0)})))}));return Ne({resize:H,scrollTo:e=>{B((()=>{V(e),O(!0)}))}}),A(q),qe(q),me((()=>{V(a.active,!0),B((()=>{x.inited=!0,v.value&&(i=Se(v.value).height),N(!0)}))})),ha(d,q),ke("scroll",(()=>{if(a.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=Se(b[e].$el);if(a>$.value)return 0===e?0:e-1}return b.length-1})();U(e)}}),{target:g,passive:!0}),w({id:f,props:a,setLine:q,scrollable:_,onRendered:(e,a)=>t("rendered",e,a),currentName:T,setTitleRefs:y,scrollIntoView:N}),()=>p("div",{ref:d,class:za([a.type])},[a.showHeader?a.sticky?p(wa,{container:d.value,offsetTop:C.value,onScroll:E},{default:()=>[L()]}):L():null,p($a,{ref:m,count:b.length,inited:x.inited,animated:a.animated,duration:a.duration,swipeable:a.swipeable,lazyRender:a.lazyRender,currentIndex:x.currentIndex,onChange:U},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const qa=Symbol(),[Ua,Va]=fe("tab"),Oa=$({name:Ua,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:ge,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:je},setup(e,{slots:a}){const t=z((()=>{const a={},{type:t,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:s}=e;l&&"card"===t&&(a.borderColor=l,o||(i?a.backgroundColor=l:a.color=l));const r=i?n:s;return r&&(a.color=r),a})),l=()=>{const t=p("span",{class:Va("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Me(e.badge)&&""!==e.badge?p(Pe,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>p("div",{id:e.id,role:"tab",class:[Va([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[Ra,Ea]=fe("swipe-item");const Ma=$e($({name:Ra,setup(e,{slots:a}){let t;const l=j({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Je(Ia);if(!o)return;const n=z((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),s=z((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,s=o.count.value-1,r=0===n&&e?s:n-1,u=n===s&&e?0:n+1;return t=i.value===n||i.value===r||i.value===u,t}));return N((()=>{B((()=>{l.mounted=!0}))})),Ne({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:Ea(),style:n.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[La,Ha]=fe("tab");const Da=$e($({name:La,props:xe({},Qe,{dot:Boolean,name:ge,badge:ge,title:String,disabled:Boolean,titleClass:Fe,titleStyle:[String,Object],showZeroBadge:je}),setup(a,{slots:t}){const o=ga(),i=e(!1),n=I(),{parent:s,index:r}=Je(Na);if(!s)return;const u=()=>{var e;return null!=(e=a.name)?e:r.value},d=z((()=>{const e=u()===s.currentName.value;return e&&!i.value&&(i.value=!0,s.props.lazyRender&&B((()=>{s.onRendered(u(),a.title)}))),e})),c=e(""),v=e("");q((()=>{const{titleClass:e,titleStyle:t}=a;c.value=e?ma(e):"",v.value=t&&"string"!=typeof t?function(e){let a="";if(!e||ia(e))return a;for(const t in e){const l=e[t];(ia(l)||"number"==typeof l)&&(a+=`${t.startsWith("--")?t:ra(t)}:${l};`)}return a}(ua(t)):t}));const m=e(!d.value);return l(d,(e=>{e?m.value=!1:Ue((()=>{m.value=!0}))})),l((()=>a.title),(()=>{s.setLine(),s.scrollIntoView()})),U(qa,d),Ne({id:o,renderTitle:e=>p(Oa,R({key:o,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:a=>e(n.proxy,r.value,a)},Xe(s.props,["type","color","shrink"]),Xe(a,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title})}),()=>{var e;const a=`${s.id}-${r.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=s.props;if(!t.default&&!l)return;const v=u||d.value;if(l||n)return p(Ma,{id:o,role:"tabpanel",class:Ha("panel-wrapper",{inactive:m.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":a,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:Ha("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const f=i.value||u||!c?null==(e=t.default)?void 0:e.call(t):null;return V(p("div",{id:o,role:"tabpanel",class:Ha("panel"),tabindex:v?0:-1,"aria-labelledby":a,"data-allow-mismatch":"attribute"},[f]),[[O,v]])}}})),Wa=$e(Aa),Pa={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ja={class:"pc_container",style:{display:"flex"}},Fa={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Xa={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Za={class:"pc_right bg-[#fff]"},Ya={id:"typing-area"},Ga={key:0,class:"decContaniner nop bg-[#fff]"},Ka={key:0,class:"img_box"},Qa=["src"],et={key:1,class:"icon"},at={class:"process_text label_width"},tt={key:0,class:"process"},lt={key:0,class:"img_box"},ot=["src"],it={key:1,class:"icon"},nt={class:"process"},st={class:"process_text"},rt={key:2},ut=["src"],dt=["src"],ct={class:"mobile_container"},vt={class:"p-3",style:{display:"flex","justify-content":"space-between"}},pt={class:"mobile_right"},mt={id:"typing-area"},ft={key:0,class:"decContaniner nop bg-[#fff]"},gt={key:0,class:"img_box"},ht=["src"],yt={key:1,class:"icon"},bt={class:"process_text label_width"},wt=(e=>(ne("data-v-1b56682f"),e=e(),se(),e))((()=>n("div",null,[n("div",{class:"process"})],-1))),xt={key:0,class:"img_box"},_t=["src"],kt={key:1,class:"icon"},It={class:"process"},St={class:"process_text"},Tt={key:2},Ct=E({__name:"index",props:{currentItem:{type:Object,default:()=>{}}},setup(m){const h=m,y=re("loading.png"),b=re("copy.png"),w=j({}),k=t(),I={},S=e([]),T=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),C=e(null),{t:$,locale:B}=M(),A=L(),q=e(!1);let U=e("a"),V=e(!0),O=e("");const R=e(""),E=e(null),ne=e(null),se=e(["1","2"]),ue=e(!1),de=e(!1),ce=e(!1);let ve;const pe=async()=>{var e;await G({appId:k.params.uuid,user:T.value.userName,mode:null==(e=C.value)?void 0:e.mode,task_id:R.value}),setTimeout((()=>{Ee.abort(),Me=!0,$e.value=[],ue.value=!1,je.value.length&&je.value.forEach((e=>{e.status=!0})),Je()}),0)},me=()=>{ce.value=!1},fe=async(e,a)=>{var t;let l=D();if(null==(t=T.value)?void 0:t.userId){const t={appUuid:a,priceId:e.priceId,monthNum:e.monthNum};let l=await K(t);l&&(Q({type:"success",message:$("tool.sS")}),setTimeout((()=>{location.href=l}),1e3))}else l&&"zh-CN"!=l?A.push((isUp,"/login")):window.addLoginDom()};N((async()=>{var e,t;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=k.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=k.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),B.value=H(),a.get("userInfo"))await ta(),Te(),await W(k.params.appUuid);else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=D();e&&"zh-CN"!=e?A.push("/login"):window.addLoginDom()}}));const ge=e=>{U.value=e.name},he=async()=>{ce.value=!0},ye=()=>{k.params.uuid&&ee({appId:k.params.uuid,user:T.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(S.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;I[t]={label:e[a].label},w[t]=""})))}))},be=z((()=>!!S.value.length)),we=z((()=>S.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),xe=()=>{k.params.uuid&&ae({appId:k.params.uuid,user:T.value.userName}).then((e=>{C.value={...e}}))},_e=e(!1),ke=e(!1),Ie=e(!1),Se=e(!1),Te=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=T.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:k.params.uuid,userUuid:null==(a=T.value)?void 0:a.openid}];await P.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},Ce=()=>{var e,a;if(0!=we.value.length||(t=w,Object.values(t).some((e=>e)))){var t;for(let e in w)if(we.value.includes(e)&&!w[e])return void Q({message:`${I[e].label}${$("tool.requiredfield")}`,type:"error"});(null==(e=C.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=C.value)?void 0:a.mode)?Q({type:"success",message:$("tool.planning")}):"completion"==C.value.mode?(V.value=!1,setTimeout((()=>{U.value="b"}),1e3),Pe()):(V.value=!1,setTimeout((()=>{U.value="b"}),1e3),We()))}else Q({message:`${$("tool.enterquestion")}`,type:"error"})},$e=e([]);var je=e([]),ze=e([]);const Be=e(""),Ne=e(0),Ae=e(""),qe=e(!1),Ue=e(!1);let Ve=e(0);const Oe=e(!1),Re=e(!1);let Ee,Me=!1,Le=!1;l(je,(()=>{He()}),{deep:!0});const He=()=>{Ve.value<je.value.length&&(ze.value.push(je.value[Ve.value]),Ve.value++,setTimeout(He,1e3))},De=()=>{Ne.value<Be.value.length?(qe.value=!0,Ae.value+=Be.value.charAt(Ne.value),Ne.value++,setTimeout(De,20)):(Re.value=!1,qe.value=!1,Ie.value=!0,Je())},We=async()=>{de.value=!0,ue.value=!0,O.value="",je.value=[],ze.value=[],Ve.value=0,Ae.value="",Be.value="",$e.value=[],Oe.value=!1,Me=!1,Ne.value=0,Ee=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,_e.value=!0,ke.value=!0,Se.value=!1,await ea(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:k.params.uuid,user:T.value.userName,inputs:{...w,outputLanguage:w.outputLanguage?w.outputLanguage:"中文"==te()?"简体中文":te()},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:k.params.appUuid}),onmessage(e){var a,t,l,o,i,n,s,r,u,d;if(e.data.trim())try{const c=JSON.parse(e.data);if(R.value=c.task_id,c.error)throw new Error(c.error);"智能体推理思维链"==(null==(a=null==c?void 0:c.data)?void 0:a.title)&&"node_finished"===c.event&&(Re.value=!0,Be.value=null==(o=JSON.parse(null==(l=null==(t=null==c?void 0:c.data)?void 0:t.outputs)?void 0:l.text))?void 0:o.text,De()),"node_started"!==c.event||Oe.value||"开始"==(null==(i=null==c?void 0:c.data)?void 0:i.title)||je.value.push({node_id:null==(n=null==c?void 0:c.data)?void 0:n.node_id,title:null==(s=null==c?void 0:c.data)?void 0:s.title,status:!1}),"error"===c.event&&(5047==c.code&&(Ue.value=!0),Oe.value=!0,ue.value=!1,Me=!0,ke.value=!1,O.value=null==c?void 0:c.message),"node_finished"===c.event&&je.value.forEach((e=>{var a;e.node_id==(null==(a=null==c?void 0:c.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===c.event&&(q.value=!0,Oe.value=!0,$e.value.push(null==(r=null==c?void 0:c.data)?void 0:r.text),Re.value||Je()),"workflow_started"===c.event&&(_e.value=!1),"workflow_finished"===c.event&&(Me=!0,Oe.value=!0,ue.value=!1,q.value||($e.value.push(null==(d=null==(u=null==c?void 0:c.data)?void 0:u.outputs)?void 0:d.text),Re.value||Je()))}catch(c){Ke(c)}},onerror(e){Ke(e)},signal:Ee.signal,openWhenHidden:!0})}catch(e){Ke()}},Pe=async()=>{O.value="",$e.value=[],Re.value=!1,Me=!1,Ee=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,_e.value=!0,ke.value=!0,Se.value=!1,await ea(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:k.params.uuid,user:T.value.userName,inputs:{...w,outputLanguage:te()},files:[],response_mode:"streaming",appUuid:k.params.appUuid,requestId:crypto.randomUUID()}),onmessage(e){if(_e.value=!1,Ie.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(R.value=a.task_id,a.error)throw new Error(a.error);"error"===a.event&&(5047==a.code&&(Ue.value=!0),Me=!0,O.value=null==a?void 0:a.message,Ie.value=!1,ke.value=!1),"message"===a.event&&($e.value.push(null==a?void 0:a.answer),Re.value||Je()),"message_end"===a.event&&(Me=!0)}catch(a){Ke(a)}},onerror(e){Ke(e)},signal:Ee.signal,openWhenHidden:!0})}catch(e){Ke()}},Je=()=>{if(0===$e.value.length)return Re.value=!1,Le=!0,void Fe();Re.value=!0;const e=$e.value.shift();if(!e)return Re.value=!1,Le=!0,void Fe();Xe(e).then((()=>{Je()}))},Fe=()=>{Le&&Me&&(ke.value=!1,Ie.value=!1,Se.value=!0)},Xe=e=>new Promise((a=>{let t=0;ve=setInterval((()=>{if(t<e.length){O.value+=e[t++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const l=document.getElementsByClassName("mobile_right");l[0].scrollTop=l[0].scrollHeight}else clearInterval(ve),a()}),0)})),Ke=()=>{setTimeout((()=>{Ee.abort()}),0),ue.value=!1,_e.value=!1,Ie.value=!1,ke.value=!1,Re.value=!1,Q.error($("tool.accessbusy")),O.value=$("tool.accessbusy")},Qe=async()=>{try{await navigator.clipboard.writeText(O.value),Q({type:"success",message:$("tool.copysuccess")})}catch(e){Q(e)}},aa=()=>{for(let e in w)w[e]="";E.value.forEach((e=>{e.updateMessage()})),ne.value.forEach((e=>{e.updateMessage()}))},ta=async()=>{if(localStorage.getItem("yudaoToken"))return ye(),void xe();try{await J({userId:T.value.userId,userName:T.value.userName,realName:T.value.realName,avatar:T.value.avatar,plaintextUserId:T.value.plaintextUserId,mobile:T.value.mobile,email:T.value.email}).then((async e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),ye(),xe())}))}catch(e){}};return(e,a)=>{const t=_,l=x,m=le,k=oe,I=F("v-md-preview"),C=ie;return o(),i("div",Pa,[n("div",Ja,[r(be)?(o(),i(c,{key:0},[n("div",Fa,[(o(!0),i(c,null,v(r(S),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(la,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(w)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(w)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:E},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Xa,[p(t,{onClick:aa},{default:d((()=>[f(s(e.$t("tool.clear")),1)])),_:1}),p(t,{onClick:Ce,loading:r(ke),type:"primary"},{default:d((()=>[f(s(e.$t("tool.execute")),1)])),_:1},8,["loading"])])]),n("div",Za,[n("div",Ya,[r(ze).length>0||r(Ae)||r(de)?(o(),i("div",Ga,[p(k,{modelValue:r(se),"onUpdate:modelValue":a[0]||(a[0]=e=>Y(se)?se.value=e:null)},{default:d((()=>[p(m,{name:"1"},{title:d((()=>[r(ue)?(o(),i("div",Ka,[n("img",{src:r(y),alt:"loading"},null,8,Qa)])):(o(),i("div",et,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),f(" "+s(e.$t("tool.execution_progress")),1)])),default:d((()=>[(o(!0),i(c,null,v(r(ze),((a,t)=>(o(),i("div",{key:t,class:"process"},[n("div",at,s(a.title),1),f("    "),n("span",{style:{color:"#36b15e"},class:Z(a.status?"":"loading-text")},s(a.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),n("div",null,[r(Ae)?(o(),i("div",tt)):g("",!0)]),r(Ae)?(o(),u(m,{key:0,name:"2"},{title:d((()=>[r(qe)?(o(),i("div",lt,[n("img",{src:r(y),alt:"loading"},null,8,ot)])):(o(),i("div",it,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),f(" "+s(e.$t("tool.reasoning_process")),1)])),default:d((()=>[n("div",nt,[n("div",st,s(r(Ae)),1)])])),_:1})):g("",!0)])),_:1},8,["modelValue"])])):g("",!0),r(O)&&!r(Ue)?(o(),u(I,{key:1,text:r(O),id:"previewMd"},null,8,["text"])):g("",!0),r(Ue)?(o(),i("div",rt,[f(s(r(O))+" ",1),p(t,{type:"text",onClick:he},{default:d((()=>[f(s(e.$t("market.subscribe")),1)])),_:1})])):g("",!0),n("div",null,[r(Ie)?(o(),i("img",{key:0,src:r(y),alt:"loading",class:"spinner"},null,8,ut)):g("",!0),r(Ie)?(o(),i("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:pe},s(e.$t("tool.stopGeneration")),1)):g("",!0),r(Se)?(o(),i("img",{key:2,onClick:Qe,src:r(b),alt:"",style:{width:"20px"},class:"copy"},null,8,dt)):g("",!0)])])])],64)):g("",!0)]),n("div",ct,[p(r(Wa),{active:r(U),shrink:"","line-width":"20",onClickTab:ge},{default:d((()=>[p(r(Da),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(r(S),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(la,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(w)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(w)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:ne},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",vt,[p(t,{onClick:aa},{default:d((()=>[f("Clear")])),_:1}),p(t,{onClick:a[1]||(a[1]=e=>Ce()),loading:r(ke),type:"primary"},{default:d((()=>[f("Execute")])),_:1},8,["loading"])])])),_:1}),p(r(Da),{title:"结果",name:"b",disabled:r(V)},{default:d((()=>[n("div",pt,[n("div",mt,[r(je).length>0||r(Ae)?(o(),i("div",ft,[p(k,{modelValue:r(se),"onUpdate:modelValue":a[2]||(a[2]=e=>Y(se)?se.value=e:null)},{default:d((()=>[p(m,{name:"1"},{title:d((()=>[r(ue)?(o(),i("div",gt,[n("img",{src:r(y),alt:"loading"},null,8,ht)])):(o(),i("div",yt,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),f(" "+s(e.$t("tool.execution_progress")),1)])),default:d((()=>[(o(!0),i(c,null,v(r(je),((a,t)=>(o(),i("div",{key:t,class:"process"},[n("div",bt,s(a.title),1),f("    "),n("span",{style:{color:"#36b15e"},class:Z(a.status?"":"loading-text")},s(a.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),wt,r(Ae)?(o(),u(m,{key:0,title:"推导过程",name:"2"},{title:d((()=>[r(qe)?(o(),i("div",xt,[n("img",{src:r(y),alt:"loading"},null,8,_t)])):(o(),i("div",kt,[p(l,null,{default:d((()=>[p(r(X))])),_:1})])),f(" "+s(e.$t("tool.reasoning_process")),1)])),default:d((()=>[n("div",It,[n("div",St,s(r(Ae)),1)])])),_:1})):g("",!0)])),_:1},8,["modelValue"])])):g("",!0),r(O)&&!r(Ue)?(o(),u(I,{key:1,text:r(O),id:"previewMd"},null,8,["text"])):g("",!0),r(Ue)?(o(),i("div",Tt,[f(s(r(O))+" ",1),p(t,{type:"text",onClick:he},{default:d((()=>[f(s(e.$t("market.subscribe")),1)])),_:1})])):g("",!0)])])])),_:1},8,["disabled"])])),_:1},8,["active"])]),r(ce)?(o(),u(C,{key:0,modelValue:r(ce),"onUpdate:modelValue":a[3]||(a[3]=e=>Y(ce)?ce.value=e:null),class:"payPC","show-close":!1},{default:d((()=>[p(Ze,{userInfo:r(T),appTypes:e.appTypes,currentItem:h.currentItem,onToAgreement:e.toAgreement,onClose:me,onSubscribe:fe},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):g("",!0),r(ce)?(o(),u(r(Ge),{key:1,show:r(ce),"onUpdate:show":a[4]||(a[4]=e=>Y(ce)?ce.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:d((()=>[p(Ye,{userInfo:r(T),appTypes:e.appTypes,currentItem:h.currentItem,onToAgreement:e.toAgreement,onClose:me},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])):g("",!0)])}}},[["__scopeId","data-v-1b56682f"]]);export{Ct as default};
