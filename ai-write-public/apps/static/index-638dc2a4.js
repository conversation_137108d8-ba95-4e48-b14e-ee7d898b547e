import{M as a,b as e,u as n,r as s,o,a8 as t,aE as i,aA as l,d as c,e as r,j as p,a as d}from"./index-c3ec2d2e.js";const u=d(a({__name:"index",setup(a){const{t:d}=e(),u=n(),v=s(),I=JSON.parse(decodeURIComponent(u.params.payInfo));o((async()=>{g();const a=navigator.userAgent;if(null!=a)if(a.includes("MicroMessenger"))t.warning(d("tool.pleasescanwithalipay"));else if(a.includes("AlipayClient")){const a=await i(I);location.replace(a)}}));const f=()=>{location.replace(location.origin)},g=()=>{v.value=setInterval((async()=>{"PAID"===(await l(I.piId)).payStatus&&(location.replace(location.origin),clearInterval(v.value))}),2e3)};return(a,e)=>(c(),r("div",null,[p("button",{onClick:f},"返回首页")]))}}),[["__scopeId","data-v-983d25d9"]]);export{u as default};
