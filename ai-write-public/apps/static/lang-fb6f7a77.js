import{I as e}from"./index-bcb508ed.js";function t(e){let t,n,o,a=!1;return function(r){void 0===t?(t=r,n=0,o=-1):t=function(e,t){const n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}(t,r);const i=t.length;let c=0;for(;n<i;){a&&(10===t[n]&&(c=++n),a=!1);let r=-1;for(;n<i&&-1===r;++n)switch(t[n]){case 58:-1===o&&(o=n-c);break;case 13:a=!0;case 10:r=n}if(-1===r)break;e(t.subarray(c,r),o),c=n,o=-1}c===i?t=void 0:0!==c&&(t=t.subarray(c),n-=c)}}var n=globalThis&&globalThis.__rest||function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]])}return n};const o="text/event-stream",a="last-event-id";function r(e,r){var{signal:c,headers:s,onopen:d,onmessage:l,onclose:u,onerror:f,openWhenHidden:h,fetch:b}=r,g=n(r,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((n,r)=>{const p=Object.assign({},s);let y;function v(){y.abort(),document.hidden||m()}p.accept||(p.accept=o),h||document.addEventListener("visibilitychange",v);let A=1e3,w=0;function I(){document.removeEventListener("visibilitychange",v),window.clearTimeout(w),y.abort()}null==c||c.addEventListener("abort",(()=>{I(),n()}));const S=null!=b?b:window.fetch,M=null!=d?d:i;async function m(){var o;y=new AbortController;try{const o=await S(e,Object.assign(Object.assign({},g),{headers:p,signal:y.signal}));await M(o),await async function(e,t){const n=e.getReader();let o;for(;!(o=await n.read()).done;)t(o.value)}(o.body,t(function(e,t,n){let o={data:"",event:"",id:"",retry:void 0};const a=new TextDecoder;return function(r,i){if(0===r.length)null==n||n(o),o={data:"",event:"",id:"",retry:void 0};else if(i>0){const n=a.decode(r.subarray(0,i)),c=i+(32===r[i+1]?2:1),s=a.decode(r.subarray(c));switch(n){case"data":o.data=o.data?o.data+"\n"+s:s;break;case"event":o.event=s;break;case"id":e(o.id=s);break;case"retry":const n=parseInt(s,10);isNaN(n)||t(o.retry=n)}}}}((e=>{e?p[a]=e:delete p[a]}),(e=>{A=e}),l))),null==u||u(),I(),n()}catch(i){if(!y.signal.aborted)try{const e=null!==(o=null==f?void 0:f(i))&&void 0!==o?o:A;window.clearTimeout(w),w=window.setTimeout(m,e)}catch(c){I(),r(c)}}}m()}))}function i(e){const t=e.headers.get("content-type");if(!(null==t?void 0:t.startsWith(o)))throw new Error(`Expected content-type to be ${o}, Actual: ${t}`)}const c={to:[String,Object],url:String,replace:Boolean};function s({to:e,url:t,replace:n,$router:o}){e&&o?o[n?"replace":"push"](e):t&&(n?location.replace(t):location.href=t)}function d(){const t=e().proxy;return()=>s(t)}const l={"zh-CN":"AI智能体-梅斯小智/梅斯医学",en:"AI Agent - MedSci xAI/MedSci Medicine",es:"Agente IA - MedSci xAI/MedSci Medicina",ja:"AIエージェント - MedSci xAI/メドサイ医学","zh-TW":"AI智能體-梅斯小智/梅斯醫學",vi:"Tác nhân AI - MedSci xAI/Y học MedSci",ko:"AI 에이전트 - MedSci xAI/메드사이 의학",pt:"Agente IA - MedSci xAI/Medicina MedSci",ar:"عميل الذكاء الاصطناعي - MedSci xAI/طب MedSci",id:"Agen AI - MedSci xAI/Kedokteran MedSci",ms:"Ejen AI - MedSci xAI/Perubatan MedSci"};export{c as a,r as f,s as r,l as t,d as u};
