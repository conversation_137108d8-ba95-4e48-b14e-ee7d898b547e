import{M as e,b as a,u as n,o as s,a6 as i,aD as t,d as o,e as l}from"./index-f7b98bee.js";const c=e({__name:"index",setup(e){const{t:c}=a(),r=n(),p=JSON.parse(decodeURIComponent(r.params.payInfo));return s((async()=>{const e=navigator.userAgent;let a=(new Date).getTime();if(a>p.time&&location.replace(location.origin),null!=e)if(e.includes("MicroMessenger"))i.warning(c("tool.pleasescanwithalipay"));else if(e.includes("AlipayClient")){p.time=a;const e=await t(p);location.replace(e)}})),(e,a)=>(o(),l("div"))}});export{c as default};
