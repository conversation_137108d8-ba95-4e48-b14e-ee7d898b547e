/* empty css                  *//* empty css                   */import{r as e,y as t,a,w as l,d as o,e as i,j as n,t as r,g as s,h as u,m as d,F as c,z as v,f as p,A as f,k as m,i as h,B as g,C as y,D as b,G as w,H as x,E as k,I,J as S,K as _,L as C,M as $,N as T,O as j,P as B,n as z,o as q,Q as N,R as O,S as A,T as R,U as V,V as E,b as U,u as M,W as L,X as H,Y as W,Z as D,c as J,$ as P,a0 as F,a1 as X,a2 as Z,a3 as Y,a4 as G}from"https://static.medsci.cn/ai-write/static/index-ec5131bd.js";import{g as K}from"./index-a0259697.js";/* empty css                *//* empty css                 *//* empty css                  *//* empty css                  */import{_ as Q}from"./_plugin-vue_export-helper-1b428a4d.js";import{c as ee,r as te,g as ae,s as le,i as oe,o as ie,a as ne,n as re,m as se,b as ue,u as de,d as ce,e as ve,f as pe,h as fe,j as me,k as he,w as ge,l as ye,p as be,t as we,q as xe,v as ke,x as Ie,y as Se,z as _e,A as Ce,B as $e,C as Te,D as je,E as Be,F as ze,G as qe,H as Ne,I as Oe,J as Ae,K as Re,L as Ve,M as Ee,N as Ue}from"./use-touch-e287ad24.js";import{r as Me,a as Le,f as He}from"./use-route-03bd9237.js";const We={class:"p-3 flex-1 rounded-md"},De={class:"text-[14px] font-bold mb-2 text-gray-600"},Je={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(S,{emit:_}){const C=e(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),$=a(),T=e([]),j=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),B=e(""),z=S,q=z.type,N=z.fileVerify,O=z.label,A=z.required,R=z.max_length,V=z.options,E={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},U=()=>{let e="";return N.forEach(((t,a)=>{a<N.length-1?e+=E[t].join(",")+",":e+=E[t].join(",")})),e},M=_,L=(e,t,a)=>{},H=()=>{B.value=""},W=async e=>{const{file:t,onSuccess:a,onError:l}=e,o=new FormData;o.append("file",t),o.append("appId",$.params.uuid),o.append("user",C.value.userName);try{const e=await g(o);B.value={type:j.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},a(e,t)}catch(i){l(i)}return!1};return V&&V.length>0&&(B.value=V[0]),l(B,(e=>{M("update:value",e)}),{immediate:!0}),(e,t)=>{const a=y,l=b,g=w,S=x,_=k,C=I;return o(),i("div",We,[n("div",De,r(s(O)),1),"paragraph"===s(q)||"text-input"===s(q)?(o(),u(a,{key:0,modelValue:B.value,"onUpdate:modelValue":t[0]||(t[0]=e=>B.value=e),type:"paragraph"===s(q)?"textarea":"text",rows:5,required:s(A),placeholder:`${s(O)}`,"show-word-limit":"",resize:"none",maxlength:s(R)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===s(q)?(o(),u(a,{key:1,modelValue:B.value,"onUpdate:modelValue":t[1]||(t[1]=e=>B.value=e),modelModifiers:{number:!0},type:"number",required:s(A),placeholder:`${s(O)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===s(q)?(o(),u(g,{key:2,modelValue:B.value,"onUpdate:modelValue":t[2]||(t[2]=e=>B.value=e),required:s(A),placeholder:`${s(O)}`},{default:d((()=>[(o(!0),i(c,null,v(s(V),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===s(q)?(o(),u(C,{key:3,"file-list":T.value,"onUpdate:fileList":t[3]||(t[3]=e=>T.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":H,"before-remove":e.beforeRemove,limit:1,accept:U(),"auto-upload":!0,"on-Success":L,"http-request":W,"on-exceed":e.handleExceed},{default:d((()=>[p(_,{disabled:1==T.value.length},{default:d((()=>[p(S,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(s(f))])),_:1}),m("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","accept","on-exceed"])):h("",!0)])}}},Pe=Array.isArray,Fe=e=>"string"==typeof e,Xe=e=>null!==e&&"object"==typeof e,Ze=/\B([A-Z])/g,Ye=(e=>{const t=Object.create(null);return a=>t[a]||(t[a]=e(a))})((e=>e.replace(Ze,"-$1").toLowerCase()));function Ge(e){if(Pe(e)){const t={};for(let a=0;a<e.length;a++){const l=e[a],o=Fe(l)?tt(l):Ge(l);if(o)for(const e in o)t[e]=o[e]}return t}if(Fe(e)||Xe(e))return e}const Ke=/;(?![^(]*\))/g,Qe=/:([^]+)/,et=/\/\*[^]*?\*\//g;function tt(e){const t={};return e.replace(et,"").split(Ke).forEach((e=>{if(e){const a=e.split(Qe);a.length>1&&(t[a[0].trim()]=a[1].trim())}})),t}function at(e){let t="";if(Fe(e))t=e;else if(Pe(e))for(let a=0;a<e.length;a++){const l=at(e[a]);l&&(t+=l+" ")}else if(Xe(e))for(const a in e)e[a]&&(t+=a+" ");return t.trim()}let lt=0;function ot(){const e=S(),{name:t="unknown"}=(null==e?void 0:e.type)||{};return`${t}-${++lt}`}function it(e,t){if(!oe||!window.IntersectionObserver)return;const a=new IntersectionObserver((e=>{t(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&a.unobserve(e.value)};C(l),$(l),ie((()=>{e.value&&a.observe(e.value)}))}const[nt,rt]=ne("sticky");const st=be(T({name:nt,props:{zIndex:re,position:se("top"),container:Object,offsetTop:ue(0),offsetBottom:ue(0)},emits:["scroll","change"],setup(t,{emit:a,slots:o}){const i=e(),n=de(i),r=j({fixed:!1,width:0,height:0,transform:0}),s=e(!1),u=B((()=>ce("top"===t.position?t.offsetTop:t.offsetBottom))),d=B((()=>{if(s.value)return;const{fixed:e,height:t,width:a}=r;return e?{width:`${a}px`,height:`${t}px`}:void 0})),c=B((()=>{if(!r.fixed||s.value)return;const e=ve(pe(t.zIndex),{width:`${r.width}px`,height:`${r.height}px`,[t.position]:`${u.value}px`});return r.transform&&(e.transform=`translate3d(0, ${r.transform}px, 0)`),e})),v=()=>{if(!i.value||me(i))return;const{container:e,position:l}=t,o=he(i),n=ae(window);if(r.width=o.width,r.height=o.height,"top"===l)if(e){const t=he(e),a=t.bottom-u.value-r.height;r.fixed=u.value>o.top&&t.bottom>0,r.transform=a<0?a:0}else r.fixed=u.value>o.top;else{const{clientHeight:t}=document.documentElement;if(e){const a=he(e),l=t-a.top-u.value-r.height;r.fixed=t-u.value<o.bottom&&t>a.top,r.transform=l<0?-l:0}else r.fixed=t-u.value<o.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:r.fixed})})(n)};return l((()=>r.fixed),(e=>a("change",e))),fe("scroll",v,{target:n,passive:!0}),it(i,v),l([ge,ye],(()=>{i.value&&!me(i)&&r.fixed&&(s.value=!0,z((()=>{const e=he(i);r.width=e.width,r.height=e.height,s.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:rt({fixed:r.fixed&&!s.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[ut,dt]=ne("swipe"),ct={loop:we,width:re,height:re,vertical:Boolean,autoplay:ue(0),duration:ue(500),touchable:we,lazyRender:Boolean,initialSwipe:ue(0),indicatorColor:String,showIndicators:we,stopPropagation:we},vt=Symbol(ut);const pt=be(T({name:ut,props:ct,emits:["change","dragStart","dragEnd"],setup(t,{emit:a,slots:o}){const i=e(),n=e(),r=j({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let s=!1;const u=xe(),{children:d,linkChildren:c}=ke(vt),v=B((()=>d.length)),f=B((()=>r[t.vertical?"height":"width"])),m=B((()=>t.vertical?u.deltaY.value:u.deltaX.value)),h=B((()=>{if(r.rect){return(t.vertical?r.rect.height:r.rect.width)-f.value*v.value}return 0})),g=B((()=>f.value?Math.ceil(Math.abs(h.value)/f.value):v.value)),y=B((()=>v.value*f.value)),b=B((()=>(r.active+v.value)%v.value)),w=B((()=>{const e=t.vertical?"vertical":"horizontal";return u.direction.value===e})),x=B((()=>{const e={transitionDuration:`${r.swiping?0:t.duration}ms`,transform:`translate${t.vertical?"Y":"X"}(${+r.offset.toFixed(2)}px)`};if(f.value){const a=t.vertical?"height":"width",l=t.vertical?"width":"height";e[a]=`${y.value}px`,e[l]=t[l]?`${t[l]}px`:""}return e})),k=(e,a=0)=>{let l=e*f.value;t.loop||(l=Math.min(l,-h.value));let o=a-l;return t.loop||(o=Te(o,h.value,0)),o},I=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=r,n=(e=>{const{active:a}=r;return e?t.loop?Te(a+e,-1,v.value):Te(a+e,0,g.value):a})(e),s=k(n,l);if(t.loop){if(d[0]&&s!==h.value){const e=s<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==s){const e=s>0;d[v.value-1].setOffset(e?-y.value:0)}}r.active=n,r.offset=s,o&&n!==i&&a("change",b.value)},S=()=>{r.swiping=!0,r.active<=-1?I({pace:v.value}):r.active>=v.value&&I({pace:-v.value})},_=()=>{S(),u.reset(),Ce((()=>{r.swiping=!1,I({pace:1,emitChange:!0})}))};let T;const O=()=>clearTimeout(T),A=()=>{O(),+t.autoplay>0&&v.value>1&&(T=setTimeout((()=>{_(),A()}),+t.autoplay))},R=(e=+t.initialSwipe)=>{if(!i.value)return;const a=()=>{var a,l;if(!me(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};r.rect=e,r.width=+(null!=(a=t.width)?a:e.width),r.height=+(null!=(l=t.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),r.active=e,r.swiping=!0,r.offset=k(e),d.forEach((e=>{e.setOffset(0)})),A()};me(i)?z().then(a):a()},V=()=>R(r.active);let E;const U=e=>{!t.touchable||e.touches.length>1||(u.start(e),s=!1,E=Date.now(),O(),S())},M=()=>{if(!t.touchable||!r.swiping)return;const e=Date.now()-E,l=m.value/e;if((Math.abs(l)>.25||Math.abs(m.value)>f.value/2)&&w.value){const e=t.vertical?u.offsetY.value:u.offsetX.value;let a=0;a=t.loop?e>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/f.value),I({pace:a,emitChange:!0})}else m.value&&I({pace:0});s=!1,r.swiping=!1,a("dragEnd",{index:b.value}),A()},L=(e,a)=>{const l=a===b.value,o=l?{backgroundColor:t.indicatorColor}:void 0;return p("i",{style:o,class:dt("indicator",{active:l})},null)};return Ie({prev:()=>{S(),u.reset(),Ce((()=>{r.swiping=!1,I({pace:-1,emitChange:!0})}))},next:_,state:r,resize:V,swipeTo:(e,a={})=>{S(),u.reset(),Ce((()=>{let l;l=t.loop&&e===v.value?0===r.active?0:e:e%v.value,a.immediate?Ce((()=>{r.swiping=!1})):r.swiping=!1,I({pace:l-r.active,emitChange:!0})}))}}),c({size:f,props:t,count:v,activeIndicator:b}),l((()=>t.initialSwipe),(e=>R(+e))),l(v,(()=>R(r.active))),l((()=>t.autoplay),A),l([ge,ye,()=>t.width,()=>t.height],V),l(Se(),(e=>{"visible"===e?A():O()})),q(R),N((()=>R(r.active))),_e((()=>R(r.active))),C(O),$(O),fe("touchmove",(e=>{if(t.touchable&&r.swiping&&(u.move(e),w.value)){!t.loop&&(0===r.active&&m.value>0||r.active===v.value-1&&m.value<0)||($e(e,t.stopPropagation),I({offset:m.value}),s||(a("dragStart",{index:b.value}),s=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:dt()},[p("div",{ref:n,style:x.value,class:dt("track",{vertical:t.vertical}),onTouchstartPassive:U,onTouchend:M,onTouchcancel:M},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):t.showIndicators&&v.value>1?p("div",{class:dt("indicators",{vertical:t.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ft,mt]=ne("tabs");var ht=T({name:ft,props:{count:je(Number),inited:Boolean,animated:Boolean,duration:je(re),swipeable:Boolean,lazyRender:Boolean,currentIndex:je(Number)},emits:["change"],setup(t,{emit:a,slots:o}){const i=e(),n=e=>a("change",e),r=()=>{var e;const a=null==(e=o.default)?void 0:e.call(o);return t.animated||t.swipeable?p(pt,{ref:i,loop:!1,class:mt("track"),duration:1e3*+t.duration,touchable:t.swipeable,lazyRender:t.lazyRender,showIndicators:!1,onChange:n},{default:()=>[a]}):a},s=e=>{const a=i.value;a&&a.state.active!==e&&a.swipeTo(e,{immediate:!t.inited})};return l((()=>t.currentIndex),s),q((()=>{s(t.currentIndex)})),Ie({swipeRef:i}),()=>p("div",{class:mt("content",{animated:t.animated||t.swipeable})},[r()])}});const[gt,yt]=ne("tabs"),bt={type:se("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ue(0),duration:ue(.3),animated:Boolean,ellipsis:we,swipeable:Boolean,scrollspy:Boolean,offsetTop:ue(0),background:String,lazyRender:we,showHeader:we,lineWidth:re,lineHeight:re,beforeChange:Function,swipeThreshold:ue(5),titleActiveColor:String,titleInactiveColor:String},wt=Symbol(gt);var xt=T({name:gt,props:bt,emits:["change","scroll","rendered","clickTab","update:active"],setup(t,{emit:a,slots:o}){let i,n,r,s,u;const d=e(),c=e(),v=e(),f=e(),m=ot(),h=de(d),[g,y]=function(){const t=e([]),a=[];return _((()=>{t.value=[]})),[t,e=>(a[e]||(a[e]=a=>{t.value[e]=a}),a[e])]}(),{children:b,linkChildren:w}=ke(wt),x=j({inited:!1,position:"",lineStyle:{},currentIndex:-1}),k=B((()=>b.length>+t.swipeThreshold||!t.ellipsis||t.shrink)),I=B((()=>({borderColor:t.color,background:t.background}))),S=(e,t)=>{var a;return null!=(a=e.name)?a:t},C=B((()=>{const e=b[x.currentIndex];if(e)return S(e,x.currentIndex)})),$=B((()=>ce(t.offsetTop))),T=B((()=>t.sticky?$.value+i:0)),q=e=>{const a=c.value,l=g.value;if(!(k.value&&a&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(a.offsetWidth-o.offsetWidth)/2;s&&s(),s=function(e,t,a){let l,o=0;const i=e.scrollLeft,n=0===a?1:Math.round(1e3*a/16);let r=i;return function a(){r+=(t-i)/n,e.scrollLeft=r,++o<n&&(l=te(a))}(),function(){ee(l)}}(a,i,e?0:+t.duration)},O=()=>{const e=x.inited;z((()=>{const a=g.value;if(!a||!a[x.currentIndex]||"line"!==t.type||me(d.value))return;const l=a[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=t,n=l.offsetLeft+l.offsetWidth/2,r={width:Be(o),backgroundColor:t.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(r.transitionDuration=`${t.duration}s`),ze(i)){const e=Be(i);r.height=e,r.borderRadius=e}x.lineStyle=r}))},A=(e,l)=>{const o=(e=>{const t=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=t}})(e);if(!ze(o))return;const i=b[o],n=S(i,o),s=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||q(),O()),n!==t.active&&(a("update:active",n),s&&a("change",n,i.title)),r&&!t.scrollspy&&Ne(Math.ceil(Oe(d.value)-$.value))},R=(e,t)=>{const a=b.find(((t,a)=>S(t,a)===e)),l=a?b.indexOf(a):0;A(l,t)},V=(e=!1)=>{if(t.scrollspy){const a=b[x.currentIndex].$el;if(a&&h.value){const l=Oe(a,h.value)-T.value;n=!0,u&&u(),u=function(e,t,a,l){let o,i=ae(e);const n=i<t,r=0===a?1:Math.round(1e3*a/16),s=(t-i)/r;return function a(){i+=s,(n&&i>t||!n&&i<t)&&(i=t),le(e,i),n&&i<t||!n&&i>t?o=te(a):l&&(o=te(l))}(),function(){ee(o)}}(h.value,l,e?0:+t.duration,(()=>{n=!1}))}}},E=(e,l,o)=>{const{title:i,disabled:n}=b[l],r=S(b[l],l);n||(Ae(t.beforeChange,{args:[r],done:()=>{A(l),V()}}),Me(e)),a("clickTab",{name:r,title:i,event:o,disabled:n})},U=e=>{r=e.isFixed,a("scroll",e)},M=()=>{if("line"===t.type&&b.length)return p("div",{class:yt("line"),style:x.lineStyle},null)},L=()=>{var e,a,l;const{type:i,border:n,sticky:r}=t,s=[p("div",{ref:r?void 0:v,class:[yt("wrap"),{[qe]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:yt("nav",[i,{shrink:t.shrink,complete:k.value}]),style:I.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(E))),M(),null==(a=o["nav-right"])?void 0:a.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return r?p("div",{ref:v},[s]):s},H=()=>{O(),z((()=>{var e,t;q(!0),null==(t=null==(e=f.value)?void 0:e.swipeRef.value)||t.resize()}))};l((()=>[t.color,t.duration,t.lineWidth,t.lineHeight]),O),l(ge,H),l((()=>t.active),(e=>{e!==C.value&&R(e)})),l((()=>b.length),(()=>{x.inited&&(R(t.active),O(),z((()=>{q(!0)})))}));return Ie({resize:H,scrollTo:e=>{z((()=>{R(e),V(!0)}))}}),N(O),_e(O),ie((()=>{R(t.active,!0),z((()=>{x.inited=!0,v.value&&(i=he(v.value).height),q(!0)}))})),it(d,O),fe("scroll",(()=>{if(t.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:t}=he(b[e].$el);if(t>T.value)return 0===e?0:e-1}return b.length-1})();A(e)}}),{target:h,passive:!0}),w({id:m,props:t,setLine:O,scrollable:k,onRendered:(e,t)=>a("rendered",e,t),currentName:C,setTitleRefs:y,scrollIntoView:q}),()=>p("div",{ref:d,class:yt([t.type])},[t.showHeader?t.sticky?p(st,{container:d.value,offsetTop:$.value,onScroll:U},{default:()=>[L()]}):L():null,p(ht,{ref:f,count:b.length,inited:x.inited,animated:t.animated,duration:t.duration,swipeable:t.swipeable,lazyRender:t.lazyRender,currentIndex:x.currentIndex,onChange:A},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const kt=Symbol(),[It,St]=ne("tab"),_t=T({name:It,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:re,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:we},setup(e,{slots:t}){const a=B((()=>{const t={},{type:a,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:r}=e;l&&"card"===a&&(t.borderColor=l,o||(i?t.backgroundColor=l:t.color=l));const s=i?n:r;return s&&(t.color=s),t})),l=()=>{const a=p("span",{class:St("text",{ellipsis:!e.scrollable})},[t.title?t.title():e.title]);return e.dot||ze(e.badge)&&""!==e.badge?p(Re,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[a]}):a};return()=>p("div",{id:e.id,role:"tab",class:[St([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:a.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[Ct,$t]=ne("swipe-item");const Tt=be(T({name:Ct,setup(e,{slots:t}){let a;const l=j({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Ve(vt);if(!o)return;const n=B((()=>{const e={},{vertical:t}=o.props;return o.size.value&&(e[t?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${t?"Y":"X"}(${l.offset}px)`),e})),r=B((()=>{const{loop:e,lazyRender:t}=o.props;if(!t||a)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,r=o.count.value-1,s=0===n&&e?r:n-1,u=n===r&&e?0:n+1;return a=i.value===n||i.value===s||i.value===u,a}));return q((()=>{z((()=>{l.mounted=!0}))})),Ie({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:$t(),style:n.value},[r.value?null==(e=t.default)?void 0:e.call(t):null])}}})),[jt,Bt]=ne("tab");const zt=be(T({name:jt,props:ve({},Le,{dot:Boolean,name:re,badge:re,title:String,disabled:Boolean,titleClass:Ee,titleStyle:[String,Object],showZeroBadge:we}),setup(t,{slots:a}){const o=ot(),i=e(!1),n=S(),{parent:r,index:s}=Ve(wt);if(!r)return;const u=()=>{var e;return null!=(e=t.name)?e:s.value},d=B((()=>{const e=u()===r.currentName.value;return e&&!i.value&&(i.value=!0,r.props.lazyRender&&z((()=>{r.onRendered(u(),t.title)}))),e})),c=e(""),v=e("");O((()=>{const{titleClass:e,titleStyle:a}=t;c.value=e?at(e):"",v.value=a&&"string"!=typeof a?function(e){let t="";if(!e||Fe(e))return t;for(const a in e){const l=e[a];(Fe(l)||"number"==typeof l)&&(t+=`${a.startsWith("--")?a:Ye(a)}:${l};`)}return t}(Ge(a)):a}));const f=e(!d.value);return l(d,(e=>{e?f.value=!1:Ce((()=>{f.value=!0}))})),l((()=>t.title),(()=>{r.setLine(),r.scrollIntoView()})),A(kt,d),Ie({id:o,renderTitle:e=>p(_t,E({key:o,id:`${r.id}-${s.value}`,ref:r.setTitleRefs(s.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:r.scrollable.value,activeColor:r.props.titleActiveColor,inactiveColor:r.props.titleInactiveColor,onClick:t=>e(n.proxy,s.value,t)},Ue(r.props,["type","color","shrink"]),Ue(t,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const t=`${r.id}-${s.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=r.props;if(!a.default&&!l)return;const v=u||d.value;if(l||n)return p(Tt,{id:o,role:"tabpanel",class:Bt("panel-wrapper",{inactive:f.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":t,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:Bt("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=i.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return R(p("div",{id:o,role:"tabpanel",class:Bt("panel"),tabindex:v?0:-1,"aria-labelledby":t,"data-allow-mismatch":"attribute"},[m]),[[V,v]])}}})),qt=be(xt),Nt={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ot={class:"pc_container",style:{display:"flex",height:"calc(var(--vh) * 100 - 190px)"}},At={class:"bg-[#fff]",style:{"border-radius":"10px",width:"40%",height:"calc(var(--vh) * 100 - 190px)"}},Rt={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Vt={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Et={style:{"margin-top":"15px"}},Ut=["src"],Mt=["src"],Lt={class:"mobile_container"},Ht={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Wt={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Dt=Q({__name:"index",setup(r){const f=K("loading.png"),g=K("copy.png"),y=j({}),b=a(),w={},x=e([]),I=e(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),S=e(null),{locale:_}=U(),C=M();let $=e("a"),T=e("");q((async()=>{var e,a;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=b.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(a=b.params)?void 0:a.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),_.value=L(),t.get("userInfo"))me(),te();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=H("current_location_country",1);e&&"中国"!=e?C.push("/login"):window.addLoginDom()}})),l(T,(()=>{T.value&&($.value="b")}));const z=()=>{b.params.uuid&&F({appId:b.params.uuid,user:I.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(x.value=e.user_input_form,e.user_input_form.forEach((e=>{const t=Object.keys(e)[0],a=e[t].variable;w[a]={label:e[t].label},y[a]=""})))}))},N=B((()=>!!x.value.length)),O=B((()=>x.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),A=()=>{b.params.uuid&&X({appId:b.params.uuid,user:I.value.userName}).then((e=>{S.value={...e}}))},V=e(!1),E=e(!1),Q=e(!1),ee=e(!1),te=async()=>{var e,t;let a=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=I.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:b.params.uuid,userUuid:null==(t=I.value)?void 0:t.openid}];await W.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",a)},ae=()=>{var e,t;if(0!=O.value.length||(a=y,Object.values(a).some((e=>e)))){var a;for(let e in y)if(O.value.includes(e)&&!y[e])return void Z({message:`${w[e].label}为必填项！`,type:"error"});(null==(e=S.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(t=S.value)?void 0:t.mode)?Z({type:"success",message:"计划中，敬请期待..."}):"completion"==S.value.mode?se():re())}else Z({message:"请输入您的问题。",type:"error"})},le=e([]),oe=e(!1);let ie=!1,ne=!1;const re=async()=>{T.value="",le.value=[],oe.value=!1,ie=!1;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run`;0,V.value=!0,E.value=!0,ee.value=!1,await He(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:b.params.uuid,user:I.value.userName,inputs:{...y,outputLanguage:y.outputLanguage?y.outputLanguage:"中文"==Y()?"简体中文":Y()},files:[],response_mode:"streaming",appUuid:b.params.appUuid}),onmessage(e){var t,a;if(e.data.trim())try{const l=JSON.parse(e.data);if(l.error)throw new Error(l.error);"text_chunk"===l.event&&(le.value.push(null==(t=null==l?void 0:l.data)?void 0:t.text),oe.value||ue()),"workflow_started"===l.event&&(V.value=!1,Q.value=!0),"workflow_finished"===l.event&&(0==le.value.length&&(le.value.push(null==(a=null==l?void 0:l.data)?void 0:a.outputs.text),oe.value||ue()),Q.value=!1,E.value=!1,ie=!0,$.value="b")}catch(l){ve(l)}},onerror(e){ve(e)},openWhenHidden:!0})}catch(e){ve(e)}},se=async()=>{T.value="",le.value=[],oe.value=!1,ie=!1;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages`;0,V.value=!0,E.value=!0,ee.value=!1,await He(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:b.params.uuid,user:I.value.userName,inputs:{...y,outputLanguage:Y()},files:[],response_mode:"streaming",appUuid:b.params.appUuid}),onmessage(e){if(V.value=!1,Q.value=!0,e.data.trim())try{const t=JSON.parse(e.data);if(t.error)throw new Error(t.error);"message"===t.event&&(le.value.push(null==t?void 0:t.answer),oe.value||ue()),"message_end"===t.event&&(Q.value=!1,E.value=!1,$.value="b",ie=!0)}catch(t){ve(t)}},onerror(e){ve(e)},openWhenHidden:!0})}catch(e){ve(e)}},ue=()=>{if(0===le.value.length)return oe.value=!1,ne=!0,void de();oe.value=!0;const e=le.value.shift();ce(e).then((()=>{ue()}))},de=()=>{ne&&ie&&(ee.value=!0)},ce=e=>new Promise((t=>{let a=0;const l=setInterval((()=>{if(a<e.length){T.value+=e[a++];const t=document.getElementById("typing-area");t.scrollTop=t.scrollHeight}else clearInterval(l),t()}),15)})),ve=e=>{V.value=!1,Q.value=!1,E.value=!1,oe.value=!1,Z.error(e.message),T.value=e.message},pe=async()=>{try{await navigator.clipboard.writeText(T.value),Z({type:"success",message:"复制成功"})}catch(e){Z(e)}},fe=()=>{for(let e in y)y[e]=""},me=()=>{if(localStorage.getItem("yudaoToken"))return z(),void A();const e=t.get("userInfo");if(e){const t=JSON.parse(e);try{D({userId:t.userId,userName:t.userName,realName:t.realName,avatar:t.avatar,plaintextUserId:t.plaintextUserId,mobile:t.mobile,email:t.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),z(),A())}))}catch(a){}}};return(e,t)=>{const a=k,l=J("v-md-preview"),r=G;return o(),i("div",Nt,[n("div",Ot,[s(N)?(o(),i(c,{key:0},[n("div",At,[(o(!0),i(c,null,v(s(x),((t,a)=>(o(),i("div",{class:"flex",key:a},[(o(!0),i(c,null,v(t,((t,a)=>(o(),u(Je,{key:t.variable,type:a,label:e.$t(null==t?void 0:t.label),value:s(y)[null==t?void 0:t.variable],required:null==t?void 0:t.required,placeholder:`${null==t?void 0:t.label}`,max_length:null==t?void 0:t.max_length,options:null==t?void 0:t.options,fileVerify:null==t?void 0:t.allowed_file_types,"onUpdate:value":e=>s(y)[null==t?void 0:t.variable]=e},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Rt,[p(a,{onClick:fe},{default:d((()=>[m("Clear")])),_:1}),p(a,{onClick:ae,loading:s(E),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])]),R((o(),i("div",{style:{width:"60%","margin-left":"15px",padding:"15px","border-radius":"10px"},class:P({"bg-[#fff]":s(T)})},[n("div",Vt,[s(T)?(o(),u(l,{key:0,text:s(T),id:"previewMd"},null,8,["text"])):h("",!0)]),n("div",Et,[s(Q)?(o(),i("img",{key:0,src:s(f),alt:"loading",class:"spinner"},null,8,Ut)):h("",!0),s(ee)?(o(),i("img",{key:1,onClick:pe,src:s(g),alt:"",style:{width:"20px"},class:"copy"},null,8,Mt)):h("",!0)])],2)),[[r,s(V)]])],64)):h("",!0)]),n("div",Lt,[p(s(qt),{active:s($),shrink:"","line-width":"20"},{default:d((()=>[p(s(zt),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(s(x),((t,a)=>(o(),i("div",{class:"flex",key:a},[(o(!0),i(c,null,v(t,((t,a)=>(o(),u(Je,{key:t.variable,type:a,label:e.$t(null==t?void 0:t.label),value:s(y)[null==t?void 0:t.variable],required:null==t?void 0:t.required,placeholder:`${null==t?void 0:t.label}`,max_length:null==t?void 0:t.max_length,options:null==t?void 0:t.options,fileVerify:null==t?void 0:t.allowed_file_types,"onUpdate:value":e=>s(y)[null==t?void 0:t.variable]=e},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ht,[p(a,{onClick:fe},{default:d((()=>[m("Clear")])),_:1}),p(a,{onClick:t[0]||(t[0]=e=>ae()),loading:s(E),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])])),_:1}),p(s(zt),{title:"结果",name:"b"},{default:d((()=>[n("div",Wt,[s(T)?(o(),u(l,{key:0,text:s(T),id:"previewMd"},null,8,["text"])):h("",!0)])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-db2905fd"]]);export{Dt as default};
