import{a,d as s,e as t,x as c,y as n,j as e,k as o}from"./index-51e6c9ce.js";const d={name:"AssistantComponent",data:()=>({}),methods:{}},i=a=>(c("data-v-39a9c511"),a=a(),n(),a),r={class:"assistant-container"},p=[i((()=>e("div",{class:"assistant-icon"},[e("img",{src:"/apps/static/kefu-ae1166e2.png",class:"fas fa-user-astronaut",alt:"客服"})],-1))),i((()=>e("div",{class:"qr-code"},[e("img",{src:"/apps/static/qrcode-4bdb4a15.png",alt:"QR Code"}),o(" 扫码添加小助手 ")],-1)))];const m=a(d,[["render",function(a,c,n,e,o,d){return s(),t("div",r,p)}],["__scopeId","data-v-39a9c511"]]);export{m as c};
