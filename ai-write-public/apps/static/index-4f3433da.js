/* empty css                  *//* empty css                   */import{r as e,x as a,u as t,w as l,c as o,d as i,i as n,t as r,f as s,g as u,l as d,F as c,y as v,e as p,z as m,j as f,h,A as g,B as y,C as b,D as w,G as x,E as I,H as k,I as S,J as _,K as C,L as T,M as $,N as B,O as j,n as z,o as q,P as N,Q as O,R,S as A,q as V,T as U,U as E,V as M,W as L,a as H,X as W,Y as D,Z as J,$ as P,a0 as F,b as X,a1 as Z,a2 as Y,a3 as G,a4 as K,a5 as Q}from"./index-46a7add2.js";import{g as ee}from"./index-7124623e.js";/* empty css                  *//* empty css                  *//* empty css                 */import{_ as ae}from"./_plugin-vue_export-helper-1b428a4d.js";import{c as te,r as le,g as oe,s as ie,i as ne,o as re,a as se,n as ue,m as de,b as ce,u as ve,d as pe,e as me,f as fe,h as he,j as ge,k as ye,w as be,l as we,p as xe,t as Ie,q as ke,v as Se,x as _e,y as Ce,z as Te,A as $e,B as Be,C as je,D as ze,E as qe,F as Ne,G as Oe,H as Re,I as Ae,J as Ve,K as Ue,L as Ee,M as Me,N as Le}from"./use-touch-d68d8464.js";import{r as He,a as We,f as De}from"./use-route-0020cd5a.js";const Je={class:"p-3 flex-1 rounded-md"},Pe={class:"text-[14px] font-bold mb-2 text-gray-600"},Fe={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(S,{emit:_}){const C=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),T=t(),$=e([]),B=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),j=e(""),z=S,q=z.type,N=z.fileVerify,O=z.label,R=z.required,A=z.max_length,V=z.options,U={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},E=()=>{let e="";return N.forEach(((a,t)=>{t<N.length-1?e+=U[a].join(",")+",":e+=U[a].join(",")})),e},M=_,L=(e,a,t)=>{},H=()=>{j.value=""},W=async e=>{const{file:a,onSuccess:t,onError:l}=e,o=new FormData;o.append("file",a),o.append("appId",T.params.uuid),o.append("user",C.value.userName);try{const e=await g(o);j.value={type:B.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(i){l(i)}return!1};return V&&V.length>0&&(j.value=V[0]),l(j,(e=>{M("update:value",e)}),{immediate:!0}),(e,a)=>{const t=y,l=b,g=w,S=x,_=I,C=k;return o(),i("div",Je,[n("div",Pe,r(s(O)),1),"paragraph"===s(q)||"text-input"===s(q)?(o(),u(t,{key:0,modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value=e),type:"paragraph"===s(q)?"textarea":"text",rows:5,required:s(R),placeholder:`${s(O)}`,"show-word-limit":"",resize:"none",maxlength:s(A)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===s(q)?(o(),u(t,{key:1,modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),modelModifiers:{number:!0},type:"number",required:s(R),placeholder:`${s(O)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===s(q)?(o(),u(g,{key:2,modelValue:j.value,"onUpdate:modelValue":a[2]||(a[2]=e=>j.value=e),required:s(R),placeholder:`${s(O)}`},{default:d((()=>[(o(!0),i(c,null,v(s(V),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===s(q)?(o(),u(C,{key:3,"file-list":$.value,"onUpdate:fileList":a[3]||(a[3]=e=>$.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":H,"before-remove":e.beforeRemove,limit:1,accept:E(),"auto-upload":!0,"on-Success":L,"http-request":W,"on-exceed":e.handleExceed},{default:d((()=>[p(_,{disabled:1==$.value.length},{default:d((()=>[p(S,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(s(m))])),_:1}),a[4]||(a[4]=f("从本地上传"))])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","accept","on-exceed"])):h("",!0)])}}};let Xe=0;function Ze(){const e=S(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++Xe}`}function Ye(e,a){if(!ne||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};C(l),T(l),re((()=>{e.value&&t.observe(e.value)}))}const[Ge,Ke]=se("sticky");const Qe=xe($({name:Ge,props:{zIndex:ue,position:de("top"),container:Object,offsetTop:ce(0),offsetBottom:ce(0)},emits:["scroll","change"],setup(a,{emit:t,slots:o}){const i=e(),n=ve(i),r=B({fixed:!1,width:0,height:0,transform:0}),s=e(!1),u=j((()=>pe("top"===a.position?a.offsetTop:a.offsetBottom))),d=j((()=>{if(s.value)return;const{fixed:e,height:a,width:t}=r;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=j((()=>{if(!r.fixed||s.value)return;const e=me(fe(a.zIndex),{width:`${r.width}px`,height:`${r.height}px`,[a.position]:`${u.value}px`});return r.transform&&(e.transform=`translate3d(0, ${r.transform}px, 0)`),e})),v=()=>{if(!i.value||ge(i))return;const{container:e,position:l}=a,o=ye(i),n=oe(window);if(r.width=o.width,r.height=o.height,"top"===l)if(e){const a=ye(e),t=a.bottom-u.value-r.height;r.fixed=u.value>o.top&&a.bottom>0,r.transform=t<0?t:0}else r.fixed=u.value>o.top;else{const{clientHeight:a}=document.documentElement;if(e){const t=ye(e),l=a-t.top-u.value-r.height;r.fixed=a-u.value<o.bottom&&a>t.top,r.transform=l<0?-l:0}else r.fixed=a-u.value<o.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:r.fixed})})(n)};return l((()=>r.fixed),(e=>t("change",e))),he("scroll",v,{target:n,passive:!0}),Ye(i,v),l([be,we],(()=>{i.value&&!ge(i)&&r.fixed&&(s.value=!0,z((()=>{const e=ye(i);r.width=e.width,r.height=e.height,s.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:Ke({fixed:r.fixed&&!s.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[ea,aa]=se("swipe"),ta={loop:Ie,width:ue,height:ue,vertical:Boolean,autoplay:ce(0),duration:ce(500),touchable:Ie,lazyRender:Boolean,initialSwipe:ce(0),indicatorColor:String,showIndicators:Ie,stopPropagation:Ie},la=Symbol(ea);const oa=xe($({name:ea,props:ta,emits:["change","dragStart","dragEnd"],setup(a,{emit:t,slots:o}){const i=e(),n=e(),r=B({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let s=!1;const u=ke(),{children:d,linkChildren:c}=Se(la),v=j((()=>d.length)),m=j((()=>r[a.vertical?"height":"width"])),f=j((()=>a.vertical?u.deltaY.value:u.deltaX.value)),h=j((()=>{if(r.rect){return(a.vertical?r.rect.height:r.rect.width)-m.value*v.value}return 0})),g=j((()=>m.value?Math.ceil(Math.abs(h.value)/m.value):v.value)),y=j((()=>v.value*m.value)),b=j((()=>(r.active+v.value)%v.value)),w=j((()=>{const e=a.vertical?"vertical":"horizontal";return u.direction.value===e})),x=j((()=>{const e={transitionDuration:`${r.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+r.offset.toFixed(2)}px)`};if(m.value){const t=a.vertical?"height":"width",l=a.vertical?"width":"height";e[t]=`${y.value}px`,e[l]=a[l]?`${a[l]}px`:""}return e})),I=(e,t=0)=>{let l=e*m.value;a.loop||(l=Math.min(l,-h.value));let o=t-l;return a.loop||(o=je(o,h.value,0)),o},k=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=r,n=(e=>{const{active:t}=r;return e?a.loop?je(t+e,-1,v.value):je(t+e,0,g.value):t})(e),s=I(n,l);if(a.loop){if(d[0]&&s!==h.value){const e=s<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==s){const e=s>0;d[v.value-1].setOffset(e?-y.value:0)}}r.active=n,r.offset=s,o&&n!==i&&t("change",b.value)},S=()=>{r.swiping=!0,r.active<=-1?k({pace:v.value}):r.active>=v.value&&k({pace:-v.value})},_=()=>{S(),u.reset(),$e((()=>{r.swiping=!1,k({pace:1,emitChange:!0})}))};let $;const O=()=>clearTimeout($),R=()=>{O(),+a.autoplay>0&&v.value>1&&($=setTimeout((()=>{_(),R()}),+a.autoplay))},A=(e=+a.initialSwipe)=>{if(!i.value)return;const t=()=>{var t,l;if(!ge(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};r.rect=e,r.width=+(null!=(t=a.width)?t:e.width),r.height=+(null!=(l=a.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),r.active=e,r.swiping=!0,r.offset=I(e),d.forEach((e=>{e.setOffset(0)})),R()};ge(i)?z().then(t):t()},V=()=>A(r.active);let U;const E=e=>{!a.touchable||e.touches.length>1||(u.start(e),s=!1,U=Date.now(),O(),S())},M=()=>{if(!a.touchable||!r.swiping)return;const e=Date.now()-U,l=f.value/e;if((Math.abs(l)>.25||Math.abs(f.value)>m.value/2)&&w.value){const e=a.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=a.loop?e>0?f.value>0?-1:1:0:-Math[f.value>0?"ceil":"floor"](f.value/m.value),k({pace:t,emitChange:!0})}else f.value&&k({pace:0});s=!1,r.swiping=!1,t("dragEnd",{index:b.value}),R()},L=(e,t)=>{const l=t===b.value,o=l?{backgroundColor:a.indicatorColor}:void 0;return p("i",{style:o,class:aa("indicator",{active:l})},null)};return _e({prev:()=>{S(),u.reset(),$e((()=>{r.swiping=!1,k({pace:-1,emitChange:!0})}))},next:_,state:r,resize:V,swipeTo:(e,t={})=>{S(),u.reset(),$e((()=>{let l;l=a.loop&&e===v.value?0===r.active?0:e:e%v.value,t.immediate?$e((()=>{r.swiping=!1})):r.swiping=!1,k({pace:l-r.active,emitChange:!0})}))}}),c({size:m,props:a,count:v,activeIndicator:b}),l((()=>a.initialSwipe),(e=>A(+e))),l(v,(()=>A(r.active))),l((()=>a.autoplay),R),l([be,we,()=>a.width,()=>a.height],V),l(Ce(),(e=>{"visible"===e?R():O()})),q(A),N((()=>A(r.active))),Te((()=>A(r.active))),C(O),T(O),he("touchmove",(e=>{if(a.touchable&&r.swiping&&(u.move(e),w.value)){!a.loop&&(0===r.active&&f.value>0||r.active===v.value-1&&f.value<0)||(Be(e,a.stopPropagation),k({offset:f.value}),s||(t("dragStart",{index:b.value}),s=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:aa()},[p("div",{ref:n,style:x.value,class:aa("track",{vertical:a.vertical}),onTouchstartPassive:E,onTouchend:M,onTouchcancel:M},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):a.showIndicators&&v.value>1?p("div",{class:aa("indicators",{vertical:a.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ia,na]=se("tabs");var ra=$({name:ia,props:{count:ze(Number),inited:Boolean,animated:Boolean,duration:ze(ue),swipeable:Boolean,lazyRender:Boolean,currentIndex:ze(Number)},emits:["change"],setup(a,{emit:t,slots:o}){const i=e(),n=e=>t("change",e),r=()=>{var e;const t=null==(e=o.default)?void 0:e.call(o);return a.animated||a.swipeable?p(oa,{ref:i,loop:!1,class:na("track"),duration:1e3*+a.duration,touchable:a.swipeable,lazyRender:a.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},s=e=>{const t=i.value;t&&t.state.active!==e&&t.swipeTo(e,{immediate:!a.inited})};return l((()=>a.currentIndex),s),q((()=>{s(a.currentIndex)})),_e({swipeRef:i}),()=>p("div",{class:na("content",{animated:a.animated||a.swipeable})},[r()])}});const[sa,ua]=se("tabs"),da={type:de("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ce(0),duration:ce(.3),animated:Boolean,ellipsis:Ie,swipeable:Boolean,scrollspy:Boolean,offsetTop:ce(0),background:String,lazyRender:Ie,showHeader:Ie,lineWidth:ue,lineHeight:ue,beforeChange:Function,swipeThreshold:ce(5),titleActiveColor:String,titleInactiveColor:String},ca=Symbol(sa);var va=$({name:sa,props:da,emits:["change","scroll","rendered","clickTab","update:active"],setup(a,{emit:t,slots:o}){let i,n,r,s,u;const d=e(),c=e(),v=e(),m=e(),f=Ze(),h=ve(d),[g,y]=function(){const a=e([]),t=[];return _((()=>{a.value=[]})),[a,e=>(t[e]||(t[e]=t=>{a.value[e]=t}),t[e])]}(),{children:b,linkChildren:w}=Se(ca),x=B({inited:!1,position:"",lineStyle:{},currentIndex:-1}),I=j((()=>b.length>+a.swipeThreshold||!a.ellipsis||a.shrink)),k=j((()=>({borderColor:a.color,background:a.background}))),S=(e,a)=>{var t;return null!=(t=e.name)?t:a},C=j((()=>{const e=b[x.currentIndex];if(e)return S(e,x.currentIndex)})),T=j((()=>pe(a.offsetTop))),$=j((()=>a.sticky?T.value+i:0)),q=e=>{const t=c.value,l=g.value;if(!(I.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;s&&s(),s=function(e,a,t){let l,o=0;const i=e.scrollLeft,n=0===t?1:Math.round(1e3*t/16);let r=i;return function t(){r+=(a-i)/n,e.scrollLeft=r,++o<n&&(l=le(t))}(),function(){te(l)}}(t,i,e?0:+a.duration)},O=()=>{const e=x.inited;z((()=>{const t=g.value;if(!t||!t[x.currentIndex]||"line"!==a.type||ge(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=a,n=l.offsetLeft+l.offsetWidth/2,r={width:qe(o),backgroundColor:a.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(r.transitionDuration=`${a.duration}s`),Ne(i)){const e=qe(i);r.height=e,r.borderRadius=e}x.lineStyle=r}))},R=(e,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(e);if(!Ne(o))return;const i=b[o],n=S(i,o),s=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||q(),O()),n!==a.active&&(t("update:active",n),s&&t("change",n,i.title)),r&&!a.scrollspy&&Re(Math.ceil(Ae(d.value)-T.value))},A=(e,a)=>{const t=b.find(((a,t)=>S(a,t)===e)),l=t?b.indexOf(t):0;R(l,a)},V=(e=!1)=>{if(a.scrollspy){const t=b[x.currentIndex].$el;if(t&&h.value){const l=Ae(t,h.value)-$.value;n=!0,u&&u(),u=function(e,a,t,l){let o,i=oe(e);const n=i<a,r=0===t?1:Math.round(1e3*t/16),s=(a-i)/r;return function t(){i+=s,(n&&i>a||!n&&i<a)&&(i=a),ie(e,i),n&&i<a||!n&&i>a?o=le(t):l&&(o=le(l))}(),function(){te(o)}}(h.value,l,e?0:+a.duration,(()=>{n=!1}))}}},U=(e,l,o)=>{const{title:i,disabled:n}=b[l],r=S(b[l],l);n||(Ve(a.beforeChange,{args:[r],done:()=>{R(l),V()}}),He(e)),t("clickTab",{name:r,title:i,event:o,disabled:n})},E=e=>{r=e.isFixed,t("scroll",e)},M=()=>{if("line"===a.type&&b.length)return p("div",{class:ua("line"),style:x.lineStyle},null)},L=()=>{var e,t,l;const{type:i,border:n,sticky:r}=a,s=[p("div",{ref:r?void 0:v,class:[ua("wrap"),{[Oe]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:ua("nav",[i,{shrink:a.shrink,complete:I.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(U))),M(),null==(t=o["nav-right"])?void 0:t.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return r?p("div",{ref:v},[s]):s},H=()=>{O(),z((()=>{var e,a;q(!0),null==(a=null==(e=m.value)?void 0:e.swipeRef.value)||a.resize()}))};l((()=>[a.color,a.duration,a.lineWidth,a.lineHeight]),O),l(be,H),l((()=>a.active),(e=>{e!==C.value&&A(e)})),l((()=>b.length),(()=>{x.inited&&(A(a.active),O(),z((()=>{q(!0)})))}));return _e({resize:H,scrollTo:e=>{z((()=>{A(e),V(!0)}))}}),N(O),Te(O),re((()=>{A(a.active,!0),z((()=>{x.inited=!0,v.value&&(i=ye(v.value).height),q(!0)}))})),Ye(d,O),he("scroll",(()=>{if(a.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=ye(b[e].$el);if(a>$.value)return 0===e?0:e-1}return b.length-1})();R(e)}}),{target:h,passive:!0}),w({id:f,props:a,setLine:O,scrollable:I,onRendered:(e,a)=>t("rendered",e,a),currentName:C,setTitleRefs:y,scrollIntoView:q}),()=>p("div",{ref:d,class:ua([a.type])},[a.showHeader?a.sticky?p(Qe,{container:d.value,offsetTop:T.value,onScroll:E},{default:()=>[L()]}):L():null,p(ra,{ref:m,count:b.length,inited:x.inited,animated:a.animated,duration:a.duration,swipeable:a.swipeable,lazyRender:a.lazyRender,currentIndex:x.currentIndex,onChange:R},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const pa=Symbol(),[ma,fa]=se("tab"),ha=$({name:ma,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:ue,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:Ie},setup(e,{slots:a}){const t=j((()=>{const a={},{type:t,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:r}=e;l&&"card"===t&&(a.borderColor=l,o||(i?a.backgroundColor=l:a.color=l));const s=i?n:r;return s&&(a.color=s),a})),l=()=>{const t=p("span",{class:fa("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Ne(e.badge)&&""!==e.badge?p(Ue,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>p("div",{id:e.id,role:"tab",class:[fa([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[ga,ya]=se("swipe-item");const ba=xe($({name:ga,setup(e,{slots:a}){let t;const l=B({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Ee(la);if(!o)return;const n=j((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),r=j((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,r=o.count.value-1,s=0===n&&e?r:n-1,u=n===r&&e?0:n+1;return t=i.value===n||i.value===s||i.value===u,t}));return q((()=>{z((()=>{l.mounted=!0}))})),_e({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:ya(),style:n.value},[r.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[wa,xa]=se("tab");const Ia=xe($({name:wa,props:me({},We,{dot:Boolean,name:ue,badge:ue,title:String,disabled:Boolean,titleClass:Me,titleStyle:[String,Object],showZeroBadge:Ie}),setup(a,{slots:t}){const o=Ze(),i=e(!1),n=S(),{parent:r,index:s}=Ee(ca);if(!r)return;const u=()=>{var e;return null!=(e=a.name)?e:s.value},d=j((()=>{const e=u()===r.currentName.value;return e&&!i.value&&(i.value=!0,r.props.lazyRender&&z((()=>{r.onRendered(u(),a.title)}))),e})),c=e(""),v=e("");O((()=>{const{titleClass:e,titleStyle:t}=a;c.value=e?R(e):"",v.value=t&&"string"!=typeof t?A(V(t)):t}));const m=e(!d.value);return l(d,(e=>{e?m.value=!1:$e((()=>{m.value=!0}))})),l((()=>a.title),(()=>{r.setLine(),r.scrollIntoView()})),U(pa,d),_e({id:o,renderTitle:e=>p(ha,L({key:o,id:`${r.id}-${s.value}`,ref:r.setTitleRefs(s.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:r.scrollable.value,activeColor:r.props.titleActiveColor,inactiveColor:r.props.titleInactiveColor,onClick:a=>e(n.proxy,s.value,a)},Le(r.props,["type","color","shrink"]),Le(a,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title})}),()=>{var e;const a=`${r.id}-${s.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=r.props;if(!t.default&&!l)return;const v=u||d.value;if(l||n)return p(ba,{id:o,role:"tabpanel",class:xa("panel-wrapper",{inactive:m.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":a,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:xa("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const f=i.value||u||!c?null==(e=t.default)?void 0:e.call(t):null;return E(p("div",{id:o,role:"tabpanel",class:xa("panel"),tabindex:v?0:-1,"aria-labelledby":a,"data-allow-mismatch":"attribute"},[f]),[[M,v]])}}})),ka=xe(va),Sa={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},_a={class:"pc_container",style:{display:"flex",height:"calc(var(--vh) * 100 - 190px)"}},Ca={class:"bg-[#fff]",style:{"border-radius":"10px",width:"40%",height:"calc(var(--vh) * 100 - 190px)"}},Ta={class:"p-3",style:{display:"flex","justify-content":"space-between"}},$a={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Ba={style:{"margin-top":"15px"}},ja=["src"],za=["src"],qa={class:"mobile_container"},Na={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Oa={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Ra=ae({__name:"index",setup(r){const m=ee("loading.png"),g=ee("copy.png"),y=B({}),b=t(),w={},x=e([]),k=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),S=e(null),{locale:_}=H(),C=W(),T=e(!1);let $=e("a"),z=e("");q((async()=>{var e,t;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=b.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=b.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),_.value=D(),a.get("userInfo"))he(),te();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=J("current_location_country",1);e&&"中国"!=e?C.push("/login"):window.addLoginDom()}})),l(z,(()=>{z.value&&($.value="b")}));const N=()=>{b.params.uuid&&Z({appId:b.params.uuid,user:k.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(x.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;w[t]={label:e[a].label},y[t]=""})))}))},O=j((()=>!!x.value.length)),A=j((()=>x.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),V=()=>{b.params.uuid&&Y({appId:b.params.uuid,user:k.value.userName}).then((e=>{S.value={...e}}))},U=e(!1),M=e(!1),L=e(!1),ae=e(!1),te=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=k.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:b.params.uuid,userUuid:null==(a=k.value)?void 0:a.openid}];await P.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},le=()=>{var e,a;if(0!=A.value.length||(t=y,Object.values(t).some((e=>e)))){var t;for(let e in y)if(A.value.includes(e)&&!y[e])return void G({message:`${w[e].label}为必填项！`,type:"error"});(null==(e=S.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=S.value)?void 0:a.mode)?G({type:"success",message:"计划中，敬请期待..."}):"completion"==S.value.mode?ue():se())}else G({message:"请输入您的问题。",type:"error"})},oe=e([]),ie=e(!1);let ne=!1,re=!1;const se=async()=>{z.value="",oe.value=[],ie.value=!1,ne=!1;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run`;0,U.value=!0,M.value=!0,ae.value=!1,await De(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:b.params.uuid,user:k.value.userName,inputs:{...y,outputLanguage:y.outputLanguage?y.outputLanguage:"中文"==K()?"简体中文":K()},files:[],response_mode:"streaming",appUuid:b.params.appUuid}),onmessage(e){var a,t;if(e.data.trim())try{const l=JSON.parse(e.data);if(l.error)throw new Error(l.error);"text_chunk"===l.event&&(T.value=!0,oe.value.push(null==(a=null==l?void 0:l.data)?void 0:a.text),ie.value||de()),"workflow_started"===l.event&&(U.value=!1,L.value=!0),"workflow_finished"===l.event&&(T.value||(oe.value.push(null==(t=null==l?void 0:l.data)?void 0:t.outputs.text),ie.value||de()),L.value=!1,M.value=!1,ne=!0,$.value="b")}catch(l){pe(l)}},onerror(e){pe(e)},openWhenHidden:!0})}catch(e){pe(e)}},ue=async()=>{z.value="",oe.value=[],ie.value=!1,ne=!1;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages`;0,U.value=!0,M.value=!0,ae.value=!1,await De(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:b.params.uuid,user:k.value.userName,inputs:{...y,outputLanguage:K()},files:[],response_mode:"streaming",appUuid:b.params.appUuid}),onmessage(e){if(U.value=!1,L.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(a.error)throw new Error(a.error);"message"===a.event&&(oe.value.push(null==a?void 0:a.answer),ie.value||de()),"message_end"===a.event&&(L.value=!1,M.value=!1,$.value="b",ne=!0)}catch(a){pe(a)}},onerror(e){pe(e)},openWhenHidden:!0})}catch(e){pe(e)}},de=()=>{if(0===oe.value.length)return ie.value=!1,re=!0,void ce();ie.value=!0;const e=oe.value.shift();ve(e).then((()=>{de()}))},ce=()=>{re&&ne&&(ae.value=!0)},ve=e=>new Promise((a=>{let t=0;const l=setInterval((()=>{if(t<e.length){z.value+=e[t++];const a=document.getElementById("typing-area");a.scrollTop=a.scrollHeight}else clearInterval(l),a()}),15)})),pe=e=>{U.value=!1,L.value=!1,M.value=!1,ie.value=!1,G.error(e.message),z.value=e.message},me=async()=>{try{await navigator.clipboard.writeText(z.value),G({type:"success",message:"复制成功"})}catch(e){G(e)}},fe=()=>{for(let e in y)y[e]=""},he=()=>{if(localStorage.getItem("yudaoToken"))return N(),void V();const e=a.get("userInfo");if(e){const a=JSON.parse(e);try{F({userId:a.userId,userName:a.userName,realName:a.realName,avatar:a.avatar,plaintextUserId:a.plaintextUserId,mobile:a.mobile,email:a.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),N(),V())}))}catch(t){}}};return(e,a)=>{const t=I,l=X("v-md-preview"),r=Q;return o(),i("div",Sa,[n("div",_a,[s(O)?(o(),i(c,{key:0},[n("div",Ca,[(o(!0),i(c,null,v(s(x),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Fe,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:s(y)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>s(y)[null==a?void 0:a.variable]=e},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ta,[p(t,{onClick:fe},{default:d((()=>a[1]||(a[1]=[f("Clear")]))),_:1}),p(t,{onClick:le,loading:s(M),type:"primary"},{default:d((()=>a[2]||(a[2]=[f("Execute")]))),_:1},8,["loading"])])]),E((o(),i("div",{style:{width:"60%","margin-left":"15px",padding:"15px","border-radius":"10px"},class:R({"bg-[#fff]":s(z)})},[n("div",$a,[s(z)?(o(),u(l,{key:0,text:s(z),id:"previewMd"},null,8,["text"])):h("",!0)]),n("div",Ba,[s(L)?(o(),i("img",{key:0,src:s(m),alt:"loading",class:"spinner"},null,8,ja)):h("",!0),s(ae)?(o(),i("img",{key:1,onClick:me,src:s(g),alt:"",style:{width:"20px"},class:"copy"},null,8,za)):h("",!0)])],2)),[[r,s(U)]])],64)):h("",!0)]),n("div",qa,[p(s(ka),{active:s($),shrink:"","line-width":"20"},{default:d((()=>[p(s(Ia),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(s(x),((a,t)=>(o(),i("div",{class:"flex",key:t},[(o(!0),i(c,null,v(a,((a,t)=>(o(),u(Fe,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:s(y)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>s(y)[null==a?void 0:a.variable]=e},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Na,[p(t,{onClick:fe},{default:d((()=>a[3]||(a[3]=[f("Clear")]))),_:1}),p(t,{onClick:a[0]||(a[0]=e=>le()),loading:s(M),type:"primary"},{default:d((()=>a[4]||(a[4]=[f("Execute")]))),_:1},8,["loading"])])])),_:1}),p(s(Ia),{title:"结果",name:"b"},{default:d((()=>[n("div",Oa,[s(z)?(o(),u(l,{key:0,text:s(z),id:"previewMd"},null,8,["text"])):h("",!0)])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-547d2414"]]);export{Ra as default};
