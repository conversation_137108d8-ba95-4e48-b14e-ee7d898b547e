/* empty css                  */import{g as e}from"./index-d0be2954.js";import{_ as a}from"./index-e3384c82.js";import{_ as t,a as i,u as n,r as l,b as s,w as o,o as p,c,d,e as r,f as m,g as u,h as _,i as v,j as g,t as f,k as h,l as b,m as x,n as I,p as y,q as w,s as A,E,v as j}from"./index-f7b98bee.js";import{c as T}from"./index-f704927e.js";const S=[{name:"writingAssistant",path:"/writingAssistant",type:"医学写作",meta:{title:"论文",dify_app_uuid:"10c03431-1933-4f59-8848-e2e3ccb2c557",desc:"医学论文是医学领域的研究者撰写的，旨在探讨疾病病因、诊断、治疗、预防或医学理论、技术进展的学术性文章。它基于科学实验或临床观察，通过严谨的数据分析，提出新发现、新观点或验证已有理论，为医学实践提供科学依据和指导。",icon:e("AI写作助手.svg"),bg:e("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-0bee426e.js")),["static/index-0bee426e.js","static/Editor-40a5b3f6.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/Editor-c3f71038.css","static/el-button-2d277b89.css","static/el-select-8809a015.css","static/el-popper-fa5f0b6f.css","static/el-input-1dbd76e7.css","static/el-tooltip-4ed993c7.js","static/index-8138ba24.js","static/index-d0be2954.js","static/index-f8194c50.css","static/index-536c05cc.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-message-35183971.css"])},{name:"intelligentPolishing",path:"/intelligentPolishing",type:"医学写作",meta:{title:"综述",dify_app_uuid:"4955c5cc-a366-45d8-8046-aa3044ff77bc",desc:"医学综述是对某一医学领域或专题在一定时期内研究成果的汇总和分析，旨在系统回顾研究进展，评价研究质量，探讨存在问题，预测未来方向，为研究者提供全面、深入的信息参考。",icon:e("AI智能润色.svg"),bg:e("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-483e2ed9.js")),["static/index-483e2ed9.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/index-8138ba24.js","static/index-84dd1b27.css","static/el-button-2d277b89.css","static/el-divider-07810808.css","static/el-input-1dbd76e7.css","static/el-message-35183971.css"])},{name:"reviewer",path:"/reviewer",type:"医学写作",meta:{title:"研究方案",dify_app_uuid:"d7df5b59-280e-4dda-b0f2-700a12b0b1f9",desc:"医学研究方案是科研人员为探索医学问题而设计的详细计划，包括研究目的、假设、方法、样本选择、数据收集与分析步骤、预期结果及伦理考量等，确保研究过程科学、严谨、可重复。",icon:e("审稿人回复信.svg"),bg:e("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-c5296262.js")),["static/index-c5296262.js","static/Editor-40a5b3f6.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/Editor-c3f71038.css","static/el-button-2d277b89.css","static/el-select-8809a015.css","static/el-popper-fa5f0b6f.css","static/el-input-1dbd76e7.css","static/index-8138ba24.js","static/index-d0be2954.js","static/index-f8194c50.css","static/index-cc9df6ae.css","static/el-switch-3d8c06e2.css","static/el-message-35183971.css"])},{name:"rewrite",path:"/rewrite",type:"医学写作",meta:{title:"软文",dify_app_uuid:"d1fc1b66-47ce-45cd-9ff9-ee49a2e44881",desc:"医学软文是以医疗、健康领域为背景，通过撰写具有吸引力和可读性的文章，间接推广医疗产品、服务或健康理念的一种营销手段。",icon:e("AI降重改写.svg"),bg:e("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-dd1838aa.js")),["static/index-dd1838aa.js","static/index-8138ba24.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/index-d0be2954.js","static/index-f8194c50.css","static/el-button-2d277b89.css","static/el-input-1dbd76e7.css","static/el-message-35183971.css"])},{name:"topTitle",path:"/topTitle",type:"医学写作",meta:{title:"指南共识",dify_app_uuid:"7252420d-c356-4bed-9e06-ce6c3bc01270",desc:"指南共识是指在医学领域中，基于系统评价的证据和专家经验，针对特定临床问题制定的指导性建议或推荐意见。",icon:e("高分title生成器.svg"),bg:e("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-f3786348.js")),["static/index-f3786348.js","static/el-tooltip-4ed993c7.js","static/index-8138ba24.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/index-d0be2954.js","static/index-f8194c50.css","static/index-d634582f.css","static/el-button-2d277b89.css","static/el-popper-fa5f0b6f.css","static/el-divider-07810808.css","static/el-input-1dbd76e7.css","static/el-message-35183971.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"病例报告",dify_app_uuid:"05544f18-9efb-42e4-ad5d-8ca78feaf56c",desc:"病例报告是详细记录并分析个别患者疾病发生、诊断、治疗及转归过程的医学文献，用于探讨罕见病、特殊临床表现或治疗经验。",icon:e("综述Review.svg"),bg:e("题材和论文章节的写作语料.png")},component:()=>t((()=>import("./index-396cb2e6.js")),["static/index-396cb2e6.js","static/Editor-40a5b3f6.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/Editor-c3f71038.css","static/el-button-2d277b89.css","static/el-select-8809a015.css","static/el-popper-fa5f0b6f.css","static/el-input-1dbd76e7.css","static/index-8138ba24.js","static/index-d0be2954.js","static/index-f8194c50.css","static/index-224cdab4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-message-35183971.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"国自然基金写作",dify_app_uuid:"cc0c4388-8c02-4f1c-8a32-e0f3e84146c7",desc:"“国自然基金写作”是撰写国家自然科学基金申请书的过程，该过程需遵循科学基金的撰写要求和规范，涵盖项目名称、关键词、摘要、立项依据、研究目标、内容、方案及创新性等多个方面的撰写，旨在清晰、准确、有逻辑地展示研究项目的科学性、创新性和可行性，以获得基金资助。",icon:e("综述Review.svg"),bg:e("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-396cb2e6.js")),["static/index-396cb2e6.js","static/Editor-40a5b3f6.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/Editor-c3f71038.css","static/el-button-2d277b89.css","static/el-select-8809a015.css","static/el-popper-fa5f0b6f.css","static/el-input-1dbd76e7.css","static/index-8138ba24.js","static/index-d0be2954.js","static/index-f8194c50.css","static/index-224cdab4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-message-35183971.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"学术写作问答",dify_app_uuid:"c81251e7-9269-413b-95d3-0a4b88ce84b9",desc:"",icon:e("场景写作.svg"),bg:e("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-396cb2e6.js")),["static/index-396cb2e6.js","static/Editor-40a5b3f6.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/Editor-c3f71038.css","static/el-button-2d277b89.css","static/el-select-8809a015.css","static/el-popper-fa5f0b6f.css","static/el-input-1dbd76e7.css","static/index-8138ba24.js","static/index-d0be2954.js","static/index-f8194c50.css","static/index-224cdab4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-message-35183971.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"AI产品发布会",dify_app_uuid:"5ec1c9f5-a9a3-4697-bb66-71793e8375a6",desc:"",icon:e("场景写作.svg"),bg:e("基于AI的写作文本加工.png")},component:()=>t((()=>import("./index-396cb2e6.js")),["static/index-396cb2e6.js","static/Editor-40a5b3f6.js","static/index-f7b98bee.js","static/index-e7ed429a.css","static/Editor-c3f71038.css","static/el-button-2d277b89.css","static/el-select-8809a015.css","static/el-popper-fa5f0b6f.css","static/el-input-1dbd76e7.css","static/index-8138ba24.js","static/index-d0be2954.js","static/index-f8194c50.css","static/index-224cdab4.css","static/el-checkbox-b9562d8d.css","static/el-switch-3d8c06e2.css","static/el-message-35183971.css"])}],V={class:"flex-1 flex flex-col overflow-hidden"},L={class:"text-gray-500 mb-2"},O=["href"],P={class:"flex items-center my-2"},R=["src"],D={class:"text-xl font-bold text-dark-200 mr-4"},k={class:"flex-1 overflow-hidden"},C={class:"flex items-center my-2"},N=["src"],$={class:"text-xl font-bold text-dark-200"},U={class:"text-dark-500 mb-6"},z={class:"flex justify-center"},F=i({__name:"Layout",setup(e){var t;const i=n(),F=l([]),H=l(null),J=l(null),q=l(!1),K=l(0),Z=l(!1),{locale:B}=s(),G=navigator.browserLanguage||navigator.language,M=(null==(t=i.params)?void 0:t.lang)||G,Q=l();B.value=M;const W=()=>{q.value=!0},X=e=>{Z.value=e};o((()=>i),(()=>{J.value=JSON.parse(sessionStorage.getItem("nodeInfo")),H.value.setCurrentKey(node.name)}),{deep:!0});const Y=l({appUuid:i.params.appUuid});return p((async()=>{Q.value="https://ai.medon.com.cn",await void A(Y.value).then((e=>{J.value=e[0]})).catch((e=>{})),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(Z.value=!0),S.map((e=>(e.label=e.meta.title,{label:e.type,children:S.filter((a=>a.type==e.type))}))).forEach((e=>{F.value.find((a=>a.type===e.label))||F.value.push(e)})),sessionStorage.getItem("nodeInfo")?J.value=JSON.parse(sessionStorage.getItem("nodeInfo")):J.value=F.value[0].children[0],K.value=document.body.clientHeight-56})),(e,t)=>{var i,n,l,s,o,p;const A=a,S=E,F=c("router-view"),H=j;return d(),r("div",null,[m(A,{onIsZHChange:X}),u(Z)?(d(),_(T,{key:0})):v("",!0),g("div",{class:"h-full flex p-6",style:w({height:`${u(K)}px`})},[g("main",V,[g("div",null,[g("div",L,[g("a",{class:"cursor-pointer hover:text-[#5298FF]",href:u(Q)},f(e.$t("tool.home")),9,O),t[2]||(t[2]=g("span",{class:"line"},"/",-1)),h(f((null==(i=u(J))?void 0:i.appType)?e.$t(`${u(b)[null==(n=u(J))?void 0:n.appType]}`):""),1),t[3]||(t[3]=g("span",{class:"line"},"/",-1)),h(f(null==(l=u(J))?void 0:l.appName),1)]),g("div",P,[g("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(s=u(J))?void 0:s.appIcon)?null==(o=u(J))?void 0:o.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,R),g("div",D,f(null==(p=u(J))?void 0:p.appName),1),m(S,{plain:"",size:"small",onClick:W},{default:x((()=>[h(f(e.$t("tool.intro")),1)])),_:1})])]),g("div",k,[m(F,null,{default:x((({Component:e})=>[(d(),_(I(e),{currentItem:u(J)},null,8,["currentItem"]))])),_:1})])]),m(H,{modelValue:u(q),"onUpdate:modelValue":t[1]||(t[1]=e=>y(q)?q.value=e:null),"show-close":!1,width:"500"},{header:x((()=>{var e,a,t;return[g("div",C,[g("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(e=u(J))?void 0:e.appIcon)?null==(a=u(J))?void 0:a.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,N),g("div",$,f(null==(t=u(J))?void 0:t.appName),1)])]})),default:x((()=>{var e;return[g("div",U,f(null==(e=u(J))?void 0:e.appDescription),1),g("div",z,[m(S,{onClick:t[0]||(t[0]=e=>q.value=!1)},{default:x((()=>t[4]||(t[4]=[h("我知道了")]))),_:1})])]})),_:1},8,["modelValue"])],4)])}}},[["__scopeId","data-v-73ee51da"]]);export{F as default};
