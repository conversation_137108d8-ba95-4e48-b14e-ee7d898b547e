/* empty css                  *//* empty css                     *//* empty css                 */import{a as e,b as l,u as o,W as a,r as s,O as t,y as i,o as n,aE as c,d as r,e as u,j as d,t as m,f as p,m as g,k as v,aF as _,a5 as f,aK as b,aL as I,C as k,aH as h,E as y,aI as B,aJ as w,a9 as x,aa as T}from"./index-bcd56cd2.js";const $=e=>(x("data-v-49de275f"),e=e(),T(),e),S={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},C={class:"cl-rootBox cl-signUp-root justify-center"},U={class:"cl-cardBox cl-signUp-start"},j={class:"cl-card cl-signUp-start"},V={class:"cl-header"},N={class:"cl-headerTitle"},q={class:"cl-headerSubtitle"},A={class:"cl-main"},z={class:"cl-socialButtonsRoot"},L={class:"cl-socialButtons"},O={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},R={class:"cl-socialButtonsBlockButton-d"},J=$((()=>d("span",null,[d("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),P={class:"cl-socialButtons"},W={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},E={class:"cl-socialButtonsBlockButton-d"},F=$((()=>d("span",null,[d("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1))),G=$((()=>d("div",{class:"cl-dividerRow"},[d("div",{class:"cl-dividerLine"}),d("p",{class:"cl-dividerText"},"or"),d("div",{class:"cl-dividerLine"})],-1))),H={class:"cl-socialButtonsRoot"},Z={class:"cl-internal-1pnppin"},M=$((()=>d("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1))),K={class:"cl-internal-742eeh"},Q={class:"cl-internal-2iusy0"},D=$((()=>d("svg",{class:"cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf"},[d("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))),X={class:"cl-footer 🔒️ cl-internal-4x6jej"},Y={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},ee={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},le=e({__name:"index",setup(e){var x;const{t:T}=l(),$=o(),le=a(),oe=$.params.socialType,ae=$.query.authCode,se=$.query.authState,te=s(),ie=s({email:"",password:"",emailCode:"",userName:""}),ne=s((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn"))),ce=t({userName:[{required:!0,message:T("tool.username_cannot_be_empty"),trigger:"blur"}],password:[{required:!0,message:T("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:T("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:T("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],emailCode:[{required:!0,message:T("tool.verification_code_cannot_be_empty"),trigger:"blur"},{min:6,max:6,message:T("tool.verification_code_must_be_6_digits"),trigger:"blur"}],email:[{required:!0,message:T("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:T("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),re=s(!1),ue=s(60),de=s(T("tool.send_verification_code")),me=i.get("userInfo")?null==(x=JSON.parse(i.get("userInfo")))?void 0:x.userId:"",pe=e=>{_(e).then((e=>{window.location.href=e}))},ge=()=>{if(!ie.value.email)return void f.error(T("tool.email_does_not_exist"));let e={email:ie.value.email,type:"RegisterCode"};b(e).then((e=>{e&&(()=>{re.value=!0,de.value=`${ue.value}${T("tool.retry_after_seconds")}`;let e=setInterval((()=>{ue.value-=1,de.value=`${ue.value}${T("tool.retry_after_seconds")}`,ue.value<=0&&(clearInterval(e),ue.value=60,de.value=T("tool.send_verification_code"),re.value=!1)}),1e3)})()}))};return n((()=>{me?le.push("/"):oe&&ae&&se&&c(oe,ae,se).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?i.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?i.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):i.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),le.push("/"))}))})),(e,l)=>{const o=k,a=h,s=y,t=B,i=w;return r(),u("div",S,[d("div",C,[d("div",U,[d("div",j,[d("div",V,[d("div",null,[d("h1",N,m(e.$t("tool.create_account")),1),d("p",q,m(e.$t("tool.registration_greeting")),1)])]),d("div",A,[d("div",z,[d("div",L,[d("button",O,[d("span",R,[J,d("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[0]||(l[0]=e=>pe(35))},m(e.$t("tool.continue_with_google")),1)])])]),d("div",P,[d("button",W,[d("span",E,[F,d("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[1]||(l[1]=e=>pe(36))},m(e.$t("tool.continue_with_facebook")),1)])])])]),G,d("div",H,[p(t,{ref_key:"ruleFormRef",ref:te,style:{"max-width":"600px"},model:ie.value,rules:ce,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:g((()=>[p(a,{label:e.$t("tool.username"),prop:"userName"},{default:g((()=>[p(o,{modelValue:ie.value.userName,"onUpdate:modelValue":l[2]||(l[2]=e=>ie.value.userName=e)},null,8,["modelValue"])])),_:1},8,["label"]),p(a,{label:e.$t("tool.email"),prop:"email"},{default:g((()=>[p(o,{modelValue:ie.value.email,"onUpdate:modelValue":l[3]||(l[3]=e=>ie.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),p(a,{label:e.$t("tool.verification_code"),prop:"emailCode"},{default:g((()=>[p(o,{modelValue:ie.value.emailCode,"onUpdate:modelValue":l[4]||(l[4]=e=>ie.value.emailCode=e)},{append:g((()=>[p(s,{onClick:ge,disabled:re.value,type:"primary"},{default:g((()=>[v(m(de.value),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])),_:1},8,["label"]),p(a,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:g((()=>[p(o,{modelValue:ie.value.password,"onUpdate:modelValue":l[5]||(l[5]=e=>ie.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),p(a,null,{default:g((()=>[d("div",Z,[M,d("div",K,[p(s,{class:"cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",onClick:l[6]||(l[6]=e=>(async e=>{e&&await e.validate(((e,l)=>{e&&I(ie.value).then((e=>{e&&(f({type:"success",message:T("tool.registersuccess")}),setTimeout((()=>{location.href=ne.value?"/apps/login":"login"}),1e3))}))}))})(te.value))},{default:g((()=>[d("span",Q,[v(m(e.$t("tool.continue")),1),D])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),d("div",X,[d("div",Y,[d("span",ee,m(e.$t("tool.alreadyhaveanaccount")),1),p(i,{href:ne.value?"/apps/login":"/login",class:"cl-footerActionLink"},{default:g((()=>[v(m(e.$t("tool.signIn")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-49de275f"]]);export{le as default};
