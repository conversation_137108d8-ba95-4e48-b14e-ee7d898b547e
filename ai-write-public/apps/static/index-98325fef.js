import{N as a,u as e,o as s,a4 as n,ao as o,d as i,e as t}from"./index-2502211c.js";const r=a({__name:"index",setup(a){const r=e(),c=JSON.parse(decodeURIComponent(r.params.payInfo));return s((async()=>{const a=navigator.userAgent;if(null!=a)if(a.includes("MicroMessenger"))n.warning("请打开支付宝扫码");else if(a.includes("AlipayClient")){const a=await o(c);location.href=a}})),(a,e)=>(i(),t("div"))}});export{r as default};
