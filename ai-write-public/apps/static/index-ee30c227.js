/* empty css                  *//* empty css                   */import{r as e,x as t,u as a,w as l,c as o,d as i,i as n,t as r,f as s,g as u,l as d,F as c,y as v,e as p,z as f,j as m,h,A as g,B as y,C as b,D as w,G as x,E as k,H as I,I as _,J as S,K as C,L as $,M as T,N as j,O as B,n as z,o as q,P as N,Q as A,R as O,S as R,T as E,U as M,a as V,V as U,W as L,X as H,Y as W,Z as D,b as J,$ as P,a0 as F,a1 as X,a2 as Z,a3 as Y,a4 as G,a5 as K}from"./index-8da3799a.js";import{g as Q}from"./index-fbc28608.js";/* empty css                *//* empty css                 *//* empty css                  *//* empty css                  */import{_ as ee}from"./_plugin-vue_export-helper-1b428a4d.js";import{c as te,r as ae,g as le,s as oe,i as ie,o as ne,a as re,n as se,m as ue,b as de,u as ce,d as ve,e as pe,f as fe,h as me,j as he,k as ge,w as ye,l as be,p as we,t as xe,q as ke,v as Ie,x as _e,y as Se,z as Ce,A as $e,B as Te,C as je,D as Be,E as ze,F as qe,G as Ne,H as Ae,I as Oe,J as Re,K as Ee,L as Me,M as Ve,N as Ue}from"./use-touch-63731e50.js";import{r as Le,a as He,f as We}from"./use-route-c82537f4.js";const De={class:"p-3 flex-1 rounded-md"},Je={class:"text-[14px] font-bold mb-2 text-gray-600"},Pe={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(_,{expose:S,emit:C}){const $=e(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),T=a(),j=e([]),B=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),z=e(""),q=_,N=q.type,A=q.fileVerify,O=q.label,R=q.required,E=q.max_length,M=q.options;"file"==N&&(z.value=null),"file-list"==N&&(z.value=[]);const V={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},U=()=>{let e="";return A.forEach(((t,a)=>{a<A.length-1?e+=V[t].join(",")+",":e+=V[t].join(",")})),e},L=C,H=(e,t,a)=>{},W=()=>{z.value=""},D=async e=>{const{file:t,onSuccess:a,onError:l}=e,o=new FormData;o.append("file",t),o.append("appId",T.params.uuid),o.append("user",$.value.userName);try{const e=await g(o);"file-list"==N?z.value.push({type:B.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):z.value={type:B.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},a(e,t)}catch(i){l(i)}return!1};M&&M.length>0&&(z.value=M[0]);return S({updateMessage:()=>{M&&M.length>0?z.value=M[0]:"file"==N?(z.value=null,j.value=[]):"file-list"==N?(z.value=[],j.value=[]):z.value=""}}),l(z,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,t)=>{const a=y,l=b,g=w,_=x,S=k,C=I;return o(),i("div",De,[n("div",Je,r(s(O)),1),"paragraph"===s(N)||"text-input"===s(N)?(o(),u(a,{key:0,modelValue:z.value,"onUpdate:modelValue":t[0]||(t[0]=e=>z.value=e),type:"paragraph"===s(N)?"textarea":"text",rows:5,required:s(R),placeholder:`${s(O)}`,"show-word-limit":"",resize:"none",maxlength:s(E)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===s(N)?(o(),u(a,{key:1,modelValue:z.value,"onUpdate:modelValue":t[1]||(t[1]=e=>z.value=e),modelModifiers:{number:!0},type:"number",required:s(R),placeholder:`${s(O)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===s(N)?(o(),u(g,{key:2,modelValue:z.value,"onUpdate:modelValue":t[2]||(t[2]=e=>z.value=e),required:s(R),placeholder:`${s(O)}`},{default:d((()=>[(o(!0),i(c,null,v(s(M),(e=>(o(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===s(N)||"file-list"===s(N)?(o(),u(C,{key:3,"file-list":j.value,"onUpdate:fileList":t[3]||(t[3]=e=>j.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":W,"before-remove":e.beforeRemove,limit:s(E),accept:U(),"auto-upload":!0,"on-Success":H,"http-request":D,"on-exceed":e.handleExceed},{default:d((()=>[p(S,{disabled:j.value.length==s(E)},{default:d((()=>[p(_,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(s(f))])),_:1}),m("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):h("",!0)])}}},Fe=Array.isArray,Xe=e=>"string"==typeof e,Ze=e=>null!==e&&"object"==typeof e,Ye=/\B([A-Z])/g,Ge=(e=>{const t=Object.create(null);return a=>t[a]||(t[a]=e(a))})((e=>e.replace(Ye,"-$1").toLowerCase()));function Ke(e){if(Fe(e)){const t={};for(let a=0;a<e.length;a++){const l=e[a],o=Xe(l)?at(l):Ke(l);if(o)for(const e in o)t[e]=o[e]}return t}if(Xe(e)||Ze(e))return e}const Qe=/;(?![^(]*\))/g,et=/:([^]+)/,tt=/\/\*[^]*?\*\//g;function at(e){const t={};return e.replace(tt,"").split(Qe).forEach((e=>{if(e){const a=e.split(et);a.length>1&&(t[a[0].trim()]=a[1].trim())}})),t}function lt(e){let t="";if(Xe(e))t=e;else if(Fe(e))for(let a=0;a<e.length;a++){const l=lt(e[a]);l&&(t+=l+" ")}else if(Ze(e))for(const a in e)e[a]&&(t+=a+" ");return t.trim()}let ot=0;function it(){const e=_(),{name:t="unknown"}=(null==e?void 0:e.type)||{};return`${t}-${++ot}`}function nt(e,t){if(!ie||!window.IntersectionObserver)return;const a=new IntersectionObserver((e=>{t(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&a.unobserve(e.value)};C(l),$(l),ne((()=>{e.value&&a.observe(e.value)}))}const[rt,st]=re("sticky");const ut=we(T({name:rt,props:{zIndex:se,position:ue("top"),container:Object,offsetTop:de(0),offsetBottom:de(0)},emits:["scroll","change"],setup(t,{emit:a,slots:o}){const i=e(),n=ce(i),r=j({fixed:!1,width:0,height:0,transform:0}),s=e(!1),u=B((()=>ve("top"===t.position?t.offsetTop:t.offsetBottom))),d=B((()=>{if(s.value)return;const{fixed:e,height:t,width:a}=r;return e?{width:`${a}px`,height:`${t}px`}:void 0})),c=B((()=>{if(!r.fixed||s.value)return;const e=pe(fe(t.zIndex),{width:`${r.width}px`,height:`${r.height}px`,[t.position]:`${u.value}px`});return r.transform&&(e.transform=`translate3d(0, ${r.transform}px, 0)`),e})),v=()=>{if(!i.value||he(i))return;const{container:e,position:l}=t,o=ge(i),n=le(window);if(r.width=o.width,r.height=o.height,"top"===l)if(e){const t=ge(e),a=t.bottom-u.value-r.height;r.fixed=u.value>o.top&&t.bottom>0,r.transform=a<0?a:0}else r.fixed=u.value>o.top;else{const{clientHeight:t}=document.documentElement;if(e){const a=ge(e),l=t-a.top-u.value-r.height;r.fixed=t-u.value<o.bottom&&t>a.top,r.transform=l<0?-l:0}else r.fixed=t-u.value<o.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:r.fixed})})(n)};return l((()=>r.fixed),(e=>a("change",e))),me("scroll",v,{target:n,passive:!0}),nt(i,v),l([ye,be],(()=>{i.value&&!he(i)&&r.fixed&&(s.value=!0,z((()=>{const e=ge(i);r.width=e.width,r.height=e.height,s.value=!1})))})),()=>{var e;return p("div",{ref:i,style:d.value},[p("div",{class:st({fixed:r.fixed&&!s.value}),style:c.value},[null==(e=o.default)?void 0:e.call(o)])])}}})),[dt,ct]=re("swipe"),vt={loop:xe,width:se,height:se,vertical:Boolean,autoplay:de(0),duration:de(500),touchable:xe,lazyRender:Boolean,initialSwipe:de(0),indicatorColor:String,showIndicators:xe,stopPropagation:xe},pt=Symbol(dt);const ft=we(T({name:dt,props:vt,emits:["change","dragStart","dragEnd"],setup(t,{emit:a,slots:o}){const i=e(),n=e(),r=j({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let s=!1;const u=ke(),{children:d,linkChildren:c}=Ie(pt),v=B((()=>d.length)),f=B((()=>r[t.vertical?"height":"width"])),m=B((()=>t.vertical?u.deltaY.value:u.deltaX.value)),h=B((()=>{if(r.rect){return(t.vertical?r.rect.height:r.rect.width)-f.value*v.value}return 0})),g=B((()=>f.value?Math.ceil(Math.abs(h.value)/f.value):v.value)),y=B((()=>v.value*f.value)),b=B((()=>(r.active+v.value)%v.value)),w=B((()=>{const e=t.vertical?"vertical":"horizontal";return u.direction.value===e})),x=B((()=>{const e={transitionDuration:`${r.swiping?0:t.duration}ms`,transform:`translate${t.vertical?"Y":"X"}(${+r.offset.toFixed(2)}px)`};if(f.value){const a=t.vertical?"height":"width",l=t.vertical?"width":"height";e[a]=`${y.value}px`,e[l]=t[l]?`${t[l]}px`:""}return e})),k=(e,a=0)=>{let l=e*f.value;t.loop||(l=Math.min(l,-h.value));let o=a-l;return t.loop||(o=je(o,h.value,0)),o},I=({pace:e=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=r,n=(e=>{const{active:a}=r;return e?t.loop?je(a+e,-1,v.value):je(a+e,0,g.value):a})(e),s=k(n,l);if(t.loop){if(d[0]&&s!==h.value){const e=s<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==s){const e=s>0;d[v.value-1].setOffset(e?-y.value:0)}}r.active=n,r.offset=s,o&&n!==i&&a("change",b.value)},_=()=>{r.swiping=!0,r.active<=-1?I({pace:v.value}):r.active>=v.value&&I({pace:-v.value})},S=()=>{_(),u.reset(),$e((()=>{r.swiping=!1,I({pace:1,emitChange:!0})}))};let T;const A=()=>clearTimeout(T),O=()=>{A(),+t.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),O()}),+t.autoplay))},R=(e=+t.initialSwipe)=>{if(!i.value)return;const a=()=>{var a,l;if(!he(i)){const e={width:i.value.offsetWidth,height:i.value.offsetHeight};r.rect=e,r.width=+(null!=(a=t.width)?a:e.width),r.height=+(null!=(l=t.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),r.active=e,r.swiping=!0,r.offset=k(e),d.forEach((e=>{e.setOffset(0)})),O()};he(i)?z().then(a):a()},E=()=>R(r.active);let M;const V=e=>{!t.touchable||e.touches.length>1||(u.start(e),s=!1,M=Date.now(),A(),_())},U=()=>{if(!t.touchable||!r.swiping)return;const e=Date.now()-M,l=m.value/e;if((Math.abs(l)>.25||Math.abs(m.value)>f.value/2)&&w.value){const e=t.vertical?u.offsetY.value:u.offsetX.value;let a=0;a=t.loop?e>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/f.value),I({pace:a,emitChange:!0})}else m.value&&I({pace:0});s=!1,r.swiping=!1,a("dragEnd",{index:b.value}),O()},L=(e,a)=>{const l=a===b.value,o=l?{backgroundColor:t.indicatorColor}:void 0;return p("i",{style:o,class:ct("indicator",{active:l})},null)};return _e({prev:()=>{_(),u.reset(),$e((()=>{r.swiping=!1,I({pace:-1,emitChange:!0})}))},next:S,state:r,resize:E,swipeTo:(e,a={})=>{_(),u.reset(),$e((()=>{let l;l=t.loop&&e===v.value?0===r.active?0:e:e%v.value,a.immediate?$e((()=>{r.swiping=!1})):r.swiping=!1,I({pace:l-r.active,emitChange:!0})}))}}),c({size:f,props:t,count:v,activeIndicator:b}),l((()=>t.initialSwipe),(e=>R(+e))),l(v,(()=>R(r.active))),l((()=>t.autoplay),O),l([ye,be,()=>t.width,()=>t.height],E),l(Se(),(e=>{"visible"===e?O():A()})),q(R),N((()=>R(r.active))),Ce((()=>R(r.active))),C(A),$(A),me("touchmove",(e=>{if(t.touchable&&r.swiping&&(u.move(e),w.value)){!t.loop&&(0===r.active&&m.value>0||r.active===v.value-1&&m.value<0)||(Te(e,t.stopPropagation),I({offset:m.value}),s||(a("dragStart",{index:b.value}),s=!0))}}),{target:n}),()=>{var e;return p("div",{ref:i,class:ct()},[p("div",{ref:n,style:x.value,class:ct("track",{vertical:t.vertical}),onTouchstartPassive:V,onTouchend:U,onTouchcancel:U},[null==(e=o.default)?void 0:e.call(o)]),o.indicator?o.indicator({active:b.value,total:v.value}):t.showIndicators&&v.value>1?p("div",{class:ct("indicators",{vertical:t.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[mt,ht]=re("tabs");var gt=T({name:mt,props:{count:Be(Number),inited:Boolean,animated:Boolean,duration:Be(se),swipeable:Boolean,lazyRender:Boolean,currentIndex:Be(Number)},emits:["change"],setup(t,{emit:a,slots:o}){const i=e(),n=e=>a("change",e),r=()=>{var e;const a=null==(e=o.default)?void 0:e.call(o);return t.animated||t.swipeable?p(ft,{ref:i,loop:!1,class:ht("track"),duration:1e3*+t.duration,touchable:t.swipeable,lazyRender:t.lazyRender,showIndicators:!1,onChange:n},{default:()=>[a]}):a},s=e=>{const a=i.value;a&&a.state.active!==e&&a.swipeTo(e,{immediate:!t.inited})};return l((()=>t.currentIndex),s),q((()=>{s(t.currentIndex)})),_e({swipeRef:i}),()=>p("div",{class:ht("content",{animated:t.animated||t.swipeable})},[r()])}});const[yt,bt]=re("tabs"),wt={type:ue("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:de(0),duration:de(.3),animated:Boolean,ellipsis:xe,swipeable:Boolean,scrollspy:Boolean,offsetTop:de(0),background:String,lazyRender:xe,showHeader:xe,lineWidth:se,lineHeight:se,beforeChange:Function,swipeThreshold:de(5),titleActiveColor:String,titleInactiveColor:String},xt=Symbol(yt);var kt=T({name:yt,props:wt,emits:["change","scroll","rendered","clickTab","update:active"],setup(t,{emit:a,slots:o}){let i,n,r,s,u;const d=e(),c=e(),v=e(),f=e(),m=it(),h=ce(d),[g,y]=function(){const t=e([]),a=[];return S((()=>{t.value=[]})),[t,e=>(a[e]||(a[e]=a=>{t.value[e]=a}),a[e])]}(),{children:b,linkChildren:w}=Ie(xt),x=j({inited:!1,position:"",lineStyle:{},currentIndex:-1}),k=B((()=>b.length>+t.swipeThreshold||!t.ellipsis||t.shrink)),I=B((()=>({borderColor:t.color,background:t.background}))),_=(e,t)=>{var a;return null!=(a=e.name)?a:t},C=B((()=>{const e=b[x.currentIndex];if(e)return _(e,x.currentIndex)})),$=B((()=>ve(t.offsetTop))),T=B((()=>t.sticky?$.value+i:0)),q=e=>{const a=c.value,l=g.value;if(!(k.value&&a&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,i=o.offsetLeft-(a.offsetWidth-o.offsetWidth)/2;s&&s(),s=function(e,t,a){let l,o=0;const i=e.scrollLeft,n=0===a?1:Math.round(1e3*a/16);let r=i;return function a(){r+=(t-i)/n,e.scrollLeft=r,++o<n&&(l=ae(a))}(),function(){te(l)}}(a,i,e?0:+t.duration)},A=()=>{const e=x.inited;z((()=>{const a=g.value;if(!a||!a[x.currentIndex]||"line"!==t.type||he(d.value))return;const l=a[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=t,n=l.offsetLeft+l.offsetWidth/2,r={width:ze(o),backgroundColor:t.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(r.transitionDuration=`${t.duration}s`),qe(i)){const e=ze(i);r.height=e,r.borderRadius=e}x.lineStyle=r}))},O=(e,l)=>{const o=(e=>{const t=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=t}})(e);if(!qe(o))return;const i=b[o],n=_(i,o),s=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||q(),A()),n!==t.active&&(a("update:active",n),s&&a("change",n,i.title)),r&&!t.scrollspy&&Ae(Math.ceil(Oe(d.value)-$.value))},R=(e,t)=>{const a=b.find(((t,a)=>_(t,a)===e)),l=a?b.indexOf(a):0;O(l,t)},E=(e=!1)=>{if(t.scrollspy){const a=b[x.currentIndex].$el;if(a&&h.value){const l=Oe(a,h.value)-T.value;n=!0,u&&u(),u=function(e,t,a,l){let o,i=le(e);const n=i<t,r=0===a?1:Math.round(1e3*a/16),s=(t-i)/r;return function a(){i+=s,(n&&i>t||!n&&i<t)&&(i=t),oe(e,i),n&&i<t||!n&&i>t?o=ae(a):l&&(o=ae(l))}(),function(){te(o)}}(h.value,l,e?0:+t.duration,(()=>{n=!1}))}}},M=(e,l,o)=>{const{title:i,disabled:n}=b[l],r=_(b[l],l);n||(Re(t.beforeChange,{args:[r],done:()=>{O(l),E()}}),Le(e)),a("clickTab",{name:r,title:i,event:o,disabled:n})},V=e=>{r=e.isFixed,a("scroll",e)},U=()=>{if("line"===t.type&&b.length)return p("div",{class:bt("line"),style:x.lineStyle},null)},L=()=>{var e,a,l;const{type:i,border:n,sticky:r}=t,s=[p("div",{ref:r?void 0:v,class:[bt("wrap"),{[Ne]:"line"===i&&n}]},[p("div",{ref:c,role:"tablist",class:bt("nav",[i,{shrink:t.shrink,complete:k.value}]),style:I.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),b.map((e=>e.renderTitle(M))),U(),null==(a=o["nav-right"])?void 0:a.call(o)])]),null==(l=o["nav-bottom"])?void 0:l.call(o)];return r?p("div",{ref:v},[s]):s},H=()=>{A(),z((()=>{var e,t;q(!0),null==(t=null==(e=f.value)?void 0:e.swipeRef.value)||t.resize()}))};l((()=>[t.color,t.duration,t.lineWidth,t.lineHeight]),A),l(ye,H),l((()=>t.active),(e=>{e!==C.value&&R(e)})),l((()=>b.length),(()=>{x.inited&&(R(t.active),A(),z((()=>{q(!0)})))}));return _e({resize:H,scrollTo:e=>{z((()=>{R(e),E(!0)}))}}),N(A),Ce(A),ne((()=>{R(t.active,!0),z((()=>{x.inited=!0,v.value&&(i=ge(v.value).height),q(!0)}))})),nt(d,A),me("scroll",(()=>{if(t.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:t}=ge(b[e].$el);if(t>T.value)return 0===e?0:e-1}return b.length-1})();O(e)}}),{target:h,passive:!0}),w({id:m,props:t,setLine:A,scrollable:k,onRendered:(e,t)=>a("rendered",e,t),currentName:C,setTitleRefs:y,scrollIntoView:q}),()=>p("div",{ref:d,class:bt([t.type])},[t.showHeader?t.sticky?p(ut,{container:d.value,offsetTop:$.value,onScroll:V},{default:()=>[L()]}):L():null,p(gt,{ref:f,count:b.length,inited:x.inited,animated:t.animated,duration:t.duration,swipeable:t.swipeable,lazyRender:t.lazyRender,currentIndex:x.currentIndex,onChange:O},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const It=Symbol(),[_t,St]=re("tab"),Ct=T({name:_t,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:se,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:xe},setup(e,{slots:t}){const a=B((()=>{const t={},{type:a,color:l,disabled:o,isActive:i,activeColor:n,inactiveColor:r}=e;l&&"card"===a&&(t.borderColor=l,o||(i?t.backgroundColor=l:t.color=l));const s=i?n:r;return s&&(t.color=s),t})),l=()=>{const a=p("span",{class:St("text",{ellipsis:!e.scrollable})},[t.title?t.title():e.title]);return e.dot||qe(e.badge)&&""!==e.badge?p(Ee,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[a]}):a};return()=>p("div",{id:e.id,role:"tab",class:[St([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:a.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[$t,Tt]=re("swipe-item");const jt=we(T({name:$t,setup(e,{slots:t}){let a;const l=j({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Me(pt);if(!o)return;const n=B((()=>{const e={},{vertical:t}=o.props;return o.size.value&&(e[t?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${t?"Y":"X"}(${l.offset}px)`),e})),r=B((()=>{const{loop:e,lazyRender:t}=o.props;if(!t||a)return!0;if(!l.mounted)return!1;const n=o.activeIndicator.value,r=o.count.value-1,s=0===n&&e?r:n-1,u=n===r&&e?0:n+1;return a=i.value===n||i.value===s||i.value===u,a}));return q((()=>{z((()=>{l.mounted=!0}))})),_e({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:Tt(),style:n.value},[r.value?null==(e=t.default)?void 0:e.call(t):null])}}})),[Bt,zt]=re("tab");const qt=we(T({name:Bt,props:pe({},He,{dot:Boolean,name:se,badge:se,title:String,disabled:Boolean,titleClass:Ve,titleStyle:[String,Object],showZeroBadge:xe}),setup(t,{slots:a}){const o=it(),i=e(!1),n=_(),{parent:r,index:s}=Me(xt);if(!r)return;const u=()=>{var e;return null!=(e=t.name)?e:s.value},d=B((()=>{const e=u()===r.currentName.value;return e&&!i.value&&(i.value=!0,r.props.lazyRender&&z((()=>{r.onRendered(u(),t.title)}))),e})),c=e(""),v=e("");A((()=>{const{titleClass:e,titleStyle:a}=t;c.value=e?lt(e):"",v.value=a&&"string"!=typeof a?function(e){let t="";if(!e||Xe(e))return t;for(const a in e){const l=e[a];(Xe(l)||"number"==typeof l)&&(t+=`${a.startsWith("--")?a:Ge(a)}:${l};`)}return t}(Ke(a)):a}));const f=e(!d.value);return l(d,(e=>{e?f.value=!1:$e((()=>{f.value=!0}))})),l((()=>t.title),(()=>{r.setLine(),r.scrollIntoView()})),O(It,d),_e({id:o,renderTitle:e=>p(Ct,M({key:o,id:`${r.id}-${s.value}`,ref:r.setTitleRefs(s.value),style:v.value,class:c.value,isActive:d.value,controls:o,scrollable:r.scrollable.value,activeColor:r.props.titleActiveColor,inactiveColor:r.props.titleInactiveColor,onClick:t=>e(n.proxy,s.value,t)},Ue(r.props,["type","color","shrink"]),Ue(t,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const t=`${r.id}-${s.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=r.props;if(!a.default&&!l)return;const v=u||d.value;if(l||n)return p(jt,{id:o,role:"tabpanel",class:zt("panel-wrapper",{inactive:f.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":t,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:zt("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=i.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return R(p("div",{id:o,role:"tabpanel",class:zt("panel"),tabindex:v?0:-1,"aria-labelledby":t,"data-allow-mismatch":"attribute"},[m]),[[E,v]])}}})),Nt=we(kt),At={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ot={class:"pc_container",style:{display:"flex"}},Rt={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Et={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Mt={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Vt=["src"],Ut=["src"],Lt={class:"mobile_container"},Ht={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Wt={id:"typing-area",style:{"max-height":"calc(100% - 50px)",overflow:"hidden","overflow-y":"scroll"}},Dt=ee({__name:"index",setup(f){const g=Q("loading.png"),y=Q("copy.png"),b=j({}),w=a(),x={},I=e([]),_=e(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),S=e(null),{locale:C}=V(),$=U(),T=e(!1);let z=e("a"),N=e("");const A=e(""),O=e(null),E=e(null);let M;const ee=async()=>{var e;await F({appId:w.params.uuid,user:_.value.userName,mode:null==(e=S.value)?void 0:e.mode,task_id:A.value}),setTimeout((()=>{pe.abort(),fe=!0,ce.value=[],ye()}),0)};q((async()=>{var e,a;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=w.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(a=w.params)?void 0:a.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),C.value=L(),t.get("userInfo"))_e(),ue();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=H("current_location_country",1);e&&"中国"!=e?$.push("/login"):window.addLoginDom()}})),l(N,(()=>{N.value&&(z.value="b")}));const te=()=>{w.params.uuid&&X({appId:w.params.uuid,user:_.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(I.value=e.user_input_form,e.user_input_form.forEach((e=>{const t=Object.keys(e)[0],a=e[t].variable;x[a]={label:e[t].label},b[a]=""})))}))},ae=B((()=>!!I.value.length)),le=B((()=>I.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),oe=()=>{w.params.uuid&&Z({appId:w.params.uuid,user:_.value.userName}).then((e=>{S.value={...e}}))},ie=e(!1),ne=e(!1),re=e(!1),se=e(!1),ue=async()=>{var e,t;let a=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=_.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:w.params.uuid,userUuid:null==(t=_.value)?void 0:t.openid}];await W.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",a)},de=()=>{var e,t;if(0!=le.value.length||(a=b,Object.values(a).some((e=>e)))){var a;for(let e in b)if(le.value.includes(e)&&!b[e])return void Y({message:`${x[e].label}为必填项！`,type:"error"});(null==(e=S.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(t=S.value)?void 0:t.mode)?Y({type:"success",message:"计划中，敬请期待..."}):"completion"==S.value.mode?ge():he())}else Y({message:"请输入您的问题。",type:"error"})},ce=e([]),ve=e(!1);let pe,fe=!1,me=!1;const he=async()=>{N.value="",ce.value=[],ve.value=!1,fe=!1,pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run`;0,ie.value=!0,ne.value=!0,se.value=!1,await We(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:_.value.userName,inputs:{...b,outputLanguage:b.outputLanguage?b.outputLanguage:"中文"==G()?"简体中文":G()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){var t,a;if(e.data.trim())try{const l=JSON.parse(e.data);if(A.value=l.task_id,l.error)throw new Error(l.error);"text_chunk"===l.event&&(T.value=!0,ce.value.push(null==(t=null==l?void 0:l.data)?void 0:t.text),ve.value||ye()),"workflow_started"===l.event&&(ie.value=!1,re.value=!0),"workflow_finished"===l.event&&(T.value||(ce.value.push(null==(a=null==l?void 0:l.data)?void 0:a.outputs.text),ve.value||ye()),fe=!0,z.value="b")}catch(l){xe(l)}},onerror(e){xe(e)},signal:pe.signal,openWhenHidden:!0})}catch(e){xe()}},ge=async()=>{N.value="",ce.value=[],ve.value=!1,fe=!1,pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages`;0,ie.value=!0,ne.value=!0,se.value=!1,await We(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:_.value.userName,inputs:{...b,outputLanguage:G()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){if(ie.value=!1,re.value=!0,e.data.trim())try{const t=JSON.parse(e.data);if(A.value=t.task_id,t.error)throw new Error(t.error);"message"===t.event&&(ce.value.push(null==t?void 0:t.answer),ve.value||ye()),"message_end"===t.event&&(z.value="b",fe=!0)}catch(t){xe(t)}},onerror(e){xe(e)},signal:pe.signal,openWhenHidden:!0})}catch(e){xe()}},ye=()=>{if(0===ce.value.length)return ve.value=!1,me=!0,void be();ve.value=!0;const e=ce.value.shift();we(e).then((()=>{ye()}))},be=()=>{me&&fe&&(ne.value=!1,re.value=!1,se.value=!0)},we=e=>new Promise((t=>{let a=0;M=setInterval((()=>{if(a<e.length){N.value+=e[a++];const t=document.getElementById("typing-area");t.scrollTop=t.scrollHeight}else clearInterval(M),t()}),15)})),xe=()=>{setTimeout((()=>{pe.abort()}),0),ie.value=!1,re.value=!1,ne.value=!1,ve.value=!1,Y.error("访问太火爆了！休息下，请稍后再试！"),N.value="访问太火爆了！休息下，请稍后再试！"},ke=async()=>{try{await navigator.clipboard.writeText(N.value),Y({type:"success",message:"复制成功"})}catch(e){Y(e)}},Ie=()=>{for(let e in b)b[e]="";O.value.forEach((e=>{e.updateMessage()})),E.value.forEach((e=>{e.updateMessage()}))},_e=()=>{if(localStorage.getItem("yudaoToken"))return te(),void oe();const e=t.get("userInfo");if(e){const t=JSON.parse(e);try{D({userId:t.userId,userName:t.userName,realName:t.realName,avatar:t.avatar,plaintextUserId:t.plaintextUserId,mobile:t.mobile,email:t.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),te(),oe())}))}catch(a){}}};return(e,t)=>{const a=k,l=J("v-md-preview"),f=K;return o(),i("div",At,[n("div",Ot,[s(ae)?(o(),i(c,{key:0},[n("div",Rt,[(o(!0),i(c,null,v(s(I),((t,a)=>(o(),i("div",{class:"flex",key:a},[(o(!0),i(c,null,v(t,((t,a)=>(o(),u(Pe,{key:t.variable,type:a,label:e.$t(null==t?void 0:t.label),value:s(b)[null==t?void 0:t.variable],required:null==t?void 0:t.required,placeholder:`${null==t?void 0:t.label}`,max_length:null==t?void 0:t.max_length,options:null==t?void 0:t.options,fileVerify:null==t?void 0:t.allowed_file_types,"onUpdate:value":e=>s(b)[null==t?void 0:t.variable]=e,ref_for:!0,ref_key:"childRef",ref:O},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Et,[p(a,{onClick:Ie},{default:d((()=>[m("Clear")])),_:1}),p(a,{onClick:de,loading:s(ne),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])]),R((o(),i("div",{style:{width:"60%","margin-left":"15px",padding:"15px","border-radius":"10px"},class:P({"bg-[#fff]":s(N)})},[n("div",Mt,[s(N)?(o(),u(l,{key:0,text:s(N),id:"previewMd"},null,8,["text"])):h("",!0)]),n("div",null,[s(re)?(o(),i("img",{key:0,src:s(g),alt:"loading",class:"spinner"},null,8,Vt)):h("",!0),s(re)?(o(),i("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:ee},r(e.$t("tool.stopGeneration")),1)):h("",!0),s(se)?(o(),i("img",{key:2,onClick:ke,src:s(y),alt:"",style:{width:"20px"},class:"copy"},null,8,Ut)):h("",!0)])],2)),[[f,s(ie)]])],64)):h("",!0)]),n("div",Lt,[p(s(Nt),{active:s(z),shrink:"","line-width":"20"},{default:d((()=>[p(s(qt),{title:"输入",name:"a"},{default:d((()=>[(o(!0),i(c,null,v(s(I),((t,a)=>(o(),i("div",{class:"flex",key:a},[(o(!0),i(c,null,v(t,((t,a)=>(o(),u(Pe,{key:t.variable,type:a,label:e.$t(null==t?void 0:t.label),value:s(b)[null==t?void 0:t.variable],required:null==t?void 0:t.required,placeholder:`${null==t?void 0:t.label}`,max_length:null==t?void 0:t.max_length,options:null==t?void 0:t.options,fileVerify:null==t?void 0:t.allowed_file_types,"onUpdate:value":e=>s(b)[null==t?void 0:t.variable]=e,ref_for:!0,ref_key:"childRefs",ref:E},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ht,[p(a,{onClick:Ie},{default:d((()=>[m("Clear")])),_:1}),p(a,{onClick:t[0]||(t[0]=e=>de()),loading:s(ne),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])])),_:1}),p(s(qt),{title:"结果",name:"b"},{default:d((()=>[n("div",Wt,[s(N)?(o(),u(l,{key:0,text:s(N),id:"previewMd"},null,8,["text"])):h("",!0)])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-853ef481"]]);export{Dt as default};
