/* empty css                  *//* empty css                *//* empty css                 */import{s as e}from"./index-c4e7b377.js";import{r as a,aM as l,d as s,e as t,f as u,g as i,q as r,j as n,m as o,k as m,F as v,z as p,T as d,h as c,aN as f,i as x,a4 as y,C as j,E as h,H as g}from"./index-2502211c.js";import{s as k}from"./index-5d3c44df.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},b=["innerHTML"],z={class:"flex justify-center mt-10"},A={__name:"index",setup(A){const C=a(""),E=a([]),H=a([]),M=a(5),N=()=>{if(a=C.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!C.value)return y.warning("请输入英文内容");var a;e({text:C.value}).then((e=>{e&&e&&e.data&&(E.value=e.data,E.value=k(E.value),M.value=5,S())}))},S=()=>{let e=[];5==M.value?(e=[0,5],M.value=3):3==M.value?(e=[5,8],M.value=2):2==M.value&&(e=[8,10],M.value=5),H.value=JSON.parse(JSON.stringify(E.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,A=g,E=l("copy");return s(),t("div",w,[u(y,{modelValue:i(C),"onUpdate:modelValue":a[0]||(a[0]=e=>r(C)?C.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:N},{default:o((()=>[m("改 写")])),_:1})]),i(H)&&i(H).length?(s(),t("div",_,[(s(!0),t(v,null,p(i(H),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,b),d((s(),c(A,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(i(f))])),_:2},1024)),[[E,e.text]])])))),128)),n("div",z,[u(k,{type:"primary",link:"",onClick:S},{default:o((()=>[m("换一换")])),_:1})])])):x("",!0)])}}};export{A as default};
