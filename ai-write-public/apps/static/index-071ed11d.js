/* empty css                  */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                   *//* empty css                *//* empty css                 */import{m as e}from"./index-5a807990.js";import{r as l,aL as t,c as s,d as a,i as r,e as i,f as o,p as d,$ as c,F as n,y as p,t as u,g as x,S as v,l as m,aM as f,aQ as b,h as g,a2 as h,B as y,G as j,aP as w,aR as _,ae as k,af as z,aS as C}from"./index-d35f61c7.js";import{s as T}from"./index-d3608df3.js";/* empty css                   */import{_ as A}from"./_plugin-vue_export-helper-1b428a4d.js";const L=e=>(k("data-v-0ac38a84"),e=e(),z(),e),M={class:"p-4 h-full"},V={class:"bg-white box h-full flex"},B={class:"w-[50%] flex flex-col"},H=L((()=>r("div",{class:"text-[16px] px-6 font-bold text-gray-600 h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"}," 输入摘要 ",-1))),S={class:"flex justify-end mb-4 mr-4"},$={class:"flex-1 flex flex-col h-full overflow-hidden"},F=L((()=>r("div",{class:"text-[16px] px-6 font-bold text-gray-600 min-h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"}," 生成5个Title ",-1))),G={class:"border-r flex-1 p-4 flex flex-col overflow-hidden"},I={class:"flex items-center mb-4"},P=["onClick"],Q={key:1,class:"py-4 flex-1 overflow-auto"},R={class:"mb-4"},U={class:"mb-2 relative"},Z={class:"text-[#0071bc]"},q={class:"text-gray-500 text-[12px]"},D={class:"text-gray-500 text-[12px] flex items-center mb-4"},E=L((()=>r("span",null,"参考文献（显示5条）",-1))),J={class:"flex"},K={class:"mr-2"},N={class:"w-[22px] h-[22px] flex items-center justify-center px-1 bg-[#4d73be] text-white rounded-md"},O={class:"mb-2 relative"},W=["innerHTML"],X={class:"text-gray-500 text-[12px]"},Y=A({__name:"index",setup(k){const z=l(""),A=l(null),L=l(1),Y=l([]),ee=l(null),le=e=>{A.value=e,L.value=1,te()},te=()=>{if(!z.value)return h.warning("请输入摘要");e("recommend-title",{text:z.value,mode:A.value}).then((e=>{e&&e.data&&(Y.value=e.data,Y.value=T(Y.value),ee.value=Y.value[0],ee.value.recArticles=T(ee.value.recArticles))}))};return(e,l)=>{const h=y,k=C,T=j,te=w,se=_,ae=t("copy");return s(),a("div",M,[r("div",V,[r("div",B,[H,i(h,{class:"p-6 flex-1",modelValue:o(z),"onUpdate:modelValue":l[0]||(l[0]=e=>d(z)?z.value=e:null),type:"textarea",placeholder:"","show-word-limit":"",resize:"none"},null,8,["modelValue"]),r("div",S,[r("div",{class:c(["text-white font-bold cursor-pointer rounded-md px-4 py-1 mr-4","classics"==o(A)?"bg-[#499557]":"bg-gray-400"]),onClick:l[1]||(l[1]=e=>le("classics"))}," 经典 ",2),r("div",{class:c(["text-white font-bold cursor-pointer rounded-md px-4 py-1","light"==o(A)?"bg-[#499557]":"bg-gray-400"]),onClick:l[2]||(l[2]=e=>le("light"))}," 眼前一亮 ",2)])]),r("div",$,[F,r("div",G,[r("div",I,[(s(!0),a(n,null,p(o(Y),((e,l)=>(s(),a("div",{class:c(["cursor-pointer px-4 text-white mr-4",l+1==o(L)?"bg-[#4d73be]":"bg-gray-400"]),key:l,onClick:t=>((e,l)=>{L.value=l+1,ee.value=e})(e,l)},u(`Title${l+1}`),11,P)))),128))]),o(ee)?(s(),a("div",Q,[r("div",R,[r("div",U,[r("span",Z,u(o(ee).title),1),v((s(),x(T,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-4 absolute top-1"},{default:m((()=>[i(o(f))])),_:1})),[[ae,o(ee).title]])]),r("div",q,u(o(ee).titleZh),1),i(te)]),r("div",null,[r("div",D,[E,i(se,{effect:"dark",content:"以上Title由系统自动参照海量高分文献的主题内容生成，此处随机显示5条。",placement:"top"},{default:m((()=>[i(T,{color:"#999",size:"16",class:"cursor-pointer"},{default:m((()=>[i(o(b))])),_:1})])),_:1})]),(s(!0),a(n,null,p(o(ee).recArticles,((e,l)=>(s(),a("div",{key:l},[r("div",J,[r("div",K,[r("div",N,u(l+1),1)]),r("div",null,[r("div",O,[r("span",{innerHTML:e.title},null,8,W),v((s(),x(T,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-4 absolute top-1"},{default:m((()=>[i(o(f))])),_:2},1024)),[[ae,e.title.replace(/<[^>]+>/g,"")]])]),r("div",X,u(e.zh_title),1)])]),l+1<o(ee).recArticles.length?(s(),x(te,{key:0})):g("",!0)])))),128))])])):(s(),x(k,{key:0,description:"暂无数据"}))])])])])}}},[["__scopeId","data-v-0ac38a84"]]);export{Y as default};
