import{M as a,b as s,u as e,o as n,a6 as i,aD as o,d as t,e as r}from"./index-0073a309.js";const l=a({__name:"index",setup(a){const{t:l}=s(),c=e(),p=JSON.parse(decodeURIComponent(c.params.payInfo));return n((async()=>{const a=navigator.userAgent;if(null!=a)if(a.includes("MicroMessenger"))i.warning(l("tool.pleasescanwithalipay"));else if(a.includes("AlipayClient")){const a=await o(p);location.href=a}})),(a,s)=>(t(),r("div"))}});export{l as default};
