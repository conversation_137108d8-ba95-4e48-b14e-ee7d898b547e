import"./index-77fb797e.js";const s=s=>new URL(Object.assign({"../assets/svg/AI写作助手.svg":"/apps/static/AI写作助手-4439f083.svg","../assets/svg/AI智能润色.svg":"/apps/static/AI智能润色-b25dcc38.svg","../assets/svg/AI降重改写.svg":"/apps/static/AI降重改写-6db9a751.svg","../assets/svg/TCGA可视化.svg":"/apps/static/TCGA可视化-a2326cc6.svg","../assets/svg/Target.svg":"/apps/static/Target-a3ac2d09.svg","../assets/svg/copy.png":"/apps/static/copy-50df1904.png","../assets/svg/loading.png":"/apps/static/loading-c174753b.png","../assets/svg/个案报道.svg":"/apps/static/个案报道-8d93f899.svg","../assets/svg/中文版GEO.svg":"/apps/static/中文版GEO-cb70c0c6.svg","../assets/svg/中文版Pubmed.svg":"/apps/static/中文版Pubmed-503b7015.svg","../assets/svg/免疫治疗.svg":"/apps/static/免疫治疗-abf3a5c2.svg","../assets/svg/免疫评分.svg":"/apps/static/免疫评分-9885d459.svg","../assets/svg/包季.png":"/apps/static/包季-5770f926.png","../assets/svg/包年.png":"/apps/static/包年-7a835991.png","../assets/svg/包月.png":"/apps/static/包月-9f366f1b.png","../assets/svg/单细胞分析.svg":"/apps/static/单细胞分析-1fc10699.svg","../assets/svg/国自然数分.svg":"/apps/static/国自然数分-bba1ed05.svg","../assets/svg/国自然查询分析以及申请书写作.png":"/apps/static/国自然查询分析以及申请书写作-c9fdefa7.png","../assets/svg/图例写作.svg":"/apps/static/图例写作-b0c7158e.svg","../assets/svg/场景写作.svg":"/apps/static/场景写作-66535fa6.svg","../assets/svg/基于AI的写作文本加工.png":"/apps/static/基于AI的写作文本加工-143ed4a6.png","../assets/svg/基于AI的写作文本加工In.png":"/apps/static/基于AI的写作文本加工In-4ad75525.png","../assets/svg/实验方案设计.svg":"/apps/static/实验方案设计-fcb651f2.svg","../assets/svg/审稿人回复信.svg":"/apps/static/审稿人回复信-e008b87e.svg","../assets/svg/批量生存分析.svg":"/apps/static/批量生存分析-b6ce5313.svg","../assets/svg/搜索.svg":"/apps/static/搜索-8381f2c3.svg","../assets/svg/机制图流程图绘制.svg":"/apps/static/机制图流程图绘制-12a48d66.svg","../assets/svg/泛癌分析.svg":"/apps/static/泛癌分析-73d156cf.svg","../assets/svg/生信零代码.svg":"/apps/static/生信零代码-9774489e.svg","../assets/svg/生物信息学公共数据库分析.png":"/apps/static/生物信息学公共数据库分析-2fd13650.png","../assets/svg/相关性分析.svg":"/apps/static/相关性分析-23876f40.svg","../assets/svg/细胞动物模型查询.svg":"/apps/static/细胞动物模型查询-7502f80a.svg","../assets/svg/综述Review.svg":"/apps/static/综述Review-e9bed271.svg","../assets/svg/语法纠错.svg":"/apps/static/语法纠错-f8253223.svg","../assets/svg/课题思路助手.svg":"/apps/static/课题思路助手-544761c8.svg","../assets/svg/题材和论文章节的写作语料.png":"/apps/static/题材和论文章节的写作语料-b0e8d171.png","../assets/svg/高分title生成器.svg":"/apps/static/高分title生成器-b33f2877.svg","../assets/svg/高分选题.svg":"/apps/static/高分选题-3b843561.svg"})[`../assets/svg/${s}`],self.location).href,t=(s,t=5)=>{if(s&&s.length>0){if(s.length>5){let a=Math.ceil(s.length/t),g=[];for(let t=0;t<s.length;t+=a)g.push(s.slice(t,t+a));g.forEach((s=>{s.sort((()=>Math.random()-.5))}));return g.reduce(((s,t)=>s.concat(t)),[])}return s.sort((()=>Math.random()-.5))}return[]},a=(s,t)=>{const a=document.createElement("style");a.type="text/css",a.innerHTML=s,document.head.appendChild(a);var g=document.createElement("script");g.type="text/javascript",g.text=t,document.body.appendChild(g)};export{s as g,a as i,t as s};
