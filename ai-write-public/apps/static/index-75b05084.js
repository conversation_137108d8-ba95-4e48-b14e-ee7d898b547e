import{a as s,d as e,e as i,j as a,k as o,a4 as t,x as l,y as d}from"./index-837d36db.js";const n={name:"AssistantComponent",data:()=>({isCollapsed:!1,isQrCodeVisible:!1,isMobile:!1}),mounted(){this.checkMobile(),window.addEventListener("resize",this.checkMobile)},beforeUnmount(){window.removeEventListener("resize",this.checkMobile)},methods:{toggleCollapse(){this.isCollapsed=!this.isCollapsed,this.isCollapsed?setTimeout((()=>{this.isQrCodeVisible=!1}),300):this.isQrCodeVisible=!0},checkMobile(){this.isMobile=window.innerWidth<=768,this.isMobile&&(this.isCollapsed=!0,this.isQrCodeVisible=!1)}}},c=s=>(l("data-v-ee471f14"),s=s(),d(),s),r=[c((()=>a("img",{src:"/apps/static/kefu-ae1166e2.png",class:"fas fa-user-astronaut",alt:"客服"},null,-1)))],p=c((()=>a("img",{src:"/apps/static/qrcode-4bdb4a15.png",alt:"QR Code"},null,-1)));const h=s(n,[["render",function(s,l,d,n,c,h){return e(),i("div",{class:t(["assistant-container",{"is-collapsed":c.isCollapsed}])},[a("div",{class:"assistant-icon",onClick:l[0]||(l[0]=(...s)=>h.toggleCollapse&&h.toggleCollapse(...s))},r),a("div",{class:t(["qr-code",{"is-visible":!c.isCollapsed&&c.isQrCodeVisible}])},[p,o(" 扫码添加小助手 ")],2)],2)}],["__scopeId","data-v-ee471f14"]]);export{h as c};
