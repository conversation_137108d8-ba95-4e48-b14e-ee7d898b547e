import{_ as e,a as l}from"./Editor-9cd98d73.js";/* empty css                  *//* empty css                 */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                    *//* empty css                  */import{a}from"./index-c14900e6.js";import{s as t}from"./index-9caef982.js";/* empty css                   */import{a as s,r as o,O as n,o as r,d as i,e as d,j as u,F as m,z as p,a1 as c,g as v,i as f,t as b,f as x,q as g,m as y,k as h,h as k,a5 as j,C as V,E as w,aj as C,aM as _,aN as S,aO as M,a9 as T,aa as z}from"./index-10a1c2d6.js";/* empty css                  *//* empty css                */const N=e=>(T("data-v-1e15cf5a"),e=e(),z(),e),U={class:"h-full flex overflow-auto"},H={class:"flex flex-col w-[160px] mr-4"},$=["onClick"],L={key:0,class:"w-[4px] h-full"},A={class:"flex-1"},E={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},I={class:"flex items-center my-8 overflow-hidden"},O={class:"flex items-center mr-4"},R={class:"flex items-center mr-2"},Y=N((()=>u("span",{class:"mr-2"},"影响因子：",-1))),q={class:"flex items-center"},D=N((()=>u("div",{class:"w-[60px]"},"年份范围",-1))),F={class:"relative flex-1"},G={class:"text-[#419eff] absolute font-bold -top-1.5 left-[30%]"},K={class:"mr-2"},P=["innerHTML","onClick"],W=["innerHTML"],B=s({__name:"index",setup(s){const T=o("类风湿关节炎铁死亡特征基因图的构建"),z=o(""),N=o(1),B=o(!0),J=o([]),Q=o([1990,2023]),X=o({}),Z=n({pageNo:1,pageSize:20}),ee=o([{label:"Title",value:"title"},{label:"Keywords",value:"keyword"},{label:"Abstract",value:"abstract"},{label:"Introduction",value:"introduction"},{label:"Methods",value:"methods"},{label:"Results",value:"results"},{label:"Discussion",value:"discussion"},{label:"Acknowledge",value:"acknowleg"},{label:"全库检索(CNS)",value:"all"}]),le=o("title"),ae=e=>{if(!T.value)return j.warning("请输入关键词或短句");1==e&&(Z.pageNo=1);let l=J.value.map((e=>({field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]})));a(le.value,{key:T.value,page:Z.pageNo-1,size:Z.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l,beginYear:Q.value[0],endYear:Q.value[1],mySentenceRange:0,sorts:[]}).then((e=>{e&&e.data&&(X.value=e.data,X.value.content=t(X.value.content))}))},te=()=>{N.value++},se=()=>{ae()};return r((()=>{ae()})),(a,t)=>{const s=V,o=w,n=e,r=C,j=_,oe=S,ne=M,re=l;return i(),d("div",U,[u("div",H,[(i(!0),d(m,null,p(v(ee),((e,l)=>(i(),d("div",{class:c(["nav-item flex items-center font-bold text-[#2d3858] cursor-pointer border border-solid border-top-0 border-left-0 border-right-0 border-gray-200 h-[46px]",v(le)==e.value?"bg-[#7e90b8]":"bg-[#f8f8f8]"]),key:l,onClick:l=>(e=>{le.value=e.value,T.value="",X.value={},J.value=[]})(e)},[v(le)!=e.value?(i(),d("div",L)):f("",!0),u("div",{class:c(["pl-10 h-full flex items-center",v(le)==e.value?"border border-solid border-left-4 border-[#2b3858] border-top-0 border-bottom-0 border-right-0":""])},b(e.label),3)],10,$)))),128))]),u("div",A,[u("div",E,[x(s,{class:"h-full !text-[24px]",modelValue:v(T),"onUpdate:modelValue":t[0]||(t[0]=e=>g(T)?T.value=e:null),placeholder:"这里输入关键词或短句，中英文均可",clearable:""},null,8,["modelValue"]),x(o,{type:"primary",onClick:t[1]||(t[1]=e=>ae(1))},{default:y((()=>[h("查 询")])),_:1})]),(i(),k(n,{class:"h-[380px] mb-4",modelValue:v(z),"onUpdate:modelValue":t[2]||(t[2]=e=>g(z)?z.value=e:null),onClear:te,key:v(N)},null,8,["modelValue"])),u("div",I,[u("div",O,[u("div",{class:c(["mr-2",v(B)?"text-[#409eff]":""])},"翻译",2),x(r,{modelValue:v(B),"onUpdate:modelValue":t[3]||(t[3]=e=>g(B)?B.value=e:null)},null,8,["modelValue"])]),u("div",R,[Y,x(oe,{modelValue:v(J),"onUpdate:modelValue":t[4]||(t[4]=e=>g(J)?J.value=e:null)},{default:y((()=>[x(j,{label:"3"},{default:y((()=>[h(b("<3分"))])),_:1}),x(j,{label:"5"},{default:y((()=>[h("3-10分")])),_:1}),x(j,{label:"10"},{default:y((()=>[h(b(">10分"))])),_:1})])),_:1},8,["modelValue"])]),u("div",q,[D,u("div",F,[x(ne,{class:"ml-6 !w-[132px]",modelValue:v(Q),"onUpdate:modelValue":t[5]||(t[5]=e=>g(Q)?Q.value=e:null),range:"",max:2023,min:1990,onChange:se},null,8,["modelValue"]),u("span",G,b(`${v(Q)[0]}-${v(Q)[1]}`),1)])])]),u("div",null,[(i(!0),d(m,null,p(v(X).content,((e,l)=>(i(),d("div",{class:"flex mb-8",key:l},[u("div",K,b(l+1)+".",1),u("div",null,[u("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");z.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,P),v(B)?(i(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,W)):f("",!0)])])))),128))]),v(X)&&v(X).eleTotal?(i(),k(re,{key:0,class:"pb-10",total:v(X).eleTotal,page:v(Z).pageNo,"onUpdate:page":t[6]||(t[6]=e=>v(Z).pageNo=e),limit:v(Z).pageSize,"onUpdate:limit":t[7]||(t[7]=e=>v(Z).pageSize=e),onPagination:ae},null,8,["total","page","limit"])):f("",!0)])])}}},[["__scopeId","data-v-1e15cf5a"]]);export{B as default};
