/* empty css                  */import{_ as e}from"./index-1e4587e0.js";import{_ as a,a as t,u as i,r as n,b as s,w as l,o,n as p,c,d,e as r,f as m,g as u,h as _,i as v,j as g,t as f,k as h,l as b,m as x,p as y,q as I,s as w,v as A,E,x as j,y as T,z as S}from"./index-59bb6979.js";import{g as V}from"./index-62001382.js";import{c as L}from"./index-ea28244e.js";const O=[{name:"writingAssistant",path:"/writingAssistant",type:"医学写作",meta:{title:"论文",dify_app_uuid:"10c03431-1933-4f59-8848-e2e3ccb2c557",desc:"医学论文是医学领域的研究者撰写的，旨在探讨疾病病因、诊断、治疗、预防或医学理论、技术进展的学术性文章。它基于科学实验或临床观察，通过严谨的数据分析，提出新发现、新观点或验证已有理论，为医学实践提供科学依据和指导。",icon:V("AI写作助手.svg"),bg:V("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-ccb6095e.js")),["static/index-ccb6095e.js","static/Editor-b263cd99.js","static/index-59bb6979.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/el-tooltip-4ed993c7.js","static/index-1613dbfb.js","static/index-62001382.js","static/index-044dd070.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"intelligentPolishing",path:"/intelligentPolishing",type:"医学写作",meta:{title:"综述",dify_app_uuid:"4955c5cc-a366-45d8-8046-aa3044ff77bc",desc:"医学综述是对某一医学领域或专题在一定时期内研究成果的汇总和分析，旨在系统回顾研究进展，评价研究质量，探讨存在问题，预测未来方向，为研究者提供全面、深入的信息参考。",icon:V("AI智能润色.svg"),bg:V("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-4edbbf0d.js")),["static/index-4edbbf0d.js","static/index-59bb6979.js","static/index-87571f09.css","static/index-1613dbfb.js","static/index-84dd1b27.css","static/el-button-b4242b8c.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewer",path:"/reviewer",type:"医学写作",meta:{title:"研究方案",dify_app_uuid:"d7df5b59-280e-4dda-b0f2-700a12b0b1f9",desc:"医学研究方案是科研人员为探索医学问题而设计的详细计划，包括研究目的、假设、方法、样本选择、数据收集与分析步骤、预期结果及伦理考量等，确保研究过程科学、严谨、可重复。",icon:V("审稿人回复信.svg"),bg:V("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-f180c7f3.js")),["static/index-f180c7f3.js","static/Editor-b263cd99.js","static/index-59bb6979.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-1613dbfb.js","static/index-62001382.js","static/index-cc9df6ae.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"rewrite",path:"/rewrite",type:"医学写作",meta:{title:"软文",dify_app_uuid:"d1fc1b66-47ce-45cd-9ff9-ee49a2e44881",desc:"医学软文是以医疗、健康领域为背景，通过撰写具有吸引力和可读性的文章，间接推广医疗产品、服务或健康理念的一种营销手段。",icon:V("AI降重改写.svg"),bg:V("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-53446599.js")),["static/index-53446599.js","static/index-1613dbfb.js","static/index-59bb6979.js","static/index-87571f09.css","static/index-62001382.js","static/el-button-b4242b8c.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"topTitle",path:"/topTitle",type:"医学写作",meta:{title:"指南共识",dify_app_uuid:"7252420d-c356-4bed-9e06-ce6c3bc01270",desc:"指南共识是指在医学领域中，基于系统评价的证据和专家经验，针对特定临床问题制定的指导性建议或推荐意见。",icon:V("高分title生成器.svg"),bg:V("题材和论文章节的写作语料.png")},component:()=>a((()=>import("./index-47310c9f.js")),["static/index-47310c9f.js","static/el-tooltip-4ed993c7.js","static/index-1613dbfb.js","static/index-59bb6979.js","static/index-87571f09.css","static/index-62001382.js","static/index-19af116e.css","static/el-button-b4242b8c.css","static/el-popper-27830cce.css","static/el-divider-f4d3946e.css","static/el-icon-b1770749.css","static/el-input-bec20aed.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"病例报告",dify_app_uuid:"05544f18-9efb-42e4-ad5d-8ca78feaf56c",desc:"病例报告是详细记录并分析个别患者疾病发生、诊断、治疗及转归过程的医学文献，用于探讨罕见病、特殊临床表现或治疗经验。",icon:V("综述Review.svg"),bg:V("题材和论文章节的写作语料.png")},component:()=>a((()=>import("./index-4569cf66.js")),["static/index-4569cf66.js","static/Editor-b263cd99.js","static/index-59bb6979.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-1613dbfb.js","static/index-62001382.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/reviewSearch",type:"医学写作",meta:{title:"国自然基金写作",dify_app_uuid:"cc0c4388-8c02-4f1c-8a32-e0f3e84146c7",desc:"“国自然基金写作”是撰写国家自然科学基金申请书的过程，该过程需遵循科学基金的撰写要求和规范，涵盖项目名称、关键词、摘要、立项依据、研究目标、内容、方案及创新性等多个方面的撰写，旨在清晰、准确、有逻辑地展示研究项目的科学性、创新性和可行性，以获得基金资助。",icon:V("综述Review.svg"),bg:V("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-4569cf66.js")),["static/index-4569cf66.js","static/Editor-b263cd99.js","static/index-59bb6979.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-1613dbfb.js","static/index-62001382.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"学术写作问答",dify_app_uuid:"c81251e7-9269-413b-95d3-0a4b88ce84b9",desc:"",icon:V("场景写作.svg"),bg:V("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-4569cf66.js")),["static/index-4569cf66.js","static/Editor-b263cd99.js","static/index-59bb6979.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-1613dbfb.js","static/index-62001382.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])},{name:"reviewSearch",path:"/chat",type:"医学会话",meta:{title:"AI产品发布会",dify_app_uuid:"5ec1c9f5-a9a3-4697-bb66-71793e8375a6",desc:"",icon:V("场景写作.svg"),bg:V("基于AI的写作文本加工.png")},component:()=>a((()=>import("./index-4569cf66.js")),["static/index-4569cf66.js","static/Editor-b263cd99.js","static/index-59bb6979.js","static/index-87571f09.css","static/Editor-5aa31017.css","static/el-button-b4242b8c.css","static/el-input-bec20aed.css","static/el-select-afa21100.css","static/el-popper-27830cce.css","static/el-icon-b1770749.css","static/index-1613dbfb.js","static/index-62001382.js","static/index-224cdab4.css","static/el-checkbox-eb7158df.css","static/el-switch-4d96cdcc.css","static/el-message-5edaa45a.css"])}],P=e=>(T("data-v-0b1b43c3"),e=e(),S(),e),R={class:"flex-1 flex flex-col overflow-hidden"},D={class:"text-gray-500 mb-2"},k=["href"],C=P((()=>g("span",{class:"line"},"/",-1))),N=P((()=>g("span",{class:"line"},"/",-1))),$={class:"flex items-center my-2"},z=["src"],U={class:"text-xl font-bold text-dark-200 mr-4"},F={class:"flex-1 overflow-hidden"},H={class:"flex items-center my-2"},J=["src"],q={class:"text-xl font-bold text-dark-200"},K={class:"text-dark-500 mb-6"},Z={class:"flex justify-center"},B=t({__name:"Layout",setup(a){var t;const T=i(),S=n([]),V=n(null),P=n(null),B=n(!1),G=n(0),M=n(!1),{locale:Q}=s(),W=navigator.browserLanguage||navigator.language,X=(null==(t=T.params)?void 0:t.lang)||W,Y=n();Q.value=X;const ee=()=>{B.value=!0},ae=e=>{M.value=e};l((()=>T),(()=>{P.value=JSON.parse(sessionStorage.getItem("nodeInfo")),V.value.setCurrentKey(node.name)}),{deep:!0});const te=n({appUuid:T.params.appUuid});return o((async()=>{Y.value="https://ai.medon.com.cn",await void A(te.value).then((e=>{P.value=e[0]})).catch((e=>{})),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(M.value=!0),O.map((e=>(e.label=e.meta.title,{label:e.type,children:O.filter((a=>a.type==e.type))}))).forEach((e=>{S.value.find((a=>a.type===e.label))||S.value.push(e)})),sessionStorage.getItem("nodeInfo")?P.value=JSON.parse(sessionStorage.getItem("nodeInfo")):P.value=S.value[0].children[0],p((()=>{})),G.value=document.body.clientHeight-56})),(a,t)=>{var i,n,s,l,o,p;const A=e,T=E,S=c("router-view"),V=j;return d(),r("div",null,[m(A,{onIsZHChange:ae}),u(M)?(d(),_(L,{key:0})):v("",!0),g("div",{class:"h-full flex p-6",style:w({height:`${u(G)}px`})},[g("main",R,[g("div",null,[g("div",D,[g("a",{class:"cursor-pointer hover:text-[#5298FF]",href:u(Y)},f(a.$t("tool.home")),9,k),C,h(f((null==(i=u(P))?void 0:i.appType)?a.$t(`${u(b)[null==(n=u(P))?void 0:n.appType]}`):""),1),N,h(f(null==(s=u(P))?void 0:s.appName),1)]),g("div",$,[g("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(l=u(P))?void 0:l.appIcon)?null==(o=u(P))?void 0:o.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,z),g("div",U,f(null==(p=u(P))?void 0:p.appName),1),m(T,{plain:"",size:"small",onClick:ee},{default:x((()=>[h(f(a.$t("tool.intro")),1)])),_:1})])]),g("div",F,[m(S,null,{default:x((({Component:e})=>[(d(),_(y(e)))])),_:1})])]),m(V,{modelValue:u(B),"onUpdate:modelValue":t[1]||(t[1]=e=>I(B)?B.value=e:null),"show-close":!1,width:"500"},{header:x((()=>{var e,a,t;return[g("div",H,[g("img",{class:"w-[38px] h-[38px] mr-4",src:(null==(e=u(P))?void 0:e.appIcon)?null==(a=u(P))?void 0:a.appIcon:"https://img.medsci.cn/web/prod/img/user_icon.png",alt:"这是icon"},null,8,J),g("div",q,f(null==(t=u(P))?void 0:t.appName),1)])]})),default:x((()=>{var e;return[g("div",K,f(null==(e=u(P))?void 0:e.appDescription),1),g("div",Z,[m(T,{onClick:t[0]||(t[0]=e=>B.value=!1)},{default:x((()=>[h("我知道了")])),_:1})])]})),_:1},8,["modelValue"])],4)])}}},[["__scopeId","data-v-0b1b43c3"]]);export{B as default};
