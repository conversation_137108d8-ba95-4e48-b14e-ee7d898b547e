/* empty css                  *//* empty css                     *//* empty css                 */import{a as e,b as l,u as o,Y as a,r as s,N as t,x as i,o as n,aF as c,d as r,e as u,j as d,t as m,f as p,m as g,k as v,aG as _,a8 as f,aL as b,aM as I,B as k,aI as h,E as B,aJ as y,aK as x}from"./index-fb45107a.js";const w={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},T={class:"cl-rootBox cl-signUp-root justify-center"},$={class:"cl-cardBox cl-signUp-start"},S={class:"cl-card cl-signUp-start"},U={class:"cl-header"},C={class:"cl-headerTitle"},j={class:"cl-headerSubtitle"},V={class:"cl-main"},N={class:"cl-socialButtonsRoot"},q={class:"cl-socialButtons"},A={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},z={class:"cl-socialButtonsBlockButton-d"},L={class:"cl-socialButtons"},R={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},J={class:"cl-socialButtonsBlockButton-d"},O={class:"cl-socialButtonsRoot"},P={class:"cl-internal-1pnppin"},G={class:"cl-internal-742eeh"},F={class:"cl-internal-2iusy0"},M={class:"cl-footer 🔒️ cl-internal-4x6jej"},W={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},Z={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},E=e({__name:"index",setup(e){var E;const{t:H}=l(),K=o(),Q=a(),Y=K.params.socialType,D=K.query.authCode,X=K.query.authState,ee=s(),le=s({email:"",password:"",emailCode:"",userName:""}),oe=s((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn"))),ae=t({userName:[{required:!0,message:H("tool.username_cannot_be_empty"),trigger:"blur"}],password:[{required:!0,message:H("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:H("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:H("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],emailCode:[{required:!0,message:H("tool.verification_code_cannot_be_empty"),trigger:"blur"},{min:6,max:6,message:H("tool.verification_code_must_be_6_digits"),trigger:"blur"}],email:[{required:!0,message:H("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:H("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),se=s(!1),te=s(60),ie=s(H("tool.send_verification_code")),ne=i.get("userInfo")?null==(E=JSON.parse(i.get("userInfo")))?void 0:E.userId:"",ce=e=>{_(e).then((e=>{window.location.href=e}))},re=()=>{if(!le.value.email)return void f.error(H("tool.email_does_not_exist"));let e={email:le.value.email,type:"RegisterCode"};b(e).then((e=>{e&&(()=>{se.value=!0,ie.value=`${te.value}${H("tool.retry_after_seconds")}`;let e=setInterval((()=>{te.value-=1,ie.value=`${te.value}${H("tool.retry_after_seconds")}`,te.value<=0&&(clearInterval(e),te.value=60,ie.value=H("tool.send_verification_code"),se.value=!1)}),1e3)})()}))};return n((()=>{ne?Q.push("/"):Y&&D&&X&&c(Y,D,X).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?i.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?i.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):i.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),Q.push("/"))}))})),(e,l)=>{const o=k,a=h,s=B,t=y,i=x;return r(),u("div",w,[d("div",T,[d("div",$,[d("div",S,[d("div",U,[d("div",null,[d("h1",C,m(e.$t("tool.create_account")),1),d("p",j,m(e.$t("tool.registration_greeting")),1)])]),d("div",V,[d("div",N,[d("div",q,[d("button",A,[d("span",z,[l[7]||(l[7]=d("span",null,[d("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),d("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[0]||(l[0]=e=>ce(35))},m(e.$t("tool.continue_with_google")),1)])])]),d("div",L,[d("button",R,[d("span",J,[l[8]||(l[8]=d("span",null,[d("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),d("span",{class:"cl-socialButtonsBlockButtonText",onClick:l[1]||(l[1]=e=>ce(36))},m(e.$t("tool.continue_with_facebook")),1)])])])]),l[11]||(l[11]=d("div",{class:"cl-dividerRow"},[d("div",{class:"cl-dividerLine"}),d("p",{class:"cl-dividerText"},"or"),d("div",{class:"cl-dividerLine"})],-1)),d("div",O,[p(t,{ref_key:"ruleFormRef",ref:ee,style:{"max-width":"600px"},model:le.value,rules:ae,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:g((()=>[p(a,{label:e.$t("tool.username"),prop:"userName"},{default:g((()=>[p(o,{modelValue:le.value.userName,"onUpdate:modelValue":l[2]||(l[2]=e=>le.value.userName=e)},null,8,["modelValue"])])),_:1},8,["label"]),p(a,{label:e.$t("tool.email"),prop:"email"},{default:g((()=>[p(o,{modelValue:le.value.email,"onUpdate:modelValue":l[3]||(l[3]=e=>le.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),p(a,{label:e.$t("tool.verification_code"),prop:"emailCode"},{default:g((()=>[p(o,{modelValue:le.value.emailCode,"onUpdate:modelValue":l[4]||(l[4]=e=>le.value.emailCode=e)},{append:g((()=>[p(s,{onClick:re,disabled:se.value,type:"primary"},{default:g((()=>[v(m(ie.value),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])),_:1},8,["label"]),p(a,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:g((()=>[p(o,{modelValue:le.value.password,"onUpdate:modelValue":l[5]||(l[5]=e=>le.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),p(a,null,{default:g((()=>[d("div",P,[l[10]||(l[10]=d("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1)),d("div",G,[p(s,{class:"cl-formButtonPrimary cl-button 🔒️ cl-internal-ttumny",onClick:l[6]||(l[6]=e=>(async e=>{e&&await e.validate(((e,l)=>{e&&I(le.value).then((e=>{e&&(f({type:"success",message:H("tool.registersuccess")}),setTimeout((()=>{location.href=oe.value?"/apps/login":"login"}),1e3))}))}))})(ee.value))},{default:g((()=>[d("span",F,[v(m(e.$t("tool.continue")),1),l[9]||(l[9]=d("svg",{class:"cl-buttonArrowIcon 🔒️ cl-internal-1c4ikgf"},[d("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),d("div",M,[d("div",W,[d("span",Z,m(e.$t("tool.alreadyhaveanaccount")),1),p(i,{href:oe.value?"/apps/login":"/login",class:"cl-footerActionLink"},{default:g((()=>[v(m(e.$t("tool.signIn")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-49de275f"]]);export{E as default};
