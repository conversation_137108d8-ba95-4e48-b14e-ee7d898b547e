import{aB as e,J as n,an as t,P as o,r as a,O as r,o as s,n as i,Q as u,L as l,q as c,w as d,S as f,g as p,aD as v,N as h,f as m}from"./index-dad9166e.js";function g(){}const y=Object.assign,w="undefined"!=typeof window,b=e=>null!==e&&"object"==typeof e,x=e=>null!=e,C=e=>"function"==typeof e,$=e=>"number"==typeof e||/^\d+(\.\d+)?$/.test(e);function T(e,n){const t=n.split(".");let o=e;return t.forEach((e=>{var n;o=b(o)&&null!=(n=o[e])?n:""})),o}function O(e,n,t){return n.reduce(((n,o)=>(t&&void 0===e[o]||(n[o]=e[o]),n)),{})}const k=null,E=[Number,String],A={type:Boolean,default:!0},S=e=>({type:e,required:!0}),L=e=>({type:Number,default:e}),z=e=>({type:E,default:e}),Y=e=>({type:String,default:e});var j="undefined"!=typeof window;function N(e){return j?requestAnimationFrame(e):-1}function B(e){j&&cancelAnimationFrame(e)}function P(e){N((()=>N(e)))}var X=(e,n)=>({top:0,left:0,right:e,bottom:n,width:e,height:n}),M=e=>{const n=p(e);if(n===window){const e=n.innerWidth,t=n.innerHeight;return X(e,t)}return(null==n?void 0:n.getBoundingClientRect)?n.getBoundingClientRect():X(0,0)};function D(r){const s=e(r,null);if(s){const e=n(),{link:a,unlink:r,internalChildren:i}=s;a(e),t((()=>r(e)));return{parent:s,index:o((()=>i.indexOf(e)))}}return{parent:null,index:a(-1)}}var F,H,V=(e,n)=>{const t=e.indexOf(n);return-1===t?e.findIndex((e=>void 0!==n.key&&null!==n.key&&e.type===n.type&&e.key===n.key)):t};function W(e,n,t){const o=function(e){const n=[],t=e=>{Array.isArray(e)&&e.forEach((e=>{var o;v(e)&&(n.push(e),(null==(o=e.component)?void 0:o.subTree)&&(n.push(e.component.subTree),t(e.component.subTree.children)),e.children&&t(e.children))}))};return t(e),n}(e.subTree.children);t.sort(((e,n)=>V(o,e.vnode)-V(o,n.vnode)));const a=t.map((e=>e.proxy));n.sort(((e,n)=>a.indexOf(e)-a.indexOf(n)))}function q(e){const t=r([]),o=r([]),a=n();return{children:t,linkChildren:n=>{f(e,Object.assign({link:e=>{e.proxy&&(o.push(e),t.push(e.proxy),W(a,t,o))},unlink:e=>{const n=o.indexOf(e);t.splice(n,1),o.splice(n,1)},children:t,internalChildren:o},n))}}}function I(e){let n;s((()=>{e(),i((()=>{n=!0}))})),u((()=>{n&&e()}))}function R(e,n,o={}){if(!j)return;const{target:a=window,passive:r=!1,capture:s=!1}=o;let i,u=!1;const f=t=>{if(u)return;const o=p(t);o&&!i&&(o.addEventListener(e,n,{capture:s,passive:r}),i=!0)},v=t=>{if(u)return;const o=p(t);o&&i&&(o.removeEventListener(e,n,s),i=!1)};let h;return t((()=>v(a))),l((()=>v(a))),I((()=>f(a))),c(a)&&(h=d(a,((e,n)=>{v(n),f(e)}))),()=>{null==h||h(),v(a),u=!0}}var Z,J=/scroll|auto|overlay/i,Q=j?window:void 0;function U(e){return"HTML"!==e.tagName&&"BODY"!==e.tagName&&1===e.nodeType}function _(e,n=Q){let t=e;for(;t&&t!==n&&U(t);){const{overflowY:e}=window.getComputedStyle(t);if(J.test(e))return t;t=t.parentNode}return n}function G(e,n=Q){const t=a();return s((()=>{e.value&&(t.value=_(e.value,n))})),t}function K(){if(!Z&&(Z=a("visible"),j)){const e=()=>{Z.value=document.hidden?"hidden":"visible"};e(),window.addEventListener("visibilitychange",e)}return Z}var ee=Symbol("van-field");function ne(n){const t=e(ee,null);t&&!t.customValue.value&&(t.customValue.value=n,d(n,(()=>{t.resetValidation(),t.validateWithTrigger("onChange")})))}function te(e){const n="scrollTop"in e?e.scrollTop:e.pageYOffset;return Math.max(n,0)}function oe(e,n){"scrollTop"in e?e.scrollTop=n:e.scrollTo(e.scrollX,n)}function ae(e){oe(window,e),oe(document.body,e)}function re(e,n){if(e===window)return 0;const t=n?te(n):window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;return M(e).top+t}w&&/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase());function se(e,n){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault(),n&&(e=>{e.stopPropagation()})(e)}function ie(e){const n=p(e);if(!n)return!1;const t=window.getComputedStyle(n),o="none"===t.display,a=null===n.offsetParent&&"fixed"!==t.position;return o||a}const{width:ue,height:le}=function(){if(!F&&(F=a(0),H=a(0),j)){const e=()=>{F.value=window.innerWidth,H.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:F,height:H}}();function ce(e){if(x(e))return $(e)?`${e}px`:String(e)}function de(e){if(x(e)){if(Array.isArray(e))return{width:ce(e[0]),height:ce(e[1])};const n=ce(e);return{width:n,height:n}}}function fe(e){const n={};return void 0!==e&&(n.zIndex=+e),n}let pe;function ve(e){return+(e=e.replace(/rem/g,""))*function(){if(!pe){const e=document.documentElement,n=e.style.fontSize||window.getComputedStyle(e).fontSize;pe=parseFloat(n)}return pe}()}function he(e){if("number"==typeof e)return e;if(w){if(e.includes("rem"))return ve(e);if(e.includes("vw"))return function(e){return+(e=e.replace(/vw/g,""))*ue.value/100}(e);if(e.includes("vh"))return function(e){return+(e=e.replace(/vh/g,""))*le.value/100}(e)}return parseFloat(e)}const me=/-(\w)/g,ge=e=>e.replace(me,((e,n)=>n.toUpperCase())),ye=(e,n,t)=>Math.min(Math.max(e,n),t),{hasOwnProperty:we}=Object.prototype;function be(e,n){return Object.keys(n).forEach((t=>{!function(e,n,t){const o=n[t];x(o)&&(we.call(e,t)&&b(o)?e[t]=be(Object(e[t]),o):e[t]=o)}(e,n,t)})),e}const xe=a("zh-CN"),Ce=r({"zh-CN":{name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,n)=>`${e}年${n}月`,rangePrompt:e=>`最多选择 ${e} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>`${e}折`,condition:e=>`满${e}元可用`},vanCouponCell:{title:"优惠券",count:e=>`${e}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}}});var $e={messages:()=>Ce[xe.value],use(e,n){xe.value=e,this.add({[e]:n})},add(e={}){be(Ce,e)}};function Te(e){const n=ge(e)+".";return(e,...t)=>{const o=$e.messages(),a=T(o,n+e)||T(o,e);return C(a)?a(...t):a}}function Oe(e,n){return n?"string"==typeof n?` ${e}--${n}`:Array.isArray(n)?n.reduce(((n,t)=>n+Oe(e,t)),""):Object.keys(n).reduce(((t,o)=>t+(n[o]?Oe(e,o):"")),""):""}function ke(e){return(n,t)=>(n&&"string"!=typeof n&&(t=n,n=""),`${n=n?`${e}__${n}`:e}${Oe(n,t)}`)}function Ee(e){const n=`van-${e}`;return[n,ke(n),Te(n)]}const Ae="van-hairline",Se=`${Ae}--top`,Le=`${Ae}--left`,ze=`${Ae}--surround`,Ye=`${Ae}--top-bottom`,je="van-haptics-feedback";function Ne(e,{args:n=[],done:t,canceled:o,error:a}){if(e){const s=e.apply(null,n);b(r=s)&&C(r.then)&&C(r.catch)?s.then((e=>{e?t():o&&o()})).catch(a||g):s?t():o&&o()}else t();var r}function Be(e){return e.install=n=>{const{name:t}=e;t&&(n.component(t,e),n.component(ge(`-${t}`),e))},e}const Pe=Symbol();function Xe(n){const t=e(Pe,null);t&&d(t,(e=>{e&&n()}))}function Me(e){const t=n();t&&y(t.proxy,e)}const[De,Fe]=Ee("badge");const He=Be(h({name:De,props:{dot:Boolean,max:E,tag:Y("div"),color:String,offset:Array,content:E,showZero:A,position:Y("top-right")},setup(e,{slots:n}){const t=()=>{if(n.content)return!0;const{content:t,showZero:o}=e;return x(t)&&""!==t&&(o||0!==t&&"0"!==t)},a=()=>{const{dot:o,max:a,content:r}=e;if(!o&&t())return n.content?n.content():x(a)&&$(r)&&+r>+a?`${a}+`:r},r=e=>e.startsWith("-")?e.replace("-",""):`-${e}`,s=o((()=>{const t={background:e.color};if(e.offset){const[o,a]=e.offset,{position:s}=e,[i,u]=s.split("-");n.default?(t[i]="number"==typeof a?ce("top"===i?a:-a):"top"===i?ce(a):r(a),t[u]="number"==typeof o?ce("left"===u?o:-o):"left"===u?ce(o):r(o)):(t.marginTop=ce(a),t.marginLeft=ce(o))}return t})),i=()=>{if(t()||e.dot)return m("div",{class:Fe([e.position,{dot:e.dot,fixed:!!n.default}]),style:s.value},[a()])};return()=>{if(n.default){const{tag:t}=e;return m(t,{class:Fe("wrapper")},{default:()=>[n.default(),i()]})}return i()}}}));function Ve(){const e=a(0),n=a(0),t=a(0),o=a(0),r=a(0),s=a(0),i=a(""),u=a(!0),l=()=>{t.value=0,o.value=0,r.value=0,s.value=0,i.value="",u.value=!0};return{move:a=>{const l=a.touches[0];t.value=(l.clientX<0?0:l.clientX)-e.value,o.value=l.clientY-n.value,r.value=Math.abs(t.value),s.value=Math.abs(o.value);var c,d;(!i.value||r.value<10&&s.value<10)&&(i.value=(c=r.value,d=s.value,c>d?"horizontal":d>c?"vertical":"")),u.value&&(r.value>5||s.value>5)&&(u.value=!1)},start:t=>{l(),e.value=t.touches[0].clientX,n.value=t.touches[0].clientY},reset:l,startX:e,startY:n,deltaX:t,deltaY:o,offsetX:r,offsetY:s,direction:i,isVertical:()=>"vertical"===i.value,isHorizontal:()=>"horizontal"===i.value,isTap:u}}export{P as A,se as B,ye as C,S as D,ce as E,x as F,Ye as G,ae as H,re as I,Ne as J,He as K,D as L,k as M,O as N,ze as O,b as P,g as Q,C as R,Le as S,Se as T,ne as U,de as V,_ as W,Pe as X,je as Y,L as Z,Ee as a,z as b,B as c,he as d,y as e,fe as f,te as g,R as h,w as i,ie as j,M as k,le as l,Y as m,E as n,I as o,Be as p,Ve as q,N as r,oe as s,A as t,G as u,q as v,ue as w,Me as x,K as y,Xe as z};
