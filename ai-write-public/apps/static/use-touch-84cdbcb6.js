import{ax as e,I as n,aj as t,O as o,r as a,N as r,o as s,n as i,P as u,K as l,p as c,w as d,R as f,f as p,az as v,M as h,e as m}from"./index-0ee7a7ea.js";function g(){}const y=Object.assign,w="undefined"!=typeof window,b=e=>null!==e&&"object"==typeof e,x=e=>null!=e,C=e=>"function"==typeof e,$=e=>"number"==typeof e||/^\d+(\.\d+)?$/.test(e);function T(e,n){const t=n.split(".");let o=e;return t.forEach((e=>{var n;o=b(o)&&null!=(n=o[e])?n:""})),o}function O(e,n,t){return n.reduce(((n,o)=>(t&&void 0===e[o]||(n[o]=e[o]),n)),{})}const k=null,E=[Number,String],A={type:Boolean,default:!0},S=e=>({type:e,required:!0}),z=e=>({type:Number,default:e}),L=e=>({type:E,default:e}),j=e=>({type:String,default:e});var Y="undefined"!=typeof window;function N(e){return Y?requestAnimationFrame(e):-1}function M(e){Y&&cancelAnimationFrame(e)}function P(e){N((()=>N(e)))}var X=(e,n)=>({top:0,left:0,right:e,bottom:n,width:e,height:n}),B=e=>{const n=p(e);if(n===window){const e=n.innerWidth,t=n.innerHeight;return X(e,t)}return(null==n?void 0:n.getBoundingClientRect)?n.getBoundingClientRect():X(0,0)};function F(r){const s=e(r,null);if(s){const e=n(),{link:a,unlink:r,internalChildren:i}=s;a(e),t((()=>r(e)));return{parent:s,index:o((()=>i.indexOf(e)))}}return{parent:null,index:a(-1)}}var H,I,R=(e,n)=>{const t=e.indexOf(n);return-1===t?e.findIndex((e=>void 0!==n.key&&null!==n.key&&e.type===n.type&&e.key===n.key)):t};function V(e,n,t){const o=function(e){const n=[],t=e=>{Array.isArray(e)&&e.forEach((e=>{var o;v(e)&&(n.push(e),(null==(o=e.component)?void 0:o.subTree)&&(n.push(e.component.subTree),t(e.component.subTree.children)),e.children&&t(e.children))}))};return t(e),n}(e.subTree.children);t.sort(((e,n)=>R(o,e.vnode)-R(o,n.vnode)));const a=t.map((e=>e.proxy));n.sort(((e,n)=>a.indexOf(e)-a.indexOf(n)))}function W(e){const t=r([]),o=r([]),a=n();return{children:t,linkChildren:n=>{f(e,Object.assign({link:e=>{e.proxy&&(o.push(e),t.push(e.proxy),V(a,t,o))},unlink:e=>{const n=o.indexOf(e);t.splice(n,1),o.splice(n,1)},children:t,internalChildren:o},n))}}}function D(e){let n;s((()=>{e(),i((()=>{n=!0}))})),u((()=>{n&&e()}))}function q(e,n,o={}){if(!Y)return;const{target:a=window,passive:r=!1,capture:s=!1}=o;let i,u=!1;const f=t=>{if(u)return;const o=p(t);o&&!i&&(o.addEventListener(e,n,{capture:s,passive:r}),i=!0)},v=t=>{if(u)return;const o=p(t);o&&i&&(o.removeEventListener(e,n,s),i=!1)};let h;return t((()=>v(a))),l((()=>v(a))),D((()=>f(a))),c(a)&&(h=d(a,((e,n)=>{v(n),f(e)}))),()=>{null==h||h(),v(a),u=!0}}var Z,K=/scroll|auto|overlay/i,U=Y?window:void 0;function _(e){return"HTML"!==e.tagName&&"BODY"!==e.tagName&&1===e.nodeType}function G(e,n=U){let t=e;for(;t&&t!==n&&_(t);){const{overflowY:e}=window.getComputedStyle(t);if(K.test(e))return t;t=t.parentNode}return n}function J(e,n=U){const t=a();return s((()=>{e.value&&(t.value=G(e.value,n))})),t}function Q(){if(!Z&&(Z=a("visible"),Y)){const e=()=>{Z.value=document.hidden?"hidden":"visible"};e(),window.addEventListener("visibilitychange",e)}return Z}var ee=Symbol("van-field");function ne(n){const t=e(ee,null);t&&!t.customValue.value&&(t.customValue.value=n,d(n,(()=>{t.resetValidation(),t.validateWithTrigger("onChange")})))}function te(e){const n="scrollTop"in e?e.scrollTop:e.pageYOffset;return Math.max(n,0)}function oe(e,n){"scrollTop"in e?e.scrollTop=n:e.scrollTo(e.scrollX,n)}function ae(e){oe(window,e),oe(document.body,e)}function re(e,n){if(e===window)return 0;const t=n?te(n):window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;return B(e).top+t}w&&/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase());function se(e,n){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault(),n&&(e=>{e.stopPropagation()})(e)}function ie(e){const n=p(e);if(!n)return!1;const t=window.getComputedStyle(n),o="none"===t.display,a=null===n.offsetParent&&"fixed"!==t.position;return o||a}const{width:ue,height:le}=function(){if(!H&&(H=a(0),I=a(0),Y)){const e=()=>{H.value=window.innerWidth,I.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:H,height:I}}();function ce(e){if(x(e))return $(e)?`${e}px`:String(e)}function de(e){if(x(e)){if(Array.isArray(e))return{width:ce(e[0]),height:ce(e[1])};const n=ce(e);return{width:n,height:n}}}function fe(e){const n={};return void 0!==e&&(n.zIndex=+e),n}let pe;function ve(e){return+(e=e.replace(/rem/g,""))*function(){if(!pe){const e=document.documentElement,n=e.style.fontSize||window.getComputedStyle(e).fontSize;pe=parseFloat(n)}return pe}()}function he(e){if("number"==typeof e)return e;if(w){if(e.includes("rem"))return ve(e);if(e.includes("vw"))return function(e){return+(e=e.replace(/vw/g,""))*ue.value/100}(e);if(e.includes("vh"))return function(e){return+(e=e.replace(/vh/g,""))*le.value/100}(e)}return parseFloat(e)}const me=/-(\w)/g,ge=e=>e.replace(me,((e,n)=>n.toUpperCase())),ye=(e,n,t)=>Math.min(Math.max(e,n),t),{hasOwnProperty:we}=Object.prototype;function be(e,n){return Object.keys(n).forEach((t=>{!function(e,n,t){const o=n[t];x(o)&&(we.call(e,t)&&b(o)?e[t]=be(Object(e[t]),o):e[t]=o)}(e,n,t)})),e}const xe=a("zh-CN"),Ce=r({"zh-CN":{name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,n)=>`${e}年${n}月`,rangePrompt:e=>`最多选择 ${e} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>`${e}折`,condition:e=>`满${e}元可用`},vanCouponCell:{title:"优惠券",count:e=>`${e}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}}});var $e={messages:()=>Ce[xe.value],use(e,n){xe.value=e,this.add({[e]:n})},add(e={}){be(Ce,e)}};function Te(e){const n=ge(e)+".";return(e,...t)=>{const o=$e.messages(),a=T(o,n+e)||T(o,e);return C(a)?a(...t):a}}function Oe(e,n){return n?"string"==typeof n?` ${e}--${n}`:Array.isArray(n)?n.reduce(((n,t)=>n+Oe(e,t)),""):Object.keys(n).reduce(((t,o)=>t+(n[o]?Oe(e,o):"")),""):""}function ke(e){return(n,t)=>(n&&"string"!=typeof n&&(t=n,n=""),`${n=n?`${e}__${n}`:e}${Oe(n,t)}`)}function Ee(e){const n=`van-${e}`;return[n,ke(n),Te(n)]}const Ae="van-hairline",Se=`${Ae}--top`,ze=`${Ae}--left`,Le=`${Ae}--surround`,je=`${Ae}--top-bottom`,Ye="van-haptics-feedback";function Ne(e,{args:n=[],done:t,canceled:o,error:a}){if(e){const s=e.apply(null,n);b(r=s)&&C(r.then)&&C(r.catch)?s.then((e=>{e?t():o&&o()})).catch(a||g):s?t():o&&o()}else t();var r}function Me(e){return e.install=n=>{const{name:t}=e;t&&(n.component(t,e),n.component(ge(`-${t}`),e))},e}const Pe=Symbol();function Xe(n){const t=e(Pe,null);t&&d(t,(e=>{e&&n()}))}function Be(e){const t=n();t&&y(t.proxy,e)}const[Fe,He]=Ee("badge");const Ie=Me(h({name:Fe,props:{dot:Boolean,max:E,tag:j("div"),color:String,offset:Array,content:E,showZero:A,position:j("top-right")},setup(e,{slots:n}){const t=()=>{if(n.content)return!0;const{content:t,showZero:o}=e;return x(t)&&""!==t&&(o||0!==t&&"0"!==t)},a=()=>{const{dot:o,max:a,content:r}=e;if(!o&&t())return n.content?n.content():x(a)&&$(r)&&+r>+a?`${a}+`:r},r=e=>e.startsWith("-")?e.replace("-",""):`-${e}`,s=o((()=>{const t={background:e.color};if(e.offset){const[o,a]=e.offset,{position:s}=e,[i,u]=s.split("-");n.default?(t[i]="number"==typeof a?ce("top"===i?a:-a):"top"===i?ce(a):r(a),t[u]="number"==typeof o?ce("left"===u?o:-o):"left"===u?ce(o):r(o)):(t.marginTop=ce(a),t.marginLeft=ce(o))}return t})),i=()=>{if(t()||e.dot)return m("div",{class:He([e.position,{dot:e.dot,fixed:!!n.default}]),style:s.value},[a()])};return()=>{if(n.default){const{tag:t}=e;return m(t,{class:He("wrapper")},{default:()=>[n.default(),i()]})}return i()}}}));function Re(){const e=a(0),n=a(0),t=a(0),o=a(0),r=a(0),s=a(0),i=a(""),u=a(!0),l=()=>{t.value=0,o.value=0,r.value=0,s.value=0,i.value="",u.value=!0};return{move:a=>{const l=a.touches[0];t.value=(l.clientX<0?0:l.clientX)-e.value,o.value=l.clientY-n.value,r.value=Math.abs(t.value),s.value=Math.abs(o.value);var c,d;(!i.value||r.value<10&&s.value<10)&&(i.value=(c=r.value,d=s.value,c>d?"horizontal":d>c?"vertical":"")),u.value&&(r.value>5||s.value>5)&&(u.value=!1)},start:t=>{l(),e.value=t.touches[0].clientX,n.value=t.touches[0].clientY},reset:l,startX:e,startY:n,deltaX:t,deltaY:o,offsetX:r,offsetY:s,direction:i,isVertical:()=>"vertical"===i.value,isHorizontal:()=>"horizontal"===i.value,isTap:u}}export{P as A,se as B,ye as C,S as D,ce as E,x as F,je as G,ae as H,re as I,Ne as J,Ie as K,F as L,k as M,O as N,Le as O,b as P,g as Q,C as R,ze as S,Se as T,ne as U,de as V,G as W,Pe as X,Ye as Y,z as Z,Ee as a,L as b,M as c,he as d,y as e,fe as f,te as g,q as h,w as i,ie as j,B as k,le as l,j as m,E as n,D as o,Me as p,Re as q,N as r,oe as s,A as t,J as u,W as v,ue as w,Be as x,Q as y,Xe as z};
