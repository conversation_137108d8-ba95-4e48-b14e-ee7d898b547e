/* empty css                  */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                   *//* empty css                *//* empty css                 */import{m as e}from"./index-c4e7b377.js";import{a as l,r as t,aM as s,d as a,e as r,j as i,f as o,g as d,q as c,a0 as n,F as p,z as u,t as x,h as v,T as m,m as f,aN as b,aR as g,i as h,a4 as y,C as j,H as w,aQ as k,aS as _,ag as T,ah as z,aT as A}from"./index-2502211c.js";import{s as C}from"./index-5d3c44df.js";/* empty css                   */const H=e=>(T("data-v-0ac38a84"),e=e(),z(),e),M={class:"p-4 h-full"},V={class:"bg-white box h-full flex"},q={class:"w-[50%] flex flex-col"},L=H((()=>i("div",{class:"text-[16px] px-6 font-bold text-gray-600 h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"}," 输入摘要 ",-1))),B={class:"flex justify-end mb-4 mr-4"},F={class:"flex-1 flex flex-col h-full overflow-hidden"},I=H((()=>i("div",{class:"text-[16px] px-6 font-bold text-gray-600 min-h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"}," 生成5个Title ",-1))),N={class:"border-r flex-1 p-4 flex flex-col overflow-hidden"},Q={class:"flex items-center mb-4"},R=["onClick"],S={key:1,class:"py-4 flex-1 overflow-auto"},U={class:"mb-4"},Z={class:"mb-2 relative"},$={class:"text-[#0071bc]"},D={class:"text-gray-500 text-[12px]"},E={class:"text-gray-500 text-[12px] flex items-center mb-4"},G=H((()=>i("span",null,"参考文献（显示5条）",-1))),J={class:"flex"},K={class:"mr-2"},O={class:"w-[22px] h-[22px] flex items-center justify-center px-1 bg-[#4d73be] text-white rounded-md"},P={class:"mb-2 relative"},W=["innerHTML"],X={class:"text-gray-500 text-[12px]"},Y=l({__name:"index",setup(l){const T=t(""),z=t(null),H=t(1),Y=t([]),ee=t(null),le=e=>{z.value=e,H.value=1,te()},te=()=>{if(!T.value)return y.warning("请输入摘要");e("recommend-title",{text:T.value,mode:z.value}).then((e=>{e&&e.data&&(Y.value=e.data,Y.value=C(Y.value),ee.value=Y.value[0],ee.value.recArticles=C(ee.value.recArticles))}))};return(e,l)=>{const t=j,y=A,C=w,te=k,se=_,ae=s("copy");return a(),r("div",M,[i("div",V,[i("div",q,[L,o(t,{class:"p-6 flex-1",modelValue:d(T),"onUpdate:modelValue":l[0]||(l[0]=e=>c(T)?T.value=e:null),type:"textarea",placeholder:"","show-word-limit":"",resize:"none"},null,8,["modelValue"]),i("div",B,[i("div",{class:n(["text-white font-bold cursor-pointer rounded-md px-4 py-1 mr-4","classics"==d(z)?"bg-[#499557]":"bg-gray-400"]),onClick:l[1]||(l[1]=e=>le("classics"))}," 经典 ",2),i("div",{class:n(["text-white font-bold cursor-pointer rounded-md px-4 py-1","light"==d(z)?"bg-[#499557]":"bg-gray-400"]),onClick:l[2]||(l[2]=e=>le("light"))}," 眼前一亮 ",2)])]),i("div",F,[I,i("div",N,[i("div",Q,[(a(!0),r(p,null,u(d(Y),((e,l)=>(a(),r("div",{class:n(["cursor-pointer px-4 text-white mr-4",l+1==d(H)?"bg-[#4d73be]":"bg-gray-400"]),key:l,onClick:t=>((e,l)=>{H.value=l+1,ee.value=e})(e,l)},x(`Title${l+1}`),11,R)))),128))]),d(ee)?(a(),r("div",S,[i("div",U,[i("div",Z,[i("span",$,x(d(ee).title),1),m((a(),v(C,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-4 absolute top-1"},{default:f((()=>[o(d(b))])),_:1})),[[ae,d(ee).title]])]),i("div",D,x(d(ee).titleZh),1),o(te)]),i("div",null,[i("div",E,[G,o(se,{effect:"dark",content:"以上Title由系统自动参照海量高分文献的主题内容生成，此处随机显示5条。",placement:"top"},{default:f((()=>[o(C,{color:"#999",size:"16",class:"cursor-pointer"},{default:f((()=>[o(d(g))])),_:1})])),_:1})]),(a(!0),r(p,null,u(d(ee).recArticles,((e,l)=>(a(),r("div",{key:l},[i("div",J,[i("div",K,[i("div",O,x(l+1),1)]),i("div",null,[i("div",P,[i("span",{innerHTML:e.title},null,8,W),m((a(),v(C,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-4 absolute top-1"},{default:f((()=>[o(d(b))])),_:2},1024)),[[ae,e.title.replace(/<[^>]+>/g,"")]])]),i("div",X,x(e.zh_title),1)])]),l+1<d(ee).recArticles.length?(a(),v(te,{key:0})):h("",!0)])))),128))])])):(a(),v(y,{key:0,description:"暂无数据"}))])])])])}}},[["__scopeId","data-v-0ac38a84"]]);export{Y as default};
