import{M as e,b as a,u as n,o as i,a6 as s,aD as t,d as o,e as l}from"./index-4b0c90f0.js";const c=e({__name:"index",setup(e){const{t:c}=a(),r=n(),p=JSON.parse(decodeURIComponent(r.params.payInfo));return i((async()=>{const e=navigator.userAgent;let a=(new Date).getTime();if((null==p?void 0:p.time)&&a>p.time&&location.replace(location.origin),null!=e)if(e.includes("MicroMessenger"))s.warning(c("tool.pleasescanwithalipay"));else if(e.includes("AlipayClient")){p.time=a;const e=await t(p);location.replace(e)}})),(e,a)=>(o(),l("div"))}});export{c as default};
