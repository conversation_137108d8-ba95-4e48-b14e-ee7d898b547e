/* empty css                  *//* empty css                */import{r as e,y as a,u as t,w as l,d as i,e as o,j as n,t as s,g as r,h as u,m as d,F as c,z as v,f as p,A as f,k as m,i as h,B as g,C as y,D as b,G as w,H as x,E as _,I as k,J as I,K as S,L as C,M as T,N as $,O as B,P as j,n as z,o as N,Q as q,R as A,S as O,T as V,U as E,V as R,a as M,b as U,W as L,X as H,Y as W,Z as D,$ as J,c as P,a0 as F,a1 as X,q as Z,a2 as Y,a3 as G,a4 as K,a5 as Q,a6 as ee,a7 as ae,a8 as te,a9 as le,aa as ie}from"./index-10a1c2d6.js";import{g as oe}from"./index-9caef982.js";/* empty css                 *//* empty css                  *//* empty css                  */import{c as ne,r as se,g as re,s as ue,i as de,o as ce,a as ve,n as pe,m as fe,b as me,u as he,d as ge,e as ye,f as be,h as we,j as xe,k as _e,w as ke,l as Ie,p as Se,t as Ce,q as Te,v as $e,x as Be,y as je,z as ze,A as Ne,B as qe,C as Ae,D as Oe,E as Ve,F as Ee,G as Re,H as Me,I as Ue,J as Le,K as He,L as We,M as De,N as Je}from"./use-touch-c699c4b7.js";import{r as Pe,a as Fe,f as Xe}from"./use-route-b463846a.js";const Ze={class:"p-3 flex-1 rounded-md"},Ye={class:"text-[14px] font-bold mb-2 text-gray-600"},Ge={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(I,{expose:S,emit:C}){const T=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),$=t(),B=e([]),j=e({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),z=e(""),N=I,q=N.type,A=N.fileVerify,O=N.label,V=N.required,E=N.max_length,R=N.options;"file"==q&&(z.value=null),"file-list"==q&&(z.value=[]);const M={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},U=()=>{let e="";return A.forEach(((a,t)=>{t<A.length-1?e+=M[a].join(",")+",":e+=M[a].join(",")})),e},L=C,H=(e,a,t)=>{},W=()=>{z.value=""},D=async e=>{const{file:a,onSuccess:t,onError:l}=e,i=new FormData;i.append("file",a),i.append("appId",$.params.uuid),i.append("user",T.value.userName);try{const e=await g(i);"file-list"==q?z.value.push({type:j.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):z.value={type:j.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(o){l(o)}return!1};R&&R.length>0&&(z.value=R[0]);return S({updateMessage:()=>{R&&R.length>0?z.value=R[0]:"file"==q?(z.value=null,B.value=[]):"file-list"==q?(z.value=[],B.value=[]):z.value=""}}),l(z,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=y,l=b,g=w,I=x,S=_,C=k;return i(),o("div",Ze,[n("div",Ye,s(r(O)),1),"paragraph"===r(q)||"text-input"===r(q)?(i(),u(t,{key:0,modelValue:z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>z.value=e),type:"paragraph"===r(q)?"textarea":"text",rows:5,required:r(V),placeholder:`${r(O)}`,"show-word-limit":"",resize:"none",maxlength:r(E)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===r(q)?(i(),u(t,{key:1,modelValue:z.value,"onUpdate:modelValue":a[1]||(a[1]=e=>z.value=e),modelModifiers:{number:!0},type:"number",required:r(V),placeholder:`${r(O)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===r(q)?(i(),u(g,{key:2,modelValue:z.value,"onUpdate:modelValue":a[2]||(a[2]=e=>z.value=e),required:r(V),placeholder:`${r(O)}`},{default:d((()=>[(i(!0),o(c,null,v(r(R),(e=>(i(),u(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===r(q)||"file-list"===r(q)?(i(),u(C,{key:3,"file-list":B.value,"onUpdate:fileList":a[3]||(a[3]=e=>B.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":W,"before-remove":e.beforeRemove,limit:r(E),accept:U(),"auto-upload":!0,"on-Success":H,"http-request":D,"on-exceed":e.handleExceed},{default:d((()=>[p(S,{disabled:B.value.length==r(E)},{default:d((()=>[p(I,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:d((()=>[p(r(f))])),_:1}),m("从本地上传")])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):h("",!0)])}}},Ke=Array.isArray,Qe=e=>"string"==typeof e,ea=e=>null!==e&&"object"==typeof e,aa=/\B([A-Z])/g,ta=(e=>{const a=Object.create(null);return t=>a[t]||(a[t]=e(t))})((e=>e.replace(aa,"-$1").toLowerCase()));function la(e){if(Ke(e)){const a={};for(let t=0;t<e.length;t++){const l=e[t],i=Qe(l)?sa(l):la(l);if(i)for(const e in i)a[e]=i[e]}return a}if(Qe(e)||ea(e))return e}const ia=/;(?![^(]*\))/g,oa=/:([^]+)/,na=/\/\*[^]*?\*\//g;function sa(e){const a={};return e.replace(na,"").split(ia).forEach((e=>{if(e){const t=e.split(oa);t.length>1&&(a[t[0].trim()]=t[1].trim())}})),a}function ra(e){let a="";if(Qe(e))a=e;else if(Ke(e))for(let t=0;t<e.length;t++){const l=ra(e[t]);l&&(a+=l+" ")}else if(ea(e))for(const t in e)e[t]&&(a+=t+" ");return a.trim()}let ua=0;function da(){const e=I(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++ua}`}function ca(e,a){if(!de||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};C(l),T(l),ce((()=>{e.value&&t.observe(e.value)}))}const[va,pa]=ve("sticky");const fa=Se($({name:va,props:{zIndex:pe,position:fe("top"),container:Object,offsetTop:me(0),offsetBottom:me(0)},emits:["scroll","change"],setup(a,{emit:t,slots:i}){const o=e(),n=he(o),s=B({fixed:!1,width:0,height:0,transform:0}),r=e(!1),u=j((()=>ge("top"===a.position?a.offsetTop:a.offsetBottom))),d=j((()=>{if(r.value)return;const{fixed:e,height:a,width:t}=s;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=j((()=>{if(!s.fixed||r.value)return;const e=ye(be(a.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[a.position]:`${u.value}px`});return s.transform&&(e.transform=`translate3d(0, ${s.transform}px, 0)`),e})),v=()=>{if(!o.value||xe(o))return;const{container:e,position:l}=a,i=_e(o),n=re(window);if(s.width=i.width,s.height=i.height,"top"===l)if(e){const a=_e(e),t=a.bottom-u.value-s.height;s.fixed=u.value>i.top&&a.bottom>0,s.transform=t<0?t:0}else s.fixed=u.value>i.top;else{const{clientHeight:a}=document.documentElement;if(e){const t=_e(e),l=a-t.top-u.value-s.height;s.fixed=a-u.value<i.bottom&&a>t.top,s.transform=l<0?-l:0}else s.fixed=a-u.value<i.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:s.fixed})})(n)};return l((()=>s.fixed),(e=>t("change",e))),we("scroll",v,{target:n,passive:!0}),ca(o,v),l([ke,Ie],(()=>{o.value&&!xe(o)&&s.fixed&&(r.value=!0,z((()=>{const e=_e(o);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return p("div",{ref:o,style:d.value},[p("div",{class:pa({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=i.default)?void 0:e.call(i)])])}}})),[ma,ha]=ve("swipe"),ga={loop:Ce,width:pe,height:pe,vertical:Boolean,autoplay:me(0),duration:me(500),touchable:Ce,lazyRender:Boolean,initialSwipe:me(0),indicatorColor:String,showIndicators:Ce,stopPropagation:Ce},ya=Symbol(ma);const ba=Se($({name:ma,props:ga,emits:["change","dragStart","dragEnd"],setup(a,{emit:t,slots:i}){const o=e(),n=e(),s=B({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=Te(),{children:d,linkChildren:c}=$e(ya),v=j((()=>d.length)),f=j((()=>s[a.vertical?"height":"width"])),m=j((()=>a.vertical?u.deltaY.value:u.deltaX.value)),h=j((()=>{if(s.rect){return(a.vertical?s.rect.height:s.rect.width)-f.value*v.value}return 0})),g=j((()=>f.value?Math.ceil(Math.abs(h.value)/f.value):v.value)),y=j((()=>v.value*f.value)),b=j((()=>(s.active+v.value)%v.value)),w=j((()=>{const e=a.vertical?"vertical":"horizontal";return u.direction.value===e})),x=j((()=>{const e={transitionDuration:`${s.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(f.value){const t=a.vertical?"height":"width",l=a.vertical?"width":"height";e[t]=`${y.value}px`,e[l]=a[l]?`${a[l]}px`:""}return e})),_=(e,t=0)=>{let l=e*f.value;a.loop||(l=Math.min(l,-h.value));let i=t-l;return a.loop||(i=Ae(i,h.value,0)),i},k=({pace:e=0,offset:l=0,emitChange:i})=>{if(v.value<=1)return;const{active:o}=s,n=(e=>{const{active:t}=s;return e?a.loop?Ae(t+e,-1,v.value):Ae(t+e,0,g.value):t})(e),r=_(n,l);if(a.loop){if(d[0]&&r!==h.value){const e=r<h.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=n,s.offset=r,i&&n!==o&&t("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Ne((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let $;const A=()=>clearTimeout($),O=()=>{A(),+a.autoplay>0&&v.value>1&&($=setTimeout((()=>{S(),O()}),+a.autoplay))},V=(e=+a.initialSwipe)=>{if(!o.value)return;const t=()=>{var t,l;if(!xe(o)){const e={width:o.value.offsetWidth,height:o.value.offsetHeight};s.rect=e,s.width=+(null!=(t=a.width)?t:e.width),s.height=+(null!=(l=a.height)?l:e.height)}v.value&&-1===(e=Math.min(v.value-1,e))&&(e=v.value-1),s.active=e,s.swiping=!0,s.offset=_(e),d.forEach((e=>{e.setOffset(0)})),O()};xe(o)?z().then(t):t()},E=()=>V(s.active);let R;const M=e=>{!a.touchable||e.touches.length>1||(u.start(e),r=!1,R=Date.now(),A(),I())},U=()=>{if(!a.touchable||!s.swiping)return;const e=Date.now()-R,l=m.value/e;if((Math.abs(l)>.25||Math.abs(m.value)>f.value/2)&&w.value){const e=a.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=a.loop?e>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/f.value),k({pace:t,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,t("dragEnd",{index:b.value}),O()},L=(e,t)=>{const l=t===b.value,i=l?{backgroundColor:a.indicatorColor}:void 0;return p("i",{style:i,class:ha("indicator",{active:l})},null)};return Be({prev:()=>{I(),u.reset(),Ne((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:E,swipeTo:(e,t={})=>{I(),u.reset(),Ne((()=>{let l;l=a.loop&&e===v.value?0===s.active?0:e:e%v.value,t.immediate?Ne((()=>{s.swiping=!1})):s.swiping=!1,k({pace:l-s.active,emitChange:!0})}))}}),c({size:f,props:a,count:v,activeIndicator:b}),l((()=>a.initialSwipe),(e=>V(+e))),l(v,(()=>V(s.active))),l((()=>a.autoplay),O),l([ke,Ie,()=>a.width,()=>a.height],E),l(je(),(e=>{"visible"===e?O():A()})),N(V),q((()=>V(s.active))),ze((()=>V(s.active))),C(A),T(A),we("touchmove",(e=>{if(a.touchable&&s.swiping&&(u.move(e),w.value)){!a.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(qe(e,a.stopPropagation),k({offset:m.value}),r||(t("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var e;return p("div",{ref:o,class:ha()},[p("div",{ref:n,style:x.value,class:ha("track",{vertical:a.vertical}),onTouchstartPassive:M,onTouchend:U,onTouchcancel:U},[null==(e=i.default)?void 0:e.call(i)]),i.indicator?i.indicator({active:b.value,total:v.value}):a.showIndicators&&v.value>1?p("div",{class:ha("indicators",{vertical:a.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[wa,xa]=ve("tabs");var _a=$({name:wa,props:{count:Oe(Number),inited:Boolean,animated:Boolean,duration:Oe(pe),swipeable:Boolean,lazyRender:Boolean,currentIndex:Oe(Number)},emits:["change"],setup(a,{emit:t,slots:i}){const o=e(),n=e=>t("change",e),s=()=>{var e;const t=null==(e=i.default)?void 0:e.call(i);return a.animated||a.swipeable?p(ba,{ref:o,loop:!1,class:xa("track"),duration:1e3*+a.duration,touchable:a.swipeable,lazyRender:a.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},r=e=>{const t=o.value;t&&t.state.active!==e&&t.swipeTo(e,{immediate:!a.inited})};return l((()=>a.currentIndex),r),N((()=>{r(a.currentIndex)})),Be({swipeRef:o}),()=>p("div",{class:xa("content",{animated:a.animated||a.swipeable})},[s()])}});const[ka,Ia]=ve("tabs"),Sa={type:fe("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:me(0),duration:me(.3),animated:Boolean,ellipsis:Ce,swipeable:Boolean,scrollspy:Boolean,offsetTop:me(0),background:String,lazyRender:Ce,showHeader:Ce,lineWidth:pe,lineHeight:pe,beforeChange:Function,swipeThreshold:me(5),titleActiveColor:String,titleInactiveColor:String},Ca=Symbol(ka);var Ta=$({name:ka,props:Sa,emits:["change","scroll","rendered","clickTab","update:active"],setup(a,{emit:t,slots:i}){let o,n,s,r,u;const d=e(),c=e(),v=e(),f=e(),m=da(),h=he(d),[g,y]=function(){const a=e([]),t=[];return S((()=>{a.value=[]})),[a,e=>(t[e]||(t[e]=t=>{a.value[e]=t}),t[e])]}(),{children:b,linkChildren:w}=$e(Ca),x=B({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=j((()=>b.length>+a.swipeThreshold||!a.ellipsis||a.shrink)),k=j((()=>({borderColor:a.color,background:a.background}))),I=(e,a)=>{var t;return null!=(t=e.name)?t:a},C=j((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),T=j((()=>ge(a.offsetTop))),$=j((()=>a.sticky?T.value+o:0)),N=e=>{const t=c.value,l=g.value;if(!(_.value&&t&&l&&l[x.currentIndex]))return;const i=l[x.currentIndex].$el,o=i.offsetLeft-(t.offsetWidth-i.offsetWidth)/2;r&&r(),r=function(e,a,t){let l,i=0;const o=e.scrollLeft,n=0===t?1:Math.round(1e3*t/16);let s=o;return function t(){s+=(a-o)/n,e.scrollLeft=s,++i<n&&(l=se(t))}(),function(){ne(l)}}(t,o,e?0:+a.duration)},A=()=>{const e=x.inited;z((()=>{const t=g.value;if(!t||!t[x.currentIndex]||"line"!==a.type||xe(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:i,lineHeight:o}=a,n=l.offsetLeft+l.offsetWidth/2,s={width:Ve(i),backgroundColor:a.color,transform:`translateX(${n}px) translateX(-50%)`};if(e&&(s.transitionDuration=`${a.duration}s`),Ee(o)){const e=Ve(o);s.height=e,s.borderRadius=e}x.lineStyle=s}))},O=(e,l)=>{const i=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(e);if(!Ee(i))return;const o=b[i],n=I(o,i),r=null!==x.currentIndex;x.currentIndex!==i&&(x.currentIndex=i,l||N(),A()),n!==a.active&&(t("update:active",n),r&&t("change",n,o.title)),s&&!a.scrollspy&&Me(Math.ceil(Ue(d.value)-T.value))},V=(e,a)=>{const t=b.find(((a,t)=>I(a,t)===e)),l=t?b.indexOf(t):0;O(l,a)},E=(e=!1)=>{if(a.scrollspy){const t=b[x.currentIndex].$el;if(t&&h.value){const l=Ue(t,h.value)-$.value;n=!0,u&&u(),u=function(e,a,t,l){let i,o=re(e);const n=o<a,s=0===t?1:Math.round(1e3*t/16),r=(a-o)/s;return function t(){o+=r,(n&&o>a||!n&&o<a)&&(o=a),ue(e,o),n&&o<a||!n&&o>a?i=se(t):l&&(i=se(l))}(),function(){ne(i)}}(h.value,l,e?0:+a.duration,(()=>{n=!1}))}}},R=(e,l,i)=>{const{title:o,disabled:n}=b[l],s=I(b[l],l);n||(Le(a.beforeChange,{args:[s],done:()=>{O(l),E()}}),Pe(e)),t("clickTab",{name:s,title:o,event:i,disabled:n})},M=e=>{s=e.isFixed,t("scroll",e)},U=()=>{if("line"===a.type&&b.length)return p("div",{class:Ia("line"),style:x.lineStyle},null)},L=()=>{var e,t,l;const{type:o,border:n,sticky:s}=a,r=[p("div",{ref:s?void 0:v,class:[Ia("wrap"),{[Re]:"line"===o&&n}]},[p("div",{ref:c,role:"tablist",class:Ia("nav",[o,{shrink:a.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(e=i["nav-left"])?void 0:e.call(i),b.map((e=>e.renderTitle(R))),U(),null==(t=i["nav-right"])?void 0:t.call(i)])]),null==(l=i["nav-bottom"])?void 0:l.call(i)];return s?p("div",{ref:v},[r]):r},H=()=>{A(),z((()=>{var e,a;N(!0),null==(a=null==(e=f.value)?void 0:e.swipeRef.value)||a.resize()}))};l((()=>[a.color,a.duration,a.lineWidth,a.lineHeight]),A),l(ke,H),l((()=>a.active),(e=>{e!==C.value&&V(e)})),l((()=>b.length),(()=>{x.inited&&(V(a.active),A(),z((()=>{N(!0)})))}));return Be({resize:H,scrollTo:e=>{z((()=>{V(e),E(!0)}))}}),q(A),ze(A),ce((()=>{V(a.active,!0),z((()=>{x.inited=!0,v.value&&(o=_e(v.value).height),N(!0)}))})),ca(d,A),we("scroll",(()=>{if(a.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=_e(b[e].$el);if(a>$.value)return 0===e?0:e-1}return b.length-1})();O(e)}}),{target:h,passive:!0}),w({id:m,props:a,setLine:A,scrollable:_,onRendered:(e,a)=>t("rendered",e,a),currentName:C,setTitleRefs:y,scrollIntoView:N}),()=>p("div",{ref:d,class:Ia([a.type])},[a.showHeader?a.sticky?p(fa,{container:d.value,offsetTop:T.value,onScroll:M},{default:()=>[L()]}):L():null,p(_a,{ref:f,count:b.length,inited:x.inited,animated:a.animated,duration:a.duration,swipeable:a.swipeable,lazyRender:a.lazyRender,currentIndex:x.currentIndex,onChange:O},{default:()=>{var e;return[null==(e=i.default)?void 0:e.call(i)]}})])}});const $a=Symbol(),[Ba,ja]=ve("tab"),za=$({name:Ba,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:pe,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:Ce},setup(e,{slots:a}){const t=j((()=>{const a={},{type:t,color:l,disabled:i,isActive:o,activeColor:n,inactiveColor:s}=e;l&&"card"===t&&(a.borderColor=l,i||(o?a.backgroundColor=l:a.color=l));const r=o?n:s;return r&&(a.color=r),a})),l=()=>{const t=p("span",{class:ja("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Ee(e.badge)&&""!==e.badge?p(He,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>p("div",{id:e.id,role:"tab",class:[ja([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[Na,qa]=ve("swipe-item");const Aa=Se($({name:Na,setup(e,{slots:a}){let t;const l=B({offset:0,inited:!1,mounted:!1}),{parent:i,index:o}=We(ya);if(!i)return;const n=j((()=>{const e={},{vertical:a}=i.props;return i.size.value&&(e[a?"height":"width"]=`${i.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),s=j((()=>{const{loop:e,lazyRender:a}=i.props;if(!a||t)return!0;if(!l.mounted)return!1;const n=i.activeIndicator.value,s=i.count.value-1,r=0===n&&e?s:n-1,u=n===s&&e?0:n+1;return t=o.value===n||o.value===r||o.value===u,t}));return N((()=>{z((()=>{l.mounted=!0}))})),Be({setOffset:e=>{l.offset=e}}),()=>{var e;return p("div",{class:qa(),style:n.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[Oa,Va]=ve("tab");const Ea=Se($({name:Oa,props:ye({},Fe,{dot:Boolean,name:pe,badge:pe,title:String,disabled:Boolean,titleClass:De,titleStyle:[String,Object],showZeroBadge:Ce}),setup(a,{slots:t}){const i=da(),o=e(!1),n=I(),{parent:s,index:r}=We(Ca);if(!s)return;const u=()=>{var e;return null!=(e=a.name)?e:r.value},d=j((()=>{const e=u()===s.currentName.value;return e&&!o.value&&(o.value=!0,s.props.lazyRender&&z((()=>{s.onRendered(u(),a.title)}))),e})),c=e(""),v=e("");A((()=>{const{titleClass:e,titleStyle:t}=a;c.value=e?ra(e):"",v.value=t&&"string"!=typeof t?function(e){let a="";if(!e||Qe(e))return a;for(const t in e){const l=e[t];(Qe(l)||"number"==typeof l)&&(a+=`${t.startsWith("--")?t:ta(t)}:${l};`)}return a}(la(t)):t}));const f=e(!d.value);return l(d,(e=>{e?f.value=!1:Ne((()=>{f.value=!0}))})),l((()=>a.title),(()=>{s.setLine(),s.scrollIntoView()})),O($a,d),Be({id:i,renderTitle:e=>p(za,R({key:i,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:i,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:a=>e(n.proxy,r.value,a)},Je(s.props,["type","color","shrink"]),Je(a,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title})}),()=>{var e;const a=`${s.id}-${r.value}`,{animated:l,swipeable:n,scrollspy:u,lazyRender:c}=s.props;if(!t.default&&!l)return;const v=u||d.value;if(l||n)return p(Aa,{id:i,role:"tabpanel",class:Va("panel-wrapper",{inactive:f.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":a,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[p("div",{class:Va("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const m=o.value||u||!c?null==(e=t.default)?void 0:e.call(t):null;return V(p("div",{id:i,role:"tabpanel",class:Va("panel"),tabindex:v?0:-1,"aria-labelledby":a,"data-allow-mismatch":"attribute"},[m]),[[E,v]])}}})),Ra=Se(Ta),Ma={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ua={class:"pc_container",style:{display:"flex"}},La={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ha={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Wa={class:"pc_right bg-[#fff]"},Da={id:"typing-area"},Ja={key:0,class:"decContaniner nop bg-[#fff]"},Pa={class:"icon"},Fa={class:"process_text label_width"},Xa={key:0,class:"process"},Za={key:0,class:"img_box"},Ya=["src"],Ga={key:1,class:"icon"},Ka={class:"process"},Qa={class:"process_text"},et=["src"],at=["src"],tt={class:"mobile_container"},lt={class:"p-3",style:{display:"flex","justify-content":"space-between"}},it={class:"mobile_right"},ot={id:"typing-area"},nt={key:0,class:"decContaniner nop bg-[#fff]"},st={class:"icon"},rt={class:"process_text label_width"},ut=(e=>(le("data-v-9e17da1b"),e=e(),ie(),e))((()=>n("div",null,[n("div",{class:"process"})],-1))),dt={key:0,class:"img_box"},ct=["src"],vt={key:1,class:"icon"},pt={class:"process"},ft={class:"process_text"},mt=M({__name:"index",setup(f){const g=oe("loading.png"),y=oe("copy.png"),b=B({}),w=t(),k={},I=e([]),S=e(a.get("userInfo")?JSON.parse(a.get("userInfo")):{}),C=e(null),T=e(null),{locale:$}=U(),z=L(),q=e(!1);let A=e("a"),O=e("");const V=e(""),E=e(null),R=e(null),M=e(["1","2"]);let le;const ie=async()=>{var e;await Y({appId:w.params.uuid,user:S.value.userName,mode:null==(e=C.value)?void 0:e.mode,task_id:V.value}),setTimeout((()=>{Ie.abort(),Se=!0,he.value=[],je()}),0)};N((async()=>{var e,t;const l=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${l}px`);if(["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=w.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=w.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),$.value=H(),a.get("userInfo"))Ve(),fe();else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=W();e&&"zh-CN"!=e?z.push("/login"):window.addLoginDom()}})),l(O,(()=>{O.value&&(A.value="b")}));const ne=()=>{w.params.uuid&&G({appId:w.params.uuid,user:S.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(I.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;k[t]={label:e[a].label},b[t]=""})))}))},se=j((()=>!!I.value.length)),re=j((()=>I.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),ue=()=>{w.params.uuid&&K({appId:w.params.uuid,user:S.value.userName}).then((e=>{C.value={...e}}))},de=e(!1),ce=e(!1),ve=e(!1),pe=e(!1),fe=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=S.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:w.params.uuid,userUuid:null==(a=S.value)?void 0:a.openid}];await D.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},me=()=>{var e,a;if(0!=re.value.length||(t=b,Object.values(t).some((e=>e)))){var t;for(let e in b)if(re.value.includes(e)&&!b[e])return void Q({message:`${k[e].label}为必填项！`,type:"error"});(null==(e=C.value)?void 0:e.mode)&&(["advanced-chat","chat"].includes(null==(a=C.value)?void 0:a.mode)?Q({type:"success",message:"计划中，敬请期待..."}):"completion"==C.value.mode?Be():$e())}else Q({message:"请输入您的问题。",type:"error"})},he=e([]),ge=e([]),ye=e(""),be=e(0),we=e(""),xe=e(!1),_e=e(!1),ke=e(!1);let Ie,Se=!1,Ce=!1;const Te=()=>{be.value<ye.value.length?(xe.value=!0,we.value+=ye.value.charAt(be.value),be.value++,setTimeout(Te,100)):(ke.value=!1,xe.value=!1,ve.value=!0,je())},$e=async()=>{O.value="",ge.value=[],we.value="",ye.value="",he.value=[],_e.value=!1,Se=!1,be.value=0,Ie=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run`;0,de.value=!0,ce.value=!0,pe.value=!1,await Xe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:S.value.userName,inputs:{...b,outputLanguage:b.outputLanguage?b.outputLanguage:"中文"==ee()?"简体中文":ee()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){var a,t,l,i,o,n;if(e.data.trim())try{const s=JSON.parse(e.data);if(V.value=s.task_id,s.error)throw new Error(s.error);"智能体推理思维链"==(null==(a=null==s?void 0:s.data)?void 0:a.title)&&"node_finished"===s.event&&(ke.value=!0,ye.value=JSON.parse(s.data.outputs.text).text,Te()),"node_started"!==s.event||_e.value||"开始"==(null==(t=null==s?void 0:s.data)?void 0:t.title)||(ge.value.push({node_id:null==(l=null==s?void 0:s.data)?void 0:l.node_id,title:null==(i=null==s?void 0:s.data)?void 0:i.title,status:!1}),setTimeout((()=>{T.value.scrollTop=T.value.scrollHeight}),10)),"node_finished"===s.event&&ge.value.forEach((e=>{var a;e.node_id==(null==(a=null==s?void 0:s.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===s.event&&(q.value=!0,_e.value=!0,he.value.push(null==(o=null==s?void 0:s.data)?void 0:o.text),ke.value||je()),"workflow_started"===s.event&&(de.value=!1),"workflow_finished"===s.event&&(_e.value=!0,q.value||(he.value.push(null==(n=null==s?void 0:s.data)?void 0:n.outputs.text),ke.value||je()),Se=!0,A.value="b")}catch(s){qe(s)}},onerror(e){qe(e)},signal:Ie.signal,openWhenHidden:!0})}catch(e){qe()}},Be=async()=>{O.value="",he.value=[],ke.value=!1,Se=!1,Ie=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages`;0,de.value=!0,ce.value=!0,pe.value=!1,await Xe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:w.params.uuid,user:S.value.userName,inputs:{...b,outputLanguage:ee()},files:[],response_mode:"streaming",appUuid:w.params.appUuid}),onmessage(e){if(de.value=!1,ve.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(V.value=a.task_id,a.error)throw new Error(a.error);"message"===a.event&&(he.value.push(null==a?void 0:a.answer),ke.value||je()),"message_end"===a.event&&(A.value="b",Se=!0)}catch(a){qe(a)}},onerror(e){qe(e)},signal:Ie.signal,openWhenHidden:!0})}catch(e){qe()}},je=()=>{if(0===he.value.length)return ke.value=!1,Ce=!0,void ze();ke.value=!0;const e=he.value.shift();Ne(e).then((()=>{je()}))},ze=()=>{Ce&&Se&&(ce.value=!1,ve.value=!1,pe.value=!0)},Ne=e=>new Promise((a=>{let t=0;le=setInterval((()=>{if(t<e.length){O.value+=e[t++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const l=document.getElementsByClassName("mobile_right");l[0].scrollTop=l[0].scrollHeight}else clearInterval(le),a()}),15)})),qe=()=>{setTimeout((()=>{Ie.abort()}),0),de.value=!1,ve.value=!1,ce.value=!1,ke.value=!1,Q.error("访问太火爆了！休息下，请稍后再试！"),O.value="访问太火爆了！休息下，请稍后再试！"},Ae=async()=>{try{await navigator.clipboard.writeText(O.value),Q({type:"success",message:"复制成功"})}catch(e){Q(e)}},Oe=()=>{for(let e in b)b[e]="";E.value.forEach((e=>{e.updateMessage()})),R.value.forEach((e=>{e.updateMessage()}))},Ve=()=>{if(localStorage.getItem("yudaoToken"))return ne(),void ue();const e=a.get("userInfo");if(e){const a=JSON.parse(e);try{J({userId:a.userId,userName:a.userName,realName:a.realName,avatar:a.avatar,plaintextUserId:a.plaintextUserId,mobile:a.mobile,email:a.email}).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),ne(),ue())}))}catch(t){}}};return(e,a)=>{const t=_,l=x,f=ae,w=te,k=P("v-md-preview");return i(),o("div",Ma,[n("div",Ua,[r(se)?(i(),o(c,{key:0},[n("div",La,[(i(!0),o(c,null,v(r(I),((a,t)=>(i(),o("div",{class:"flex",key:t},[(i(!0),o(c,null,v(a,((a,t)=>(i(),u(Ge,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:E},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",Ha,[p(t,{onClick:Oe},{default:d((()=>[m("Clear")])),_:1}),p(t,{onClick:me,loading:r(ce),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])]),n("div",Wa,[n("div",Da,[r(ge).length>0||r(we)?(i(),o("div",Ja,[p(w,{modelValue:r(M),"onUpdate:modelValue":a[0]||(a[0]=e=>Z(M)?M.value=e:null)},{default:d((()=>[p(f,{name:"1"},{title:d((()=>[n("div",Pa,[p(l,null,{default:d((()=>[p(r(F))])),_:1})]),m("执行进度 ")])),default:d((()=>[(i(!0),o(c,null,v(r(ge),((e,a)=>(i(),o("div",{key:a,class:"process"},[n("div",Fa,s(e.title),1),m("    "),n("span",{style:{color:"#36B15E"},class:X(e.status?"":"loading-text")},s(e.status?"已完成":"加载中"),3)])))),128))])),_:1}),n("div",null,[r(we)?(i(),o("div",Xa)):h("",!0)]),r(we)?(i(),u(f,{key:0,title:"推导过程",name:"2"},{title:d((()=>[r(xe)?(i(),o("div",Za,[n("img",{src:r(g),alt:"loading"},null,8,Ya)])):(i(),o("div",Ga,[p(l,null,{default:d((()=>[p(r(F))])),_:1})])),m("推导过程 ")])),default:d((()=>[n("div",Ka,[n("div",Qa,s(r(we)),1)])])),_:1})):h("",!0)])),_:1},8,["modelValue"])])):h("",!0),r(O)?(i(),u(k,{key:1,text:r(O),id:"previewMd"},null,8,["text"])):h("",!0),n("div",null,[r(ve)?(i(),o("img",{key:0,src:r(g),alt:"loading",class:"spinner"},null,8,et)):h("",!0),r(ve)?(i(),o("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:ie},s(e.$t("tool.stopGeneration")),1)):h("",!0),r(pe)?(i(),o("img",{key:2,onClick:Ae,src:r(y),alt:"",style:{width:"20px"},class:"copy"},null,8,at)):h("",!0)])])])],64)):h("",!0)]),n("div",tt,[p(r(Ra),{active:r(A),shrink:"","line-width":"20"},{default:d((()=>[p(r(Ea),{title:"输入",name:"a"},{default:d((()=>[(i(!0),o(c,null,v(r(I),((a,t)=>(i(),o("div",{class:"flex",key:t},[(i(!0),o(c,null,v(a,((a,t)=>(i(),u(Ge,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:r(b)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>r(b)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:R},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),n("div",lt,[p(t,{onClick:Oe},{default:d((()=>[m("Clear")])),_:1}),p(t,{onClick:a[1]||(a[1]=e=>me()),loading:r(ce),type:"primary"},{default:d((()=>[m("Execute")])),_:1},8,["loading"])])])),_:1}),p(r(Ea),{title:"结果",name:"b"},{default:d((()=>[n("div",it,[n("div",ot,[r(ge).length>0||r(we)?(i(),o("div",nt,[p(w,{modelValue:r(M),"onUpdate:modelValue":a[2]||(a[2]=e=>Z(M)?M.value=e:null)},{default:d((()=>[p(f,{name:"1"},{title:d((()=>[n("div",st,[p(l,null,{default:d((()=>[p(r(F))])),_:1})]),m("执行进度 ")])),default:d((()=>[(i(!0),o(c,null,v(r(ge),((e,a)=>(i(),o("div",{key:a,class:"process"},[n("div",rt,s(e.title),1),m("    "),n("span",{style:{color:"#36B15E"},class:X(e.status?"":"loading-text")},s(e.status?"已完成":"加载中"),3)])))),128))])),_:1}),ut,r(we)?(i(),u(f,{key:0,title:"推导过程",name:"2"},{title:d((()=>[r(xe)?(i(),o("div",dt,[n("img",{src:r(g),alt:"loading"},null,8,ct)])):(i(),o("div",vt,[p(l,null,{default:d((()=>[p(r(F))])),_:1})])),m("推导过程 ")])),default:d((()=>[n("div",pt,[n("div",ft,s(r(we)),1)])])),_:1})):h("",!0)])),_:1},8,["modelValue"])])):h("",!0),r(O)?(i(),u(k,{key:1,text:r(O),id:"previewMd"},null,8,["text"])):h("",!0)])])])),_:1})])),_:1},8,["active"])])])}}},[["__scopeId","data-v-9e17da1b"]]);export{mt as default};
